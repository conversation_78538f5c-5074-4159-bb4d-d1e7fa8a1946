{"metadata": {"title": "ดูหนัง <PERSON><PERSON>n (2012) คนโคตรระห่ำ เต็มเรื่อง 24-HD.COM", "ogTitle": "<PERSON><PERSON><PERSON> (2012) คนโคตรระห่ำ", "poster": "https://www.24-hd.com/wp-content/uploads/2023/09/Stolen-2012-คนโคตรระห่ำ.jpg", "movieTitle": "<PERSON><PERSON><PERSON> (2012) คนโคตรระห่ำ", "imdbRating": "<img width=\"64\" height=\"30\"alt=\"Stolen (2012) คนโคตรระห่ำ on IMDb\" src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAWCAMAAACITl7FAAAArlBMVEVHcEzUuEDKqi3pyk3MqCTguCjoxD7JpSD6+a7/+pL/9Ij+7H3/6Xf/5mz34W/+4F/i4Kfr3pP032jz3Fny2WDv1Fnw1VDW0pTuz0fszFDZzH3qyUjrykDfyGTKx47pxTnow0DowTLnvyvlvCbkuSHjtx6/t3nMuEK/sGexr4TJsDbFpiipoWbBnBqglFuIgVZ9eFJ5cEZoZUhZVz1NSzVAPSoxLyEeHRQNDQgAAABX2pHVAAAACHRSTlMAGiUmyM7Q0qyE1EQAAAGpSURBVHgBBcHRjhRVEADQU9V3GFyXdY0JJGwE44OJD/z/j5j4umggBggKJDPTPX2rPCfE8jsFGiYAFggk/Dk7cvlFd3eZ7AoTYCENS4aI4LHi+KpV1+wym6ZgH0gR5FgyRARv482lu2o9q+PaOnI2oiGW4Sq/G0tGiHAd6FoPz9W7h8H2989P2D4+YP/63/zhZdaX9TBSwKA7zs+/x93C/tf9wv7hGdy//OP2GZ8vbSRBwrXQGvE00JoSh9++0PZ126s1iQJA3gH4ujKOOB9v1y2qMXSrBuA+ABTg1xHzn9NNNwkAdByiAQAxMsZP5yskAOhyMBsA6Mcr0QoJAEyhAACA0iQAsDcTAAA0SABg0nsAAACQACC21htAAlYIggEZAJfKugDuglqJ14nIxCBSIgRKIwRJfz7iBuc8EIZoh+O2dLtkf3t6c/p2ij1O0J/+/fEU9elFbB9uO4N4c6mecVqrn2xtmR1KREHEYrJU5O0hMuIax1fdVbPLTlNghyERDDkyIr2NXF53d5l2ChMAC4lhyYiIxwoxHq6KnUbBPvYBicCQHN5f+3+afNlIWcBYzgAAAABJRU5ErkJggg==\">5.5"}, "playerData": {}, "languageOptions": [{"value": "Thai", "text": "Thai", "type": "select"}, {"value": "Sound Track", "text": "Sound Track", "type": "select"}], "m3u8Urls": ["https://main.24playerhd.com/newplaylist/Failed%20to%20connect%20to%20MySQL%20-%20Too%20many%20connections/Failed%20to%20connect%20to%20MySQL%20-%20Too%20many%20connections.m3u8", "https://main.24playerhd.com/newplaylist/Failed%20to%20connect%20to%20MySQL%20-%20Too%20many%20connections/Failed%20to%20connect%20to%20MySQL%20-%20Too%20many%20connections.m3u8", "https://main.24playerhd.com/newplaylist/c0fc6b1719ab5c49cb731118/c0fc6b1719ab5c49cb731118.m3u8"], "ajaxCalls": [{"url": "https://fonts.googleapis.com/css?family=Kanit:300&display=swap", "method": "GET", "headers": {"accept-language": "en-US,en;q=0.9", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "referer": "https://www.24-hd.com/"}}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css", "method": "GET", "headers": {"accept-language": "en-US,en;q=0.9", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "referer": "https://www.24-hd.com/"}}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-solid-900.woff2", "method": "GET", "headers": {"origin": "https://www.24-hd.com", "referer": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css", "accept-language": "en-US,en;q=0.9", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/webfonts/fa-brands-400.woff2", "method": "GET", "headers": {"origin": "https://www.24-hd.com", "referer": "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css", "accept-language": "en-US,en;q=0.9", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}}, {"url": "https://api.24-hd.com/get.php", "method": "POST", "headers": {"referer": "https://www.24-hd.com/", "accept-language": "en-US,en;q=0.9", "accept": "*/*", "content-type": "application/x-www-form-urlencoded; charset=UTF-8", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}, "postData": "action=halim_ajax_player&nonce=&episode=1&server=1&postid=26133&lang=Thai&title="}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/mobile-detect/1.4.5/mobile-detect.min.js", "method": "GET", "headers": {"accept-language": "en-US,en;q=0.9", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "referer": "https://main.24playerhd.com/"}}, {"url": "https://api.24-hd.com/get.php", "method": "POST", "headers": {"referer": "https://www.24-hd.com/", "accept-language": "en-US,en;q=0.9", "accept": "*/*", "content-type": "application/x-www-form-urlencoded; charset=UTF-8", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}, "postData": "action=halim_ajax_player&nonce=&episode=1&postid=26133&lang=Thai&server=1"}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/mobile-detect/1.4.5/mobile-detect.min.js", "method": "GET", "headers": {"accept-language": "en-US,en;q=0.9", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "referer": "https://main.24playerhd.com/"}}, {"url": "https://api.24-hd.com/get.php", "method": "POST", "headers": {"referer": "https://www.24-hd.com/", "accept-language": "en-US,en;q=0.9", "accept": "*/*", "content-type": "application/x-www-form-urlencoded; charset=UTF-8", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"}, "postData": "action=halim_ajax_player&nonce=&episode=1&postid=26133&lang=Sound+Track&server=1"}, {"url": "https://cdnjs.cloudflare.com/ajax/libs/mobile-detect/1.4.5/mobile-detect.min.js", "method": "GET", "headers": {"accept-language": "en-US,en;q=0.9", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "referer": "https://main.24playerhd.com/"}}]}