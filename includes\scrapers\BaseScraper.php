<?php

if (!defined('ABSPATH')) {
    exit;
}

abstract class BaseScraper {
    protected $upload_dir;
    protected $base_url;
    
    protected $language_mappings = [
        'thai' => 'พากย์ไทย',
        'subthai' => 'ซับไทย', 
        'sub' => 'ซับอังกฤษ'
    ];
    
    protected $group_priorities = [
        7 => 1,
        1 => 2,
        0 => 3
    ];
    
    public function __construct() {
        $upload_dir = wp_upload_dir();
        $this->upload_dir = $upload_dir['basedir'] . '/';
        $this->base_url = $upload_dir['baseurl'] . '/';
    }
    
    abstract public function scrape($post_id, $url, $options = []);
    abstract protected function process_content($post_id, $config, $options = []);
    
    protected function fetch_html($url) {
        $this->log("Fetching HTML from: $url");
        
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ));
        
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log("HTTP Request failed for URL: $url - Error: $error_message");
            return false;
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 500) {
            $this->log("HTTP 500 Server Error for URL: $url");
            return 'HTTP_500_ERROR';
        }
        
        if ($response_code === 404) {
            $this->log("HTTP 404 Not Found for URL: $url");
            return 'HTTP_404_ERROR';
        }
        
        if ($response_code !== 200) {
            $this->log("HTTP Request failed for URL: $url - Response Code: $response_code");
            return false;
        }
        
        $body = wp_remote_retrieve_body($response);
        if (empty($body)) {
            $this->log("Empty response body for URL: $url");
            return false;
        }
        
        $this->log("Successfully fetched HTML: " . strlen($body) . " characters");
        return $body;
    }
    
    protected function extract_main_player($html) {
        $this->log("Extracting main player from HTML");
        
        preg_match_all('/<iframe[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $html, $matches);
        if (!isset($matches[1])) {
            $this->log("No iframe sources found");
            return false;
        }
        
        foreach ($matches[1] as $iframe_src) {
            if (strpos($iframe_src, 'youtube.com') !== false || 
                strpos($iframe_src, 'youtu.be') !== false) {
                continue;
            }
            
            $this->log("Found main player: $iframe_src");
            return $iframe_src;
        }
        
        $this->log("No suitable main player found");
        return false;
    }
    
    protected function extract_config($html) {
        $this->log("Extracting configuration from HTML");
        
        $config_patterns = [
            'movieList' => [
                '/movieList\s*=\s*(\{[^;]*\})/s',
                '/var\s+movieList\s*=\s*(\{[^;]*\})/s',
                '/movieList\s*=\s*(\{(?:[^{}]*|{[^{}]*})*\})/s'
            ],
            'seriesList' => [
                '/seriesList\s*=\s*(\{[^;]*\})/s',
                '/var\s+seriesList\s*=\s*(\{[^;]*\})/s',
                '/seriesList\s*=\s*(\{(?:[^{}]*|{[^{}]*})*\})/s'
            ],
            'episodeList' => [
                '/episodeList\s*=\s*(\{[^;]*\})/s',
                '/var\s+episodeList\s*=\s*(\{[^;]*\})/s',
                '/episodeList\s*=\s*(\{(?:[^{}]*|{[^{}]*})*\})/s'
            ],
            'playerConfig' => [
                '/playerConfig\s*=\s*(\{[^;]*\})/s',
                '/var\s+playerConfig\s*=\s*(\{[^;]*\})/s',
                '/playerConfig\s*=\s*(\{(?:[^{}]*|{[^{}]*})*\})/s'
            ]
        ];
        
        foreach ($config_patterns as $type => $patterns) {
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $html, $matches)) {
                    $this->log("Found $type configuration with pattern");
                    
                    $json_str = $matches[1];
                    $config = $this->parse_config_json($json_str);
                    
                    if ($config !== false) {
                        $this->log("Successfully parsed $type configuration");
                        $this->log("Config structure: " . json_encode(array_keys($config), JSON_UNESCAPED_UNICODE));
                        return $config;
                    } else {
                        $this->log("Failed to parse $type JSON, trying next pattern");
                    }
                }
            }
        }
        
        $this->log("No valid configuration found, trying alternative extraction");
        return $this->extract_config_alternative($html);
    }
    
    protected function extract_config_alternative($html) {
        $this->log("Trying alternative config extraction methods");
        
        $alternative_patterns = [
            '/window\.movieList\s*=\s*(\{[^;]*\})/s',
            '/window\.playerConfig\s*=\s*(\{[^;]*\})/s',
            '/var\s+config\s*=\s*(\{[^;]*\})/s',
            '/data\s*:\s*(\{[^}]*"link"[^}]*\})/s',
            '/(\{[^{}]*"link"\s*:\s*\{[^{}]*"thai"[^{}]*\}[^{}]*\})/s'
        ];
        
        foreach ($alternative_patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $this->log("Found alternative config pattern");
                $json_str = $matches[1];
                $config = $this->parse_config_json($json_str);
                
                if ($config !== false && $this->validate_config_structure($config)) {
                    $this->log("Successfully parsed alternative configuration");
                    return $config;
                }
            }
        }
        
        $this->log("Searching for inline data structures");
        if (preg_match_all('/(\{[^{}]*"MU_url"[^{}]*\})/s', $html, $matches)) {
            $this->log("Found " . count($matches[0]) . " potential player objects");
            
            $synthetic_config = [
                'link' => [
                    'thai' => [],
                    'subthai' => [],
                    'sub' => []
                ]
            ];
            
            foreach ($matches[0] as $match) {
                $player_data = $this->parse_config_json($match);
                if ($player_data && isset($player_data['MU_url'])) {
                    $sound = $player_data['MU_sound'] ?? 'thai';
                    if (in_array($sound, ['thai', 'subthai', 'sub'])) {
                        $synthetic_config['link'][$sound][] = $player_data;
                    }
                }
            }
            
            if (!empty($synthetic_config['link']['thai']) || !empty($synthetic_config['link']['subthai']) || !empty($synthetic_config['link']['sub'])) {
                $this->log("Created synthetic config from inline data");
                return $synthetic_config;
            }
        }
        
        $this->log("All config extraction methods failed");
        return false;
    }
    
    protected function validate_config_structure($config) {
        if (!is_array($config)) {
            return false;
        }
        
        if (isset($config['link']) && is_array($config['link'])) {
            foreach (['thai', 'subthai', 'sub'] as $lang) {
                if (isset($config['link'][$lang]) && is_array($config['link'][$lang])) {
                    return true;
                }
            }
        }
        
        return $this->has_nested_language_structure($config);
    }
    
    protected function has_nested_language_structure($data, $depth = 0) {
        if ($depth > 5 || !is_array($data)) {
            return false;
        }
        
        foreach ($data as $key => $value) {
            if ($key === 'link' && is_array($value)) {
                foreach (['thai', 'subthai', 'sub'] as $lang) {
                    if (isset($value[$lang]) && is_array($value[$lang])) {
                        return true;
                    }
                }
            } elseif (is_array($value)) {
                if ($this->has_nested_language_structure($value, $depth + 1)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    protected function parse_config_json($json_str) {
        $json_str = preg_replace('/\/\*[\s\S]*?\*\//', '', $json_str);
        $json_str = preg_replace('/\/\/.*?$/m', '', $json_str);
        
        $config = json_decode($json_str, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $config;
        }
        
        $json_str = preg_replace('/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/', '$1"$2":', $json_str);
        $json_str = preg_replace('/:\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*([,}])/', ':"$1"$2', $json_str);
        
        $config = json_decode($json_str, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            return $config;
        }
        
        return false;
    }
    
    protected function detect_language_players($config) {
        $this->log("Detecting language players from config");
        
        $language_players = [
            'dubbed' => [],
            'subbed' => []
        ];
        
        $this->extract_language_players_recursive($config, $language_players);
        
        foreach ($language_players as $lang => $players) {
            $sorted_players = $this->prioritize_players_by_group($players);
            $language_players[$lang] = $sorted_players;
            $this->log("Found " . count($players) . " $lang players");
        }
        
        return $language_players;
    }
    
    protected function extract_language_players_recursive($data, &$language_players) {
        if (!is_array($data)) {
            return;
        }
        
        foreach ($data as $key => $value) {
            if ($key === 'link' && is_array($value)) {
                foreach (['thai', 'subthai', 'sub'] as $original_lang) {
                    if (isset($value[$original_lang]) && is_array($value[$original_lang])) {
                        $target_lang = ($original_lang === 'thai') ? 'dubbed' : 'subbed';
                        foreach ($value[$original_lang] as $player) {
                            if (isset($player['MU_url']) && !empty($player['MU_url'])) {
                                $language_players[$target_lang][] = [
                                    'url' => $player['MU_url'],
                                    'group' => $player['MU_group'] ?? 0,
                                    'type' => $player['MU_type'] ?? 'embed',
                                    'sound' => $player['MU_sound'] ?? $original_lang,
                                    'id' => $player['MU_id'] ?? null,
                                    'original_lang' => $original_lang
                                ];
                            }
                        }
                    }
                }
            } elseif (in_array($key, ['thai', 'subthai', 'sub']) && is_array($value)) {
                $target_lang = ($key === 'thai') ? 'dubbed' : 'subbed';
                foreach ($value as $player) {
                    if (isset($player['MU_url']) && !empty($player['MU_url'])) {
                        $language_players[$target_lang][] = [
                            'url' => $player['MU_url'],
                            'group' => $player['MU_group'] ?? 0,
                            'type' => $player['MU_type'] ?? 'embed',
                            'sound' => $player['MU_sound'] ?? $key,
                            'id' => $player['MU_id'] ?? null,
                            'original_lang' => $key
                        ];
                    }
                }
            } elseif (is_array($value)) {
                $this->extract_language_players_recursive($value, $language_players);
            }
        }
    }
    
    protected function prioritize_players_by_group($players) {
        if (empty($players)) {
            return [];
        }
        
        usort($players, function($a, $b) {
            $priority_a = $this->group_priorities[$a['group']] ?? 999;
            $priority_b = $this->group_priorities[$b['group']] ?? 999;
            return $priority_a <=> $priority_b;
        });
        
        return $players;
    }
    
    protected function extract_m3u8_files($player_url) {
        $this->log("Extracting M3U8 files from: $player_url");
        
        $html = $this->fetch_html($player_url);
        
        if (!$html || $html === 'HTTP_500_ERROR' || $html === 'HTTP_404_ERROR') {
            $this->log("Failed to fetch player HTML or HTTP error");
            return $html;
        }
        
        $patterns = [
            '/file:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/source:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/src:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/url:\s*["\']([^"\']+\.m3u8[^"\']*)["\']/',
            '/"([^"]*\.m3u8[^"]*)"/',
            '/' . "'([^']*\.m3u8[^']*)'/"
        ];
        
        $master_url = null;
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $master_url = $matches[1];
                $this->log("Found M3U8 URL: $master_url");
                break;
            }
        }
        
        if (!$master_url) {
            $master_url = $this->try_fembed_fallback($player_url);
            if (!$master_url) {
                $master_url = $this->try_zplays_fallback($player_url);
                if (!$master_url) {
                    $this->log("No M3U8 source found");
                    return false;
                }
            }
        }
        
        $master_content = $this->fetch_html($master_url);
        
        if (!$master_content || $master_content === 'HTTP_500_ERROR' || $master_content === 'HTTP_404_ERROR') {
            $this->log("Failed to fetch master M3U8 content");
            return $master_content;
        }
        
        $files = [
            'master_original' => [
                'url' => $master_url,
                'content' => $master_content
            ]
        ];
        
        $this->extract_resolution_files($master_content, $master_url, $files);
        
        $this->log("Successfully extracted M3U8 files: " . count($files) . " files");
        return $files;
    }
    
    protected function extract_resolution_files($master_content, $master_url, &$files) {
        $this->log("Extracting resolution files from master M3U8");
        
        $lines = explode("\n", $master_content);
        
        if (strpos($master_url, 'asset.fembed.co') !== false) {
            $base_url = 'https://asset.fembed.co/';
        } else {
            $base_url = dirname($master_url) . '/';
        }
        
        $resolutions_found = [];
        $current_resolution = null;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (strpos($line, '#EXT-X-STREAM-INF:') === 0) {
                if (preg_match('/RESOLUTION=(\d+)x(\d+)/', $line, $matches)) {
                    $current_resolution = $matches[2];
                    $this->log("Found resolution: {$current_resolution}p");
                }
            } elseif (!empty($line) && strpos($line, '#') !== 0 && strpos($line, '.m3u8') !== false && $current_resolution) {
                $resolutions_found[] = $current_resolution;
                
                if (strpos($line, 'http') === 0) {
                    $resolution_url = $line;
                } elseif (strpos($line, '//') === 0) {
                    $resolution_url = 'https:' . $line;
                } else {
                    $resolution_url = $base_url . $line;
                }
                
                $resolution_content = $this->fetch_html($resolution_url);
                
                if ($resolution_content) {
                    $files['resolutions'][$current_resolution] = [
                        'url' => $resolution_url,
                        'content' => $resolution_content,
                        'filename' => $current_resolution . '.m3u8'
                    ];
                    $this->log("Successfully extracted resolution: {$current_resolution}p");
                }
                
                $current_resolution = null;
            }
        }
        
        if (empty($resolutions_found)) {
            $this->log("No resolution files found in master M3U8");
        } else {
            $this->log("Total resolutions extracted: " . count($resolutions_found));
        }
    }
    
    protected function try_fembed_fallback($player_url) {
        $this->log("Trying Fembed fallback for: $player_url");

        if (!preg_match('/fembed\.co\/embed\/([^\/\?]+)/', $player_url, $matches)) {
            return false;
        }

        $video_id = $matches[1];
        $language_codes = ['th', 'en', 'de'];
        $extensions = ['m3u8', 'txt'];

        foreach ($language_codes as $lang) {
            foreach ($extensions as $ext) {
                $test_url = "https://asset.fembed.co/master/$lang/$video_id.$ext";

                $content = $this->fetch_html($test_url);

                if ($content && strpos($content, '#EXTM3U') !== false) {
                    $this->log("Found valid Fembed M3U8: $test_url");
                    return $test_url;
                }
            }
        }

        return false;
    }

    protected function try_zplays_fallback($player_url) {
        $this->log("Trying zplays fallback for: $player_url");

        if (!preg_match('/zplays\.xyz\/embed\/([^\/\?]+)/', $player_url, $matches)) {
            return false;
        }

        $video_id = $matches[1];
        $language_codes = ['th', 'en', 'de'];

        foreach ($language_codes as $lang) {
            $test_url = "https://asset.fembed.co/master/$lang/$video_id.txt";

            $content = $this->fetch_html($test_url);

            if ($content && strpos($content, '#EXTM3U') !== false) {
                $this->log("Found valid zplays M3U8: $test_url");
                return $test_url;
            }
        }

        return false;
    }
    
    protected function save_m3u8_files($post_id, $type, $language, $m3u8_files) {
        $folder_type = $language;
        
        $post_dir = $this->upload_dir . 'post-' . $post_id . '/' . $folder_type . '/';
        
        if (!file_exists($post_dir)) {
            wp_mkdir_p($post_dir);
        }
        
        $saved_files = [];
        $base_path = 'post-' . $post_id . '/' . $folder_type . '/';
        
        if (isset($m3u8_files['master_original'])) {
            $master_file = $post_dir . 'master_original.m3u8';
            file_put_contents($master_file, $m3u8_files['master_original']['content']);
            $saved_files['master_original'] = $this->base_url . $base_path . 'master_original.m3u8';
            $this->log("Saved original master file for $language in $folder_type folder");
        }
        
        if (isset($m3u8_files['resolutions'])) {
            foreach ($m3u8_files['resolutions'] as $resolution => $data) {
                $filename = $data['filename'];
                $resolution_file = $post_dir . $filename;
                file_put_contents($resolution_file, $data['content']);
                $saved_files['resolutions'][$resolution] = $this->base_url . $base_path . $filename;
                $this->log("Saved resolution '$resolution' for $language in $folder_type folder");
            }
        }
        
        if (isset($m3u8_files['master_original'])) {
            $local_resolutions = $saved_files['resolutions'] ?? [];
            $custom_master = $this->create_custom_master($m3u8_files['master_original']['content'], $local_resolutions);
            $custom_master_file = $post_dir . 'master.m3u8';
            file_put_contents($custom_master_file, $custom_master);
            $saved_files['master'] = $this->base_url . $base_path . 'master.m3u8';
            
            $resolution_count = count($local_resolutions);
            $this->log("Created custom master file for $language in $folder_type folder with $resolution_count local resolution(s)");
            
            if ($resolution_count > 0) {
                foreach ($local_resolutions as $res => $url) {
                    $this->log("Master file includes local resolution: {$res}p -> $url");
                }
            }
        }
        
        return $saved_files;
    }
    
    protected function create_custom_master($original_content, $local_resolutions = []) {
        $lines = explode("\n", $original_content);
        $custom_lines = [];
        $current_stream_info = null;
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (empty($line)) {
                $custom_lines[] = $line;
                continue;
            }
            
            if (strpos($line, '#EXT-X-STREAM-INF:') === 0) {
                $current_stream_info = $line;
                $custom_lines[] = $line;
                continue;
            }
            
            if (strpos($line, '#') === 0) {
                $custom_lines[] = $line;
                continue;
            }
            
            if (strpos($line, '.m3u8') !== false) {
                $found_local = false;
                $line_resolution = null;
                
                if ($current_stream_info && preg_match('/RESOLUTION=(\d+)x(\d+)/', $current_stream_info, $res_matches)) {
                    $line_resolution = $res_matches[2];
                }
                
                if ($line_resolution && !empty($local_resolutions)) {
                    foreach ($local_resolutions as $resolution => $local_url) {
                        if ($line_resolution === $resolution) {
                            $custom_lines[] = $local_url;
                            $found_local = true;
                            $this->log("Replaced with local resolution file for {$line_resolution}p: $local_url");
                            break;
                        }
                    }
                }
                
                if (!$found_local) {
                    if (!empty($local_resolutions)) {
                        $this->log("No local file found for resolution {$line_resolution}p, keeping original: $line");
                    }
                    $custom_lines[] = $line;
                }
                
                $current_stream_info = null;
            } else {
                $custom_lines[] = $line;
            }
        }
        
        return implode("\n", $custom_lines);
    }
    
    protected function extract_metadata($post_id, $html) {
        $this->log("Extracting metadata for post ID: $post_id");
        
        $settings = get_option('movie_scraper_settings', []);
        
        if (isset($settings['auto_fill_youtube']) && $settings['auto_fill_youtube']) {
            $youtube_id = $this->extract_youtube_id($html);
            if ($youtube_id) {
                $existing_youtube = get_post_meta($post_id, 'linkvideo', true);
                if (!$existing_youtube || $existing_youtube !== $youtube_id) {
                    update_post_meta($post_id, 'linkvideo', $youtube_id);
                    $this->log("YouTube ID saved: $youtube_id (replaced: $existing_youtube)");
                } else {
                    $this->log("YouTube ID already exists: $existing_youtube");
                }
            }
        }
        
        if (isset($settings['auto_fill_duration']) && $settings['auto_fill_duration']) {
            $duration = $this->extract_duration($html);
            if ($duration) {
                update_post_meta($post_id, 'movie_duration', $duration);
                $this->log("Duration saved: $duration minutes");
            }
        }
        
        if (isset($settings['auto_fill_imdb']) && $settings['auto_fill_imdb']) {
            $imdb_rating = $this->extract_imdb_rating($html);
            if ($imdb_rating) {
                $this->sync_imdb_rating($post_id, $imdb_rating);
                $this->log("IMDB rating saved: $imdb_rating");
            }
        }
        
        if (isset($settings['auto_download_poster']) && $settings['auto_download_poster']) {
            $poster_url = $this->extract_poster_image($html);
            if ($poster_url) {
                $existing_poster = get_post_meta($post_id, '_featured_image_url', true);
                if (!$existing_poster || $existing_poster !== $poster_url) {
                    update_post_meta($post_id, '_featured_image_url', $poster_url);
                    $this->log("Poster URL saved: $poster_url (replaced: $existing_poster)");
                } else {
                    $this->log("Poster URL already exists: $existing_poster");
                }
            }
        }
        
        $this->auto_fill_title($post_id, $html);
    }
    
    protected function extract_youtube_id($html) {
        $patterns = [
            [
                'pattern' => '/videoId:\s*["\']([a-zA-Z0-9_-]+)["\']/',
                'name' => 'videoId_js',
                'priority' => 1
            ],
            [
                'pattern' => '/youtube\.com\/vi\/([a-zA-Z0-9_-]+)/',
                'name' => 'youtube_vi',
                'priority' => 2
            ],
            [
                'pattern' => '/youtube\.com\/embed\/([a-zA-Z0-9_-]+)/',
                'name' => 'youtube_embed',
                'priority' => 3
            ],
            [
                'pattern' => '/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/',
                'name' => 'youtube_watch',
                'priority' => 4
            ],
            [
                'pattern' => '/youtu\.be\/([a-zA-Z0-9_-]+)/',
                'name' => 'youtu_be',
                'priority' => 5
            ]
        ];

        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $youtube_id = $matches[1];
                if (preg_match('/^[a-zA-Z0-9_-]{10,12}$/', $youtube_id)) {
                    $this->log("YouTube ID extracted: $youtube_id using pattern: {$pattern_info['name']}");
                    return $youtube_id;
                }
            }
        }
        
        $this->log("No YouTube ID found in HTML");
        $this->log("HTML snippet for YouTube debugging: " . substr($html, 0, 1000) . "...");
        return null;
    }
    
    protected function extract_duration($html) {
        $this->log("Extracting duration from HTML");

        // รูปแบบที่ 1: มีทั้งชั่วโมงและนาที เช่น "1 <span>ชั่วโมง</span> 30 <span>นาที</span>"
        if (preg_match('/(\d+)\s*<span[^>]*>ชั่วโมง<\/span>\s*(\d+)\s*<span[^>]*>นาที<\/span>/', $html, $matches)) {
            $hours = intval($matches[1]);
            $minutes = intval($matches[2]);
            $total_minutes = ($hours * 60) + $minutes;
            $this->log("Found duration with hours and minutes: {$hours}h {$minutes}m = {$total_minutes} minutes");
            return $total_minutes;
        }

        // รูปแบบที่ 2: มีเฉพาะชั่วโมง เช่น "2 <span>ชั่วโมง</span>"
        if (preg_match('/(\d+)\s*<span[^>]*>ชั่วโมง<\/span>/', $html, $matches)) {
            $hours = intval($matches[1]);
            $total_minutes = $hours * 60;
            $this->log("Found duration with hours only: {$hours}h = {$total_minutes} minutes");
            return $total_minutes;
        }

        // รูปแบบที่ 3: มีเฉพาะนาที เช่น "40 <span>นาที</span>"
        if (preg_match('/(\d+)\s*<span[^>]*>นาที<\/span>/', $html, $matches)) {
            $minutes = intval($matches[1]);
            $this->log("Found duration with minutes only: {$minutes} minutes");
            return $minutes;
        }

        $this->log("No duration pattern matched");
        return null;
    }
    
    protected function extract_imdb_rating($html) {
        $patterns = [
            [
                'pattern' => '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?<\/noscript>([0-9]+\.?[0-9]*)/i',
                'name' => 'score_noscript',
                'priority' => 1
            ],
            [
                'pattern' => '/<span[^>]*class=["\']*score["\']*[^>]*>[\s\S]*?([0-9]+\.?[0-9]*)/i',
                'name' => 'score_span',
                'priority' => 2
            ],
            [
                'pattern' => '/<span[^>]*class="star"[^>]*><img[^>]*alt="IMDb"[^>]*>\s*([0-9]+\.?[0-9]*)\s*<\/span>/i',
                'name' => 'star_imdb',
                'priority' => 3
            ],
            [
                'pattern' => '/IMDb[^0-9]*([0-9]+\.?[0-9]*)/i',
                'name' => 'generic_imdb',
                'priority' => 4
            ]
        ];
        
        foreach ($patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $rating = floatval($matches[1]);
                if ($rating >= 0 && $rating <= 10) {
                    $this->log("IMDB rating extracted: {$rating} using pattern: {$pattern_info['name']}");
                    return $rating;
                } else {
                    $this->log("Invalid IMDB rating: {$rating} (out of range 0-10)");
                }
            }
        }
        
        $this->log("No IMDB rating pattern matched in HTML");
        $this->log("HTML snippet for debugging: " . substr($html, 0, 500) . "...");
        return null;
    }
    
    protected function extract_poster_image($html) {
        $patterns = [
            '/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*class=["\'][^"\']*poster[^"\']*["\'][^>]*src=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*class=["\'][^"\']*cover[^"\']*["\'][^>]*src=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*class=["\'][^"\']*thumbnail[^"\']*["\'][^>]*src=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*src=["\']([^"\']+)["\'][^>]*class=["\'][^"\']*poster[^"\']*["\']>/i',
            '/<div[^>]*class=["\'][^"\']*poster[^"\']*["\'][^>]*><img[^>]*src=["\']([^"\']+)["\'][^>]*>/i'
        ];
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $image_url = trim($matches[1]);
                if ($this->is_valid_image_url($image_url)) {
                    $this->log("Poster image extracted: $image_url using pattern: $pattern");
                    return $image_url;
                }
            }
        }
        $this->log("No poster image found in HTML");
        return null;
    }

    protected function is_valid_image_url($url) {
        if (empty($url)) {
            return false;
        }
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'];
        $pathInfo = pathinfo($url);
        $extension = isset($pathInfo['extension']) ? strtolower($pathInfo['extension']) : '';
        return in_array($extension, $imageExtensions) && (strpos($url, 'http') === 0);
    }
    
    protected function auto_fill_title($post_id, $html) {
        $current_title = get_the_title($post_id);
        $this->log("Current title: '$current_title'");
        
        $title_patterns = [
            [
                'pattern' => '/<div[^>]*class=["\'][^"\']*movietext[^"\']*["\'][^>]*>[\s\S]*?<h1[^>]*>([^<]+)<\/h1>/i',
                'name' => 'movietext_h1',
                'priority' => 1
            ],
            [
                'pattern' => '/<h1[^>]*>([^<]+)<\/h1>/i',
                'name' => 'generic_h1',
                'priority' => 2
            ],
            [
                'pattern' => '/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
                'name' => 'og_title',
                'priority' => 3
            ]
        ];

        foreach ($title_patterns as $pattern_info) {
            if (preg_match($pattern_info['pattern'], $html, $matches)) {
                $title = trim(strip_tags($matches[1]));
                $title = preg_replace('/^ดูหนัง\s*/i', '', $title);
                $title = preg_replace('/\s*เต็มเรื่อง.*$/i', '', $title);

                if (!empty($title) && strlen($title) > 3 &&
                    strpos($title, 'เว็บดูหนัง') === false &&
                    strpos($title, 'Netflix ฟรี') === false &&
                    strpos($title, 'ดูหนังใหม่') === false) {

                    wp_update_post([
                        'ID' => $post_id,
                        'post_title' => $title
                    ]);
                    $this->log("Title updated from '$current_title' to '$title' using pattern: {$pattern_info['name']}");
                    return true;
                }
            }
        }
        
        $this->log("No valid title found in HTML");
        return false;
    }
    
    protected function sync_imdb_rating($post_id, $imdb_rating) {
        update_post_meta($post_id, 'imdb_rating', $imdb_rating);
        
        $term_result = wp_set_post_terms($post_id, [strval($imdb_rating)], 'imdb');
        if (is_wp_error($term_result)) {
            $this->log("Failed to save IMDB rating to taxonomy: " . $term_result->get_error_message());
        }
    }
    
    protected function update_scraping_status($post_id, $status, $message = '', $progress = null) {
        update_post_meta($post_id, 'scraping_status', $status);
        update_post_meta($post_id, 'scraping_message', $message);
        update_post_meta($post_id, 'scraping_updated', current_time('mysql'));

        if ($progress !== null) {
            update_post_meta($post_id, 'scraping_progress', intval($progress));
        }

        $progress_text = $progress !== null ? " ($progress%)" : "";
        $this->log("Status updated for post $post_id: $status - $message$progress_text");
    }
    
    protected function clear_scraping_data($post_id) {
        $this->log("Clearing scraping data for post ID: $post_id");
        
        $meta_keys = [
            'scraping_status', 'scraping_message', 'scraping_updated',
            'scraping_dubbed_status', 'scraping_dubbed_message', 'scraping_dubbed_files', 'scraping_dubbed_url', 'scraping_dubbed_updated',
            'scraping_subbed_status', 'scraping_subbed_message', 'scraping_subbed_files', 'scraping_subbed_url', 'scraping_subbed_updated',
            'dubbed_master_original_url', 'subbed_master_original_url',
            'm3u8_dubbed', 'm3u8_subbed',
            'imdb_rating', 'movie_duration', 'linkvideo',
            '_featured_image_url',
            'scraping_video_unavailable', 'scraping_requires_user_notification', 'scraping_notification_message', 'scraping_notification_data',
            'series_episodes', 'series_url',
            'scraper_dubbed_episodes', 'scraper_subbed_episodes', 'scraper_series_info',
            'scraper_dubbed_original_url', 'scraper_subbed_original_url',
            'scraper_dubbed_master_url', 'scraper_subbed_master_url',
            'scraper_dubbed_files', 'scraper_subbed_files'
        ];
        
        foreach ($meta_keys as $key) {
            delete_post_meta($post_id, $key);
            $this->log("Deleted meta key: $key");
        }
        
        wp_set_post_terms($post_id, [], 'imdb');
        $this->log("Cleared IMDB taxonomy terms");
        
        $current_title = get_the_title($post_id);
        if ($current_title && $current_title !== 'Auto Draft') {
            wp_update_post([
                'ID' => $post_id,
                'post_title' => 'Auto Draft'
            ]);
            $this->log("Reset post title to Auto Draft");
        }
        
        $post_dir = $this->upload_dir . 'post-' . $post_id . '/';
        if (is_dir($post_dir)) {
            $this->log("Deleting directory: $post_dir");
            $this->delete_directory($post_dir);
        }
        
        $this->log("Completed clearing all scraping data for post ID: $post_id");
    }
    
    protected function delete_directory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->delete_directory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
    
    public function check_source_change($post_id, $new_url) {
        $stored_url = get_post_meta($post_id, 'scraper_source_url', true);
        $stored_website = get_post_meta($post_id, 'scraper_source_website', true);

        $new_website = $this->detect_website($new_url);

        if (empty($stored_url) || empty($stored_website)) {
            update_post_meta($post_id, 'scraper_source_url', $new_url);
            update_post_meta($post_id, 'scraper_source_website', $new_website);
            $this->log("First time scraping - stored source: $new_website");
            return 'first_time';
        }

        if ($stored_website !== $new_website) {
            $this->log("Website changed from $stored_website to $new_website - will clear old data");
            return 'website_changed';
        }

        if ($stored_url !== $new_url) {
            $this->log("URL changed from $stored_url to $new_url - will update incrementally");
            return 'url_changed';
        }

        $this->log("Same source detected - will update incrementally");
        return 'same_source';
    }

    protected function handle_source_change($post_id, $change_type, $new_url) {
        switch ($change_type) {
            case 'website_changed':
                $this->log("Clearing all old data due to website change");
                $this->clear_scraping_data($post_id);
                update_post_meta($post_id, 'scraper_source_url', $new_url);
                update_post_meta($post_id, 'scraper_source_website', $this->detect_website($new_url));
                delete_post_meta($post_id, 'scraper_last_change_type');
                $this->log("Cleared old data and updated source info for website change");
                return true;

            case 'url_changed':
                $this->log("URL changed within same website - clearing episode data for fresh scrape");
                $episode_keys = [
                    'scraper_dubbed_episodes', 'scraper_subbed_episodes',
                    'scraper_dubbed_original_url', 'scraper_subbed_original_url',
                    'scraper_dubbed_files', 'scraper_subbed_files'
                ];
                foreach ($episode_keys as $key) {
                    delete_post_meta($post_id, $key);
                }
                update_post_meta($post_id, 'scraper_source_url', $new_url);
                return true;

            case 'same_source':
                $this->log("Same source detected - will update incrementally");
                update_post_meta($post_id, 'scraper_source_url', $new_url);
                return false;

            case 'first_time':
            default:
                $this->log("First time scraping - no data to clear");
                return false;
        }
    }

    public function detect_website($url) {
        if (strpos($url, 'serieday-hd.com') !== false) {
            return 'serieday-hd';
        } elseif (strpos($url, '22-hdd.com') !== false) {
            return '22-hdd';
        } elseif (strpos($url, '24-hd.com') !== false) {
            return '24-hd';
        }
        return 'unknown';
    }

    protected function log($message) {
        $class_name = get_class($this);
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("[$class_name] $message");
        }
    }
}

class ScrapingProgressManager {
    private static $instance = null;
    private $progress_steps = [];
    private $current_step = 0;
    private $total_steps = 0;
    private $post_id = null;
    private $post_type = null;
    
    private $step_definitions = [
        'movie' => [
            'init' => ['weight' => 2, 'message' => 'เริ่มต้นการดึงข้อมูลหนัง'],
            'domain_check' => ['weight' => 3, 'message' => 'ตรวจสอบการเปลี่ยนแปลงโดเมน'],
            'fetch_html' => ['weight' => 8, 'message' => 'กำลังดาวน์โหลดหน้าเว็บ'],
            'extract_metadata' => ['weight' => 10, 'message' => 'กำลังดึงข้อมูลหนัง (ชื่อ, คะแนน, โปสเตอร์)'],
            'find_player' => ['weight' => 8, 'message' => 'กำลังค้นหา Player หลัก'],
            'fetch_player' => ['weight' => 12, 'message' => 'กำลังดาวน์โหลดข้อมูล Player'],
            'extract_config' => ['weight' => 10, 'message' => 'กำลังดึงข้อมูลการตั้งค่าวิดีโอ'],
            'process_dubbed' => ['weight' => 22, 'message' => 'กำลังดึงไฟล์พากย์ไทย'],
            'process_subbed' => ['weight' => 22, 'message' => 'กำลังดึงไฟล์ซับไทย'],
            'finalize' => ['weight' => 3, 'message' => 'กำลังบันทึกข้อมูล']
        ],
        'serie' => [
            'init' => ['weight' => 1, 'message' => 'เริ่มต้นการดึงข้อมูลซีรีย์'],
            'domain_check' => ['weight' => 2, 'message' => 'ตรวจสอบการเปลี่ยนแปลงโดเมน'],
            'detect_website' => ['weight' => 2, 'message' => 'กำลังตรวจสอบเว็บไซต์'],
            'fetch_html' => ['weight' => 5, 'message' => 'กำลังดาวน์โหลดหน้าเว็บ'],
            'extract_metadata' => ['weight' => 5, 'message' => 'กำลังดึงข้อมูลซีรีย์ (ชื่อ, คะแนน, โปสเตอร์)'],
            'detect_season' => ['weight' => 3, 'message' => 'กำลังตรวจสอบซีซั่น'],
            'find_player' => ['weight' => 5, 'message' => 'กำลังค้นหา Player หลัก'],
            'fetch_player' => ['weight' => 7, 'message' => 'กำลังดาวน์โหลดข้อมูล Player'],
            'extract_config' => ['weight' => 8, 'message' => 'กำลังดึงข้อมูลการตั้งค่าวิดีโอ'],
            'process_episodes' => ['weight' => 60, 'message' => 'กำลังประมวลผลตอนต่างๆ'],
            'finalize' => ['weight' => 2, 'message' => 'กำลังบันทึกข้อมูล']
        ],
        'anime' => [
            'init' => ['weight' => 1, 'message' => 'เริ่มต้นการดึงข้อมูลอนิเมะ'],
            'domain_check' => ['weight' => 2, 'message' => 'ตรวจสอบการเปลี่ยนแปลงโดเมน'],
            'detect_website' => ['weight' => 2, 'message' => 'กำลังตรวจสอบเว็บไซต์'],
            'fetch_html' => ['weight' => 5, 'message' => 'กำลังดาวน์โหลดหน้าเว็บ'],
            'extract_metadata' => ['weight' => 5, 'message' => 'กำลังดึงข้อมูลอนิเมะ (ชื่อ, คะแนน, โปสเตอร์)'],
            'detect_season' => ['weight' => 3, 'message' => 'กำลังตรวจสอบซีซั่น'],
            'find_player' => ['weight' => 5, 'message' => 'กำลังค้นหา Player หลัก'],
            'fetch_player' => ['weight' => 7, 'message' => 'กำลังดาวน์โหลดข้อมูล Player'],
            'extract_config' => ['weight' => 8, 'message' => 'กำลังดึงข้อมูลการตั้งค่าวิดีโอ'],
            'process_episodes' => ['weight' => 60, 'message' => 'กำลังประมวลผลตอนต่างๆ'],
            'finalize' => ['weight' => 2, 'message' => 'กำลังบันทึกข้อมูล']
        ]
    ];
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function initializeForPost($post_id, $post_type = null) {
        $this->post_id = $post_id;
        $this->post_type = $post_type ?: get_post_type($post_id);
        $this->current_step = 0;
        
        if (!isset($this->step_definitions[$this->post_type])) {
            $this->post_type = 'movie';
        }
        
        $this->progress_steps = $this->step_definitions[$this->post_type];
        $this->total_steps = array_sum(array_column($this->progress_steps, 'weight'));
        
        $this->log("Progress initialized for post $post_id (type: {$this->post_type}) with {$this->total_steps} total weight");
        return $this;
    }
    
    public function updateProgress($step_name, $sub_progress = 0, $custom_message = null) {
        if (!isset($this->progress_steps[$step_name])) {
            $this->log("Unknown step: $step_name");
            return;
        }
        
        $completed_weight = 0;
        $step_found = false;
        
        foreach ($this->progress_steps as $name => $step) {
            if ($name === $step_name) {
                $step_found = true;
                $step_weight = $step['weight'];
                $step_progress = ($sub_progress / 100) * $step_weight;
                $completed_weight += $step_progress;
                break;
            }
            $completed_weight += $step['weight'];
        }
        
        if (!$step_found) {
            return;
        }
        
        $progress_percentage = min(100, ($completed_weight / $this->total_steps) * 100);
        $message = $custom_message ?: $this->progress_steps[$step_name]['message'];
        
        if ($sub_progress > 0 && $sub_progress < 100) {
            $message .= " ({$sub_progress}%)";
        }
        
        $this->updateScrapingStatus('processing', $message, $progress_percentage);
        $this->log("Progress updated: $step_name - {$progress_percentage}% - $message");
    }
    
    public function completeStep($step_name, $custom_message = null) {
        $this->updateProgress($step_name, 100, $custom_message);
    }
    
    public function setError($message, $step_name = null) {
        $progress = $step_name ? $this->calculateProgressForStep($step_name) : 0;
        $this->updateScrapingStatus('failed', $message, $progress);
        $this->log("Error set: $message");
    }
    
    public function setCompleted($message = null, $progress = 100) {
        $final_message = $message ?: 'ดึงข้อมูลเสร็จสิ้น';
        $this->updateScrapingStatus('completed', $final_message, $progress);
        $this->log("Completed: $final_message");
    }
    
    public function setVideoUnavailable($message = 'ไฟล์วิดีโอไม่พร้อมใช้งาน') {
        $this->updateScrapingStatus('video_unavailable', $message, 100);
        $this->log("Video unavailable: $message");
    }
    
    public function checkDomainChange($new_url) {
        if (!$this->post_id) {
            return 'first_time';
        }
        
        $stored_url = get_post_meta($this->post_id, 'scraper_source_url', true);
        $stored_domain = get_post_meta($this->post_id, 'scraper_source_domain', true);
        
        $new_domain = $this->extractDomain($new_url);
        
        if (empty($stored_url) || empty($stored_domain)) {
            update_post_meta($this->post_id, 'scraper_source_url', $new_url);
            update_post_meta($this->post_id, 'scraper_source_domain', $new_domain);
            $this->log("First time scraping - stored domain: $new_domain");
            return 'first_time';
        }
        
        if ($stored_domain !== $new_domain) {
            update_post_meta($this->post_id, 'scraper_source_url', $new_url);
            update_post_meta($this->post_id, 'scraper_source_domain', $new_domain);
            $this->log("Domain changed from $stored_domain to $new_domain");
            return 'domain_changed';
        }
        
        if ($stored_url !== $new_url) {
            update_post_meta($this->post_id, 'scraper_source_url', $new_url);
            $this->log("URL changed within same domain");
            return 'url_changed';
        }
        
        $this->log("Same source detected");
        return 'same_source';
    }
    
    private function extractDomain($url) {
        $parsed = parse_url($url);
        return $parsed['host'] ?? 'unknown';
    }
    
    private function calculateProgressForStep($step_name) {
        $completed_weight = 0;
        
        foreach ($this->progress_steps as $name => $step) {
            if ($name === $step_name) {
                break;
            }
            $completed_weight += $step['weight'];
        }
        
        return ($completed_weight / $this->total_steps) * 100;
    }
    
    private function updateScrapingStatus($status, $message, $progress) {
        if (!$this->post_id) {
            return;
        }
        
        update_post_meta($this->post_id, 'scraping_status', $status);
        update_post_meta($this->post_id, 'scraping_message', $message);
        update_post_meta($this->post_id, 'scraping_progress', intval($progress));
        update_post_meta($this->post_id, 'scraping_updated', current_time('mysql'));
    }
    
    private function log($message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("[ScrapingProgressManager] $message");
        }
    }


}