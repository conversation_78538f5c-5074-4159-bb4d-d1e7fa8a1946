<?php

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/BaseScraper.php';

class SeriesScraper extends BaseScraper {

    private $post_id;

    private $detection_patterns = [
        'episodes' => [
            'high_confidence' => [
                'weight' => 10,
                'patterns' => [
                    '/ตอนที่\s*(\d+)/i',
                    '/Episode\s*(\d+)/i',
                    '/EP\s*(\d+)/i',
                    '/E(\d+)(?![^\s]*[a-zA-Z])/i'
                ]
            ],
            'medium_confidence' => [
                'weight' => 5,
                'patterns' => [
                    '/\bEP(\d+)\b/i',
                    '/\b(\d+)(?:\s*-\s*(?:END|จบ|ตอนจบ))/i'
                ]
            ]
        ],
        'seasons' => [
            'high_confidence' => [
                'weight' => 10,
                'patterns' => [
                    '/Season\s*(\d+)/i',
                    '/ปี\s*(\d+)/i',
                    '/ซีซั่น\s*(\d+)/i'
                ]
            ],
            'medium_confidence' => [
                'weight' => 7,
                'patterns' => [
                    '/S(\d+)/i',
                    '/SS(\d+)/i'
                ]
            ]
        ]
    ];
    
    public function __construct() {
        parent::__construct();
        $this->upload_dir .= 'series/';
        $this->base_url .= 'series/';
        
        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }
    
    public function scrape($post_id, $series_url, $options = []) {
        try {
            $this->post_id = $post_id;
            $progress = ScrapingProgressManager::getInstance()->initializeForPost($post_id, 'serie');
            
            $this->log("Starting series scraping for post ID: $post_id, URL: $series_url");
            $progress->updateProgress('init', 0);

            $change_type = $progress->checkDomainChange($series_url);
            $progress->updateProgress('domain_check', 50);
            
            $should_clear = $this->handle_source_change($post_id, $change_type, $series_url);
            update_post_meta($post_id, 'scraper_last_change_type', $change_type);

            if ($should_clear) {
                $this->log("Cleared old data due to source change");
            }
            $progress->completeStep('domain_check');

            $progress->updateProgress('detect_website', 0);
            $website = $this->detect_website($series_url);
            $this->log("Detected website: $website");
            $progress->completeStep('detect_website');

            switch ($website) {
                case 'serieday-hd':
                    $progress->updateProgress('fetch_html', 0, 'เชื่อมต่อ SeriedayHD');
                    $scraper = new SeriedayHDScraper();
                    return $scraper->scrape_serieday($post_id, $series_url, $options);

                case '22-hdd':
                    $progress->updateProgress('fetch_html', 0, 'เชื่อมต่อ 22HDD');
                    return $this->scrape_22hdd($post_id, $series_url, $options);

                default:
                    $error_msg = "Unsupported website: $website. Please add scraper support for this website.";
                    $this->log("ERROR: $error_msg");
                    $progress->setError($error_msg, 'detect_website');
                    return false;
            }

        } catch (Exception $e) {
            $error_msg = 'Exception occurred: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }



    private function scrape_22hdd($post_id, $series_url, $options = []) {
        $this->post_id = $post_id;
        $progress = ScrapingProgressManager::getInstance();
        
        $this->log("Using 22-hdd.com scraping method");

        $progress->updateProgress('fetch_html', 20);
        $html = $this->fetch_html($series_url);
        if (!$html) {
            $error_msg = 'Could not fetch HTML from URL: ' . $series_url;
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg, 'fetch_html');
            return false;
        }
        $progress->completeStep('fetch_html');

        $progress->updateProgress('extract_metadata', 20);
        $this->extract_metadata($post_id, $html);
        $this->log("Extracted metadata for series scraping");
        $progress->completeStep('extract_metadata');

        $progress->updateProgress('detect_season', 0);
        $target_season = $this->detect_target_season($post_id, $options, $series_url);
        $this->log("Final target season determined: $target_season");
        $progress->completeStep('detect_season');

        $progress->updateProgress('find_player', 0);
        $main_player_url = $this->extract_main_player($html);
        if (!$main_player_url) {
            $error_msg = 'No main player iframe found in HTML';
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg, 'find_player');
            return false;
        }
        $progress->completeStep('find_player');

        $progress->updateProgress('fetch_player', 0);
        $player_html = $this->fetch_html($main_player_url);
        if (!$player_html) {
            $error_msg = 'Could not fetch player HTML from URL: ' . $main_player_url;
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg, 'fetch_player');
            return false;
        }
        $progress->completeStep('fetch_player');

        $progress->updateProgress('extract_config', 0);
        $config = $this->extract_config($player_html);
        if (!$config) {
            $this->log("No direct config found, trying fallback detection");
            $config = $this->fallback_episode_detection($player_html, $target_season);

            if (!$config) {
                $error_msg = 'No series configuration found in player HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'extract_config');
                return false;
            }
        }
        $progress->completeStep('extract_config');

        return $this->process_content($post_id, $config, ['target_season' => $target_season]);
    }
    
    protected function detect_target_season($post_id, $options = [], $url = '') {
        if (isset($options['target_season']) && $options['target_season']) {
            $this->log("Using manual target season: " . $options['target_season']);
            return $options['target_season'];
        }
        
        $post_title = get_the_title($post_id);
        $this->log("Detecting target season from post title: $post_title");
        
        $sources_to_check = [
            'post_title' => $post_title,
            'url' => $url
        ];
        
        $english_patterns = [
            '/\bSeason\s*(\d+)/i',
            '/\bS(\d+)(?:\s|$|[^\w])/i',
            '/\bSS(\d+)(?:\s|$|[^\w])/i',
            '/\bSeries\s*(\d+)/i', 
            '/\bYear\s*(\d+)/i',
            '/\bPart\s*(\d+)/i',
            '/\bVol(?:ume)?\s*(\d+)/i',
            '/\bBook\s*(\d+)/i',
            '/(\d+)(?:st|nd|rd|th)\s*Season/i',
            '/Season\s*(\d+)/i',
            '/\bS(\d{2})/i'
        ];
        
        $thai_patterns = [
            '/ซีซั่น\s*(\d+)/i',
            '/ปี\s*(\d+)/i', 
            '/ภาค\s*(\d+)/i',
            '/เล่ม\s*(\d+)/i',
            '/ตอน\s*(\d+)/i'
        ];
        
        foreach ($sources_to_check as $source_name => $source_text) {
            if (empty($source_text) || $source_text === 'Auto Draft') {
                $this->log("Skipping empty or auto draft source: $source_name");
                continue;
            }
            
            $this->log("Checking season patterns in $source_name: $source_text");
            
            $english_season = null;
            $thai_season = null;
            $matched_pattern = '';
            
            foreach ($english_patterns as $pattern) {
                if (preg_match($pattern, $source_text, $matches)) {
                    $english_season = intval($matches[1]);
                    $matched_pattern = $pattern;
                    $this->log("English pattern matched in $source_name: $pattern -> Season $english_season");
                    break;
                }
            }
            
            foreach ($thai_patterns as $pattern) {
                if (preg_match($pattern, $source_text, $matches)) {
                    $thai_season = intval($matches[1]);
                    $this->log("Thai pattern matched in $source_name: $pattern -> Season $thai_season");
                    break;
                }
            }
            
            if ($english_season !== null && $thai_season !== null) {
                if ($english_season === $thai_season) {
                    $this->log("English and Thai seasons match in $source_name: Season $english_season");
                    return $english_season;
                } else {
                    $this->log("Season conflict in $source_name! English: $english_season, Thai: $thai_season - Using English as primary");
                    return $english_season;
                }
            }
            
            if ($english_season !== null) {
                $this->log("Using English detected season from $source_name: $english_season");
                return $english_season;
            }
            
            if ($thai_season !== null) {
                $this->log("Using Thai detected season from $source_name: $thai_season");
                return $thai_season;
            }
        }
        
        foreach ($sources_to_check as $source_name => $source_text) {
            if (empty($source_text) || $source_text === 'Auto Draft') {
                continue;
            }
            
            $numeric_patterns = [
                '/\b(\d+)\b.*(?:season|ซีซั่น|ปี)/i',
                '/(?:season|ซีซั่น|ปี).*\b(\d+)\b/i'
            ];
            
            foreach ($numeric_patterns as $pattern) {
                if (preg_match($pattern, $source_text, $matches)) {
                    $detected_season = intval($matches[1]);
                    if ($detected_season > 0 && $detected_season < 20) {
                        $this->log("Fallback numeric pattern detected in $source_name: Season $detected_season");
                        return $detected_season;
                    }
                }
            }
        }
        
        $this->log("No specific season detected from any source, defaulting to Season 1");
        return 1;
    }
    
    protected function process_content($post_id, $config, $options = []) {
        $target_season = $options['target_season'] ?? 1;
        $progress = ScrapingProgressManager::getInstance();
        
        $this->log("Processing series content for post ID: $post_id, Target Season: $target_season");
        $progress->updateProgress('process_episodes', 0);
        
        if (isset($config['fallback_mode']) && $config['fallback_mode']) {
            return $this->process_fallback_episodes($post_id, $config, $target_season);
        }
        
        $seasons_data = $this->extract_season_episodes($config, $target_season);
        
        if (empty($seasons_data)) {
            $this->log("No seasons data extracted for target season: $target_season");
            $progress->setError("No episodes found for Season $target_season", 'process_episodes');
            return false;
        }
        
        $existing_episodes = $this->get_existing_episodes($post_id, $target_season);
        $this->log("Found " . count($existing_episodes) . " existing episodes for Season $target_season");
        
        $processed_episodes = [];
        $total_episodes = 0;
        $successful_episodes = 0;
        $updated_episodes = 0;
        $new_episodes = 0;
        
        foreach ($seasons_data as $season_number => $episodes) {
            if ($season_number != $target_season) {
                $this->log("Skipping Season $season_number (target: $target_season)");
                continue;
            }
            
            $this->log("Processing Season $season_number with " . count($episodes) . " episodes");
            $total_episodes = count($episodes);
            
            foreach ($episodes as $episode_index => $episode_data) {
                $episode_progress = (($episode_index + 1) / $total_episodes) * 90;
                $progress->updateProgress('process_episodes', $episode_progress, "กำลังประมวลผลตอนที่ " . ($episode_index + 1) . "/$total_episodes");
                
                $episode_key = "S{$episode_data['season']}E{$episode_data['episode']}";
                
                $should_update = $this->should_update_episode($post_id, $episode_data, $existing_episodes);
                
                if ($should_update) {
                    if (isset($existing_episodes[$episode_key])) {
                        $this->log("Updating existing episode: $episode_key");
                        $this->remove_episode_files($post_id, $episode_data['season'], $episode_data['episode']);
                        $updated_episodes++;
                    } else {
                        $this->log("Adding new episode: $episode_key");
                        $new_episodes++;
                    }
                
                $episode_result = $this->process_single_episode($post_id, $episode_data);

                if ($episode_result) {
                    $processed_episodes[] = $episode_result;
                    $successful_episodes++;
                    $this->log("Successfully processed $episode_key");
                } else {
                    $this->log("Failed to process $episode_key - attempting auto-categorization");

                    $available_languages = $this->get_previous_episodes_languages(
                        $processed_episodes,
                        $episode_data['season'],
                        $episode_data['episode']
                    );

                    if (!empty($available_languages)) {
                        $fallback_episode = $this->create_fallback_episode($episode_data, $available_languages);
                        $processed_episodes[] = $fallback_episode;
                        $successful_episodes++;

                        $languages_str = implode(', ', $available_languages);
                        $this->log("Auto-categorized $episode_key with languages: $languages_str");
                    } else {
                        $this->log("No previous episodes found for auto-categorization of $episode_key");
                    }
                }
                } else {
                    $this->log("Skipping unchanged episode: $episode_key");
                    if (isset($existing_episodes[$episode_key])) {
                        $processed_episodes[] = $existing_episodes[$episode_key];
                        $successful_episodes++;
                    }
                }
            }
        }
        
        $progress->completeStep('process_episodes');
        $progress->updateProgress('finalize', 0);
        
        if ($successful_episodes > 0) {
            $this->save_episodes_hybrid($post_id, $processed_episodes, $target_season);
            $status_msg = "Successfully processed $successful_episodes episodes for Season $target_season";
            if ($new_episodes > 0) {
                $status_msg .= " (New: $new_episodes)";
            }
            if ($updated_episodes > 0) {
                $status_msg .= " (Updated: $updated_episodes)";
            }
            
            $progress->setCompleted($status_msg);
            $this->log("Series processing completed: $status_msg");
            return true;
        }
        
        $progress->setError("No episodes could be processed for Season $target_season", 'finalize');
        return false;
    }
    
    private function get_existing_episodes($post_id, $season) {
        $existing_episodes = [];
        
        if (function_exists('get_field')) {
            $episodes_data = get_field('series_episodes', $post_id);
        } else {
            $episodes_data = get_post_meta($post_id, 'series_episodes', true);
        }
        
        if (is_array($episodes_data)) {
            foreach ($episodes_data as $episode) {
                if (isset($episode['episode_season']) && $episode['episode_season'] == $season) {
                    $episode_key = "S{$episode['episode_season']}E{$episode['episode_number']}";
                    $existing_episodes[$episode_key] = $episode;
                }
            }
        }
        
        return $existing_episodes;
    }
    
    private function should_update_episode($post_id, $episode_data, $existing_episodes) {
        $change_type = get_post_meta($post_id, 'scraper_last_change_type', true);

        if ($change_type === 'website_changed' || $change_type === 'first_time') {
            return true;
        }

        $episode_key = "S{$episode_data['season']}E{$episode_data['episode']}";

        if (!isset($existing_episodes[$episode_key])) {
            $this->log("New episode detected: $episode_key");
            return true;
        }

        $existing_episode = $existing_episodes[$episode_key];

        if (isset($existing_episode['episode_title']) && $existing_episode['episode_title'] !== $this->clean_episode_title($episode_data['title'])) {
            $this->log("Episode title changed for $episode_key");
            return true;
        }

        $current_languages = array_keys($episode_data['languages']);
        $existing_languages = [];

        if (isset($existing_episode['episode_languages'])) {
            foreach ($existing_episode['episode_languages'] as $lang) {
                if (isset($lang['language_type'])) {
                    $existing_languages[] = $lang['language_type'];
                }
            }
        }

        sort($current_languages);
        sort($existing_languages);

        if ($current_languages !== $existing_languages) {
            $this->log("Episode languages changed for $episode_key");
            return true;
        }

        if ($this->should_update_incrementally($change_type)) {
            $this->log("No changes detected for $episode_key - skipping for incremental update");
            return false;
        }

        return false;
    }
    
    private function remove_episode_files($post_id, $season, $episode) {
        $episode_dir = $this->upload_dir . "post-$post_id/*/season_$season/episode_$episode/";
        $this->log("Removing old episode files from: $episode_dir");

        $language_dirs = glob($this->upload_dir . "post-$post_id/*/season_$season/episode_$episode/", GLOB_ONLYDIR);

        foreach ($language_dirs as $dir) {
            $files = glob($dir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            if (is_dir($dir)) {
                rmdir($dir);
            }
        }
    }

    private function detect_missing_and_new_episodes($post_id, $new_episodes) {
        $existing_dubbed = get_post_meta($post_id, 'scraper_dubbed_episodes', true) ?: [];
        $existing_subbed = get_post_meta($post_id, 'scraper_subbed_episodes', true) ?: [];

        $existing_all = array_merge($existing_dubbed, $existing_subbed);
        $existing_episodes = [];

        foreach ($existing_all as $episode) {
            if (isset($episode['season']) && isset($episode['episode'])) {
                $key = "S{$episode['season']}E{$episode['episode']}";
                $existing_episodes[$key] = $episode;
            }
        }

        $new_episode_keys = [];
        foreach ($new_episodes as $episode) {
            if (isset($episode['season']) && isset($episode['episode'])) {
                $key = "S{$episode['season']}E{$episode['episode']}";
                $new_episode_keys[$key] = $episode;
            }
        }

        $missing_episodes = array_diff_key($existing_episodes, $new_episode_keys);
        $new_episodes_only = array_diff_key($new_episode_keys, $existing_episodes);

        $this->log("Detected " . count($missing_episodes) . " missing episodes and " . count($new_episodes_only) . " new episodes");

        return [
            'missing' => $missing_episodes,
            'new' => $new_episodes_only,
            'existing' => $existing_episodes
        ];
    }

    private function should_update_incrementally($change_type) {
        return in_array($change_type, ['same_source', 'url_changed']);
    }


    
    protected function extract_season_episodes($config, $target_season = null) {
        $this->log("Extracting season episodes from config" . ($target_season ? " for season $target_season" : ""));
        
        $seasons_data = [];
        
        if (!isset($config['seasonList']) || !is_array($config['seasonList'])) {
            $this->log("No seasonList found in config");
            return [];
        }
        
        foreach ($config['seasonList'] as $season_id => $season_data) {
            if (!isset($season_data['name']) || !isset($season_data['epList'])) {
                continue;
            }
            
            $season_number = $this->extract_season_number($season_data['name']);
            
            if ($target_season && $season_number != $target_season) {
                $this->log("Skipping season $season_number (target: $target_season)");
                continue;
            }
            
            $this->log("Processing season $season_number with " . count($season_data['epList']) . " episodes");
            
            $episodes = [];
            foreach ($season_data['epList'] as $ep_id => $ep_data) {
                if (!isset($ep_data['name']) || !isset($ep_data['link'])) {
                    continue;
                }
                
                $episode_number = $this->extract_episode_number($ep_data['name']);
                $languages = $this->detect_episode_languages($ep_data['link']);
                
                if (empty($languages)) {
                    $this->log("No languages found for episode: {$ep_data['name']}");
                    continue;
                }
                
                $episodes[] = [
                    'season' => $season_number,
                    'episode' => $episode_number,
                    'title' => $ep_data['name'],
                    'languages' => $languages,
                    'ep_id' => $ep_id
                ];
            }
            
            if (!empty($episodes)) {
                $seasons_data[$season_number] = $episodes;
            }
        }
        
        return $seasons_data;
    }
    
    protected function detect_episode_languages($link_data) {
        return $this->detect_language_players($link_data);
    }

    /**
     * ตรวจสอบภาษาที่มีอยู่ในตอนก่อนหน้าของซีซั่นเดียวกัน
     * เพื่อใช้ในการจัดหมวดหมู่ตอนที่ไม่สามารถประมวลผลภาษาได้
     */
    protected function get_previous_episodes_languages($processed_episodes, $current_season, $current_episode) {
        $available_languages = [];

        foreach ($processed_episodes as $episode) {
            if ($episode['season'] == $current_season && $episode['episode'] < $current_episode) {
                if (isset($episode['languages'])) {
                    foreach ($episode['languages'] as $lang_type => $lang_data) {
                        if (!in_array($lang_type, $available_languages)) {
                            $available_languages[] = $lang_type;
                        }
                    }
                }
            }
        }

        $this->log("Found languages in previous episodes of Season $current_season: " . implode(', ', $available_languages));
        return $available_languages;
    }

    /**
     * สร้างตอนจำลองสำหรับตอนที่ไม่สามารถประมวลผลภาษาได้
     * โดยจัดหมวดหมู่ตามภาษาที่มีอยู่ในตอนก่อนหน้า
     */
    protected function create_fallback_episode($episode_data, $available_languages) {
        $episode_result = [
            'season' => $episode_data['season'],
            'episode' => $episode_data['episode'],
            'title' => $episode_data['title'],
            'languages' => [],
            'auto_categorized' => true
        ];

        $sample_original_url = $this->get_sample_original_url($episode_data['season'], $available_languages);
        foreach ($available_languages as $lang_type) {
            $episode_result['languages'][$lang_type] = [
                'files' => [
                    'master' => '',
                    'master_original_url' => $sample_original_url,
                    'resolutions' => []
                ],
                'player_url' => '',
                'group' => 0,
                'original_url' => $sample_original_url,
                'status' => 'auto_categorized'
            ];
        }

        return $episode_result;
    }

    private function get_sample_original_url($season, $available_languages) {
        $existing_dubbed = get_post_meta($this->post_id, 'scraper_dubbed_episodes', true) ?: [];
        $existing_subbed = get_post_meta($this->post_id, 'scraper_subbed_episodes', true) ?: [];
        foreach ($available_languages as $lang_type) {
            $episodes = ($lang_type === 'dubbed') ? $existing_dubbed : $existing_subbed;

            foreach ($episodes as $episode_key => $episode_data) {
                if (strpos($episode_key, "S{$season}E") === 0) {
                    if (!empty($episode_data['master_original_url'])) {
                        $this->log("Found sample original_url for auto-categorized episode: " . $episode_data['master_original_url']);
                        return $episode_data['master_original_url'];
                    }
                }
            }
        }

        $this->log("No sample original_url found for auto-categorized episode");
        return '';
    }

    protected function extract_season_number($season_name) {
        $patterns = [
            '/season\s*(\d+)/i',
            '/ซีซั่น\s*(\d+)/i',
            '/ปี\s*(\d+)/i',
            '/s(\d+)/i',
            '/(\d+)/'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $season_name, $matches)) {
                return intval($matches[1]);
            }
        }
        
        return 1;
    }
    
    protected function extract_episode_number($episode_name) {
        $patterns = [
            '/episode\s*(\d+)/i',
            '/ตอนที่\s*(\d+)/i',
            '/ep\s*(\d+)/i',
            '/e(\d+)/i',
            '/(\d+)/'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $episode_name, $matches)) {
                return intval($matches[1]);
            }
        }
        
        return 1;
    }
    
    protected function process_single_episode($post_id, $episode_data) {
        $season = $episode_data['season'];
        $episode = $episode_data['episode'];
        
        $this->log("Processing S{$season}E{$episode}: {$episode_data['title']}");
        
        $episode_result = [
            'season' => $season,
            'episode' => $episode,
            'title' => $episode_data['title'],
            'languages' => [],
            'ep_id' => $episode_data['ep_id'] ?? null
        ];
        
        $success_count = 0;
        
        foreach ($episode_data['languages'] as $lang_type => $players) {
            $this->log("Processing $lang_type for S{$season}E{$episode}");
            
            $lang_result = $this->process_episode_language($post_id, $season, $episode, $lang_type, $players);
            
            if ($lang_result && $lang_result !== 'HTTP_500_ERROR' && $lang_result !== 'HTTP_404_ERROR') {
                $episode_result['languages'][$lang_type] = $lang_result;
                $success_count++;
                $this->log("Successfully processed $lang_type for S{$season}E{$episode}");
            } else {
                $this->log("Failed to process $lang_type for S{$season}E{$episode}");
            }
        }
        
        if ($success_count > 0) {
            $this->log("Episode S{$season}E{$episode} processed with $success_count language(s)");
            return $episode_result;
        }
        
        $this->log("Episode S{$season}E{$episode} failed - no languages processed");
        return false;
    }
    
    protected function process_episode_language($post_id, $season, $episode, $lang_type, $players) {
        foreach ($players as $index => $player) {
            $this->log("Trying $lang_type player " . ($index + 1) . " for S{$season}E{$episode}: {$player['url']}");
            
            $m3u8_files = $this->extract_m3u8_files($player['url']);
            
            if ($m3u8_files === 'HTTP_500_ERROR' || $m3u8_files === 'HTTP_404_ERROR') {
                $this->log("HTTP error from $lang_type player " . ($index + 1) . " for S{$season}E{$episode}");
                continue;
            }
            
            if (empty($m3u8_files)) {
                $this->log("No M3U8 files from $lang_type player " . ($index + 1) . " for S{$season}E{$episode}");
                continue;
            }
            
            $saved_files = $this->save_episode_m3u8_files($post_id, $season, $episode, $lang_type, $m3u8_files);
            
            if ($saved_files) {
                $this->log("Successfully saved $lang_type files for S{$season}E{$episode}");
                
                return [
                    'files' => $saved_files,
                    'player_url' => $player['url'],
                    'group' => $player['group'],
                    'original_url' => $m3u8_files['master_original']['url'] ?? null
                ];
            }
        }
        
        return false;
    }
    
    protected function save_episode_m3u8_files($post_id, $season, $episode, $language, $m3u8_files) {
        $episode_dir = $this->upload_dir . "post-$post_id/$language/season_$season/episode_$episode/";
        
        if (!file_exists($episode_dir)) {
            wp_mkdir_p($episode_dir);
        }
        
        $saved_files = [];
        $base_path = "post-$post_id/$language/season_$season/episode_$episode/";
        
        if (isset($m3u8_files['master_original'])) {
            $master_file = $episode_dir . 'master_original.m3u8';
            file_put_contents($master_file, $m3u8_files['master_original']['content']);
            $saved_files['master_original'] = $this->base_url . $base_path . 'master_original.m3u8';
            $saved_files['master_original_url'] = $m3u8_files['master_original']['url'] ?? '';
            $this->log("Saved original master file for $language S{$season}E{$episode}");
        }
        
        if (isset($m3u8_files['resolutions'])) {
            foreach ($m3u8_files['resolutions'] as $resolution => $data) {
                $filename = $data['filename'];
                $resolution_file = $episode_dir . $filename;
                file_put_contents($resolution_file, $data['content']);
                $saved_files['resolutions'][$resolution] = $this->base_url . $base_path . $filename;
                $this->log("Saved resolution '$resolution' for $language S{$season}E{$episode}");
            }
        }
        
        if (isset($m3u8_files['master_original'])) {
            $custom_master = $this->create_custom_master($m3u8_files['master_original']['content'], $saved_files['resolutions'] ?? []);
            $custom_master_file = $episode_dir . 'master.m3u8';
            file_put_contents($custom_master_file, $custom_master);
            $saved_files['master'] = $this->base_url . $base_path . 'master.m3u8';
            $this->log("Created custom master file for $language S{$season}E{$episode}");
        }
        
        return $saved_files;
    }
    
    protected function save_episodes_hybrid($post_id, $processed_episodes, $target_season = null) {
        $this->log("Saving episodes in hybrid format (new S2E1 + old index 0,1,2) for Season " . ($target_season ?: 'all'));
        
        $existing_dubbed = get_post_meta($post_id, 'scraper_dubbed_episodes', true) ?: [];
        $existing_subbed = get_post_meta($post_id, 'scraper_subbed_episodes', true) ?: [];
        $existing_dubbed_original = get_post_meta($post_id, 'scraper_dubbed_original_url', true) ?: [];
        $existing_subbed_original = get_post_meta($post_id, 'scraper_subbed_original_url', true) ?: [];
        $existing_dubbed_master = get_post_meta($post_id, 'scraper_dubbed_master_url', true) ?: [];
        $existing_subbed_master = get_post_meta($post_id, 'scraper_subbed_master_url', true) ?: [];
        $existing_dubbed_files = get_post_meta($post_id, 'scraper_dubbed_files', true) ?: [];
        $existing_subbed_files = get_post_meta($post_id, 'scraper_subbed_files', true) ?: [];
        
        if ($target_season) {
            $existing_dubbed = $this->filter_episodes_by_season_s2e1($existing_dubbed, $target_season, false);
            $existing_subbed = $this->filter_episodes_by_season_s2e1($existing_subbed, $target_season, false);
            $existing_dubbed_original = $this->filter_episodes_by_season_index($existing_dubbed_original, $target_season, false);
            $existing_subbed_original = $this->filter_episodes_by_season_index($existing_subbed_original, $target_season, false);
            $existing_dubbed_master = $this->filter_episodes_by_season_index($existing_dubbed_master, $target_season, false);
            $existing_subbed_master = $this->filter_episodes_by_season_index($existing_subbed_master, $target_season, false);
            $existing_dubbed_files = $this->filter_episodes_by_season_index($existing_dubbed_files, $target_season, false);
            $existing_subbed_files = $this->filter_episodes_by_season_index($existing_subbed_files, $target_season, false);
        }
        
        $new_dubbed_episodes = [];
        $new_subbed_episodes = [];
        $new_dubbed_original = [];
        $new_subbed_original = [];
        $new_dubbed_master = [];
        $new_subbed_master = [];
        $new_dubbed_files = [];
        $new_subbed_files = [];

        $max_episode_num = 0;
        foreach ($processed_episodes as $episode) {
            $max_episode_num = max($max_episode_num, $episode['episode']);
        }

        for ($i = 1; $i <= $max_episode_num; $i++) {
            $new_dubbed_original[$i - 1] = '';
            $new_subbed_original[$i - 1] = '';
            $new_dubbed_master[$i - 1] = '';
            $new_subbed_master[$i - 1] = '';
            $new_dubbed_files[$i - 1] = [];
            $new_subbed_files[$i - 1] = [];
        }
        
        foreach ($processed_episodes as $episode) {
            $episode_key = "S{$episode['season']}E{$episode['episode']}";
            $episode_title = $this->clean_episode_title($episode['title']);
            $episode_duration = null;
            $episode_description = $this->generate_episode_description($episode);
            $date_added = current_time('Y-m-d H:i:s');
            
            foreach ($episode['languages'] as $lang_type => $lang_data) {
                $episode_entry = [
                    'season' => $episode['season'],
                    'episode' => $episode['episode'],
                    'title' => $episode_title,
                    'duration' => $episode_duration,
                    'quality' => $this->determine_video_quality([$lang_type => $lang_data]),
                    'status' => 'available',
                    'date_added' => $date_added,
                    'description' => $episode_description,
                    'master_url' => $lang_data['files']['master'] ?? '',
                    'master_original_url' => $lang_data['original_url'] ?? ($lang_data['files']['master_original_url'] ?? ''),
                    'resolutions' => $lang_data['files']['resolutions'] ?? [],
                    'file_size' => $this->calculate_file_size($lang_data['files'] ?? []),
                    'video_codec' => 'H.264',
                    'audio_codec' => 'AAC'
                ];
                
                $compatibility_entry = [
                    'season' => $episode['season'],
                    'episode' => $episode['episode'],
                    'title' => $episode_title,
                    'files' => $lang_data['files'] ?? [],
                    'player_url' => $lang_data['player_url'] ?? '',
                    'original_url' => $lang_data['original_url'] ?? ''
                ];
                
                $episode_index = $episode['episode'] - 1;

                if ($lang_type === 'dubbed') {
                    $new_dubbed_episodes[$episode_key] = $episode_entry;
                    $new_dubbed_original[$episode_index] = $lang_data['original_url'] ?? ($lang_data['files']['master_original_url'] ?? '');
                    $new_dubbed_master[$episode_index] = $lang_data['files']['master'] ?? '';
                    $new_dubbed_files[$episode_index] = $compatibility_entry;

                    $auto_categorized = isset($episode['auto_categorized']) && $episode['auto_categorized'];
                    $log_message = "Added dubbed episode: $episode_key (index: $episode_index)";
                    if ($auto_categorized) {
                        $log_message .= " [AUTO-CATEGORIZED]";
                    }
                    $this->log($log_message);

                } elseif ($lang_type === 'subbed') {
                    $new_subbed_episodes[$episode_key] = $episode_entry;
                    $new_subbed_original[$episode_index] = $lang_data['original_url'] ?? ($lang_data['files']['master_original_url'] ?? '');
                    $new_subbed_master[$episode_index] = $lang_data['files']['master'] ?? '';
                    $new_subbed_files[$episode_index] = $compatibility_entry;

                    $auto_categorized = isset($episode['auto_categorized']) && $episode['auto_categorized'];
                    $log_message = "Added subbed episode: $episode_key (index: $episode_index)";
                    if ($auto_categorized) {
                        $log_message .= " [AUTO-CATEGORIZED]";
                    }
                    $this->log($log_message);
                }
            }
        }
        
        $this->apply_smart_episode_merging($new_dubbed_episodes, $new_subbed_episodes, $new_dubbed_original, $new_subbed_original, $new_dubbed_master, $new_subbed_master, $new_dubbed_files, $new_subbed_files);

        $final_dubbed_episodes = array_merge($existing_dubbed, $new_dubbed_episodes);
        $final_subbed_episodes = array_merge($existing_subbed, $new_subbed_episodes);
        $final_dubbed_original = $new_dubbed_original;
        $final_subbed_original = $new_subbed_original;
        $final_dubbed_master = $new_dubbed_master;
        $final_subbed_master = $new_subbed_master;
        $final_dubbed_files = $new_dubbed_files;
        $final_subbed_files = $new_subbed_files;
        
        update_post_meta($post_id, 'scraper_dubbed_episodes', $final_dubbed_episodes);
        update_post_meta($post_id, 'scraper_subbed_episodes', $final_subbed_episodes);
        update_post_meta($post_id, 'scraper_dubbed_original_url', $final_dubbed_original);
        update_post_meta($post_id, 'scraper_subbed_original_url', $final_subbed_original);
        update_post_meta($post_id, 'scraper_dubbed_master_url', $final_dubbed_master);
        update_post_meta($post_id, 'scraper_subbed_master_url', $final_subbed_master);
        update_post_meta($post_id, 'scraper_dubbed_files', $final_dubbed_files);
        update_post_meta($post_id, 'scraper_subbed_files', $final_subbed_files);

        $this->log("Original URLs saved - Dubbed: " . count(array_filter($final_dubbed_original)) . "/" . count($final_dubbed_original) . " episodes");
        $this->log("Original URLs saved - Subbed: " . count(array_filter($final_subbed_original)) . "/" . count($final_subbed_original) . " episodes");
        
        $series_info = [
            'total_seasons' => $this->calculate_total_seasons($final_dubbed_episodes, $final_subbed_episodes),
            'current_season' => $target_season ?: $this->get_latest_season($final_dubbed_episodes, $final_subbed_episodes),
            'last_updated' => current_time('Y-m-d H:i:s'),
            'scraping_status' => 'completed',
            'scraping_message' => $this->generate_completion_message($processed_episodes, $target_season)
        ];
        
        $this->add_episode_counts_to_series_info($series_info, $final_dubbed_episodes, $final_subbed_episodes);
        update_post_meta($post_id, 'scraper_series_info', $series_info);

        $auto_categorized_count = 0;
        foreach ($processed_episodes as $episode) {
            if (isset($episode['auto_categorized']) && $episode['auto_categorized']) {
                $auto_categorized_count++;
            }
        }

        $this->log("Hybrid save completed:");
        $this->log("- NEW FORMAT: Dubbed episodes: " . count($final_dubbed_episodes) . ", Subbed episodes: " . count($final_subbed_episodes));
        $this->log("- OLD FORMAT: Dubbed indices: " . count($final_dubbed_original) . ", Subbed indices: " . count($final_subbed_original));
        if ($auto_categorized_count > 0) {
            $this->log("- AUTO-CATEGORIZED: $auto_categorized_count episodes were automatically categorized due to missing language data");
        }
        
        $season_text = $target_season ? "Season $target_season" : "all seasons";
        $this->update_scraping_status($post_id, 'completed',
            "Successfully processed " . count($processed_episodes) . " episodes for $season_text");
    }



    private function apply_smart_episode_merging(&$dubbed_episodes, &$subbed_episodes, &$dubbed_original = null, &$subbed_original = null, &$dubbed_master = null, &$subbed_master = null, &$dubbed_files = null, &$subbed_files = null) {
        if (empty($dubbed_episodes) && empty($subbed_episodes)) {
            return;
        }
        $dubbed_episode_numbers = $this->extract_episode_numbers($dubbed_episodes);
        $subbed_episode_numbers = $this->extract_episode_numbers($subbed_episodes);
        $dubbed_starts_with_1 = !empty($dubbed_episode_numbers) && min($dubbed_episode_numbers) == 1;
        $subbed_starts_with_1 = !empty($subbed_episode_numbers) && min($subbed_episode_numbers) == 1;
        $dubbed_count = count($dubbed_episodes);
        $subbed_count = count($subbed_episodes);
        $should_merge_to_subbed = !$dubbed_starts_with_1 && $dubbed_count < $subbed_count && $subbed_starts_with_1;
        $should_merge_to_dubbed = !$subbed_starts_with_1 && $subbed_count < $dubbed_count && $dubbed_starts_with_1;
        if ($should_merge_to_subbed) {
            $subbed_episodes = array_merge($subbed_episodes, $dubbed_episodes);
            $dubbed_episodes = [];
            if ($dubbed_original !== null && $subbed_original !== null) {
                $subbed_original = array_merge($subbed_original, $dubbed_original);
                $dubbed_original = [];
            }
            if ($dubbed_master !== null && $subbed_master !== null) {
                $subbed_master = array_merge($subbed_master, $dubbed_master);
                $dubbed_master = [];
            }
            if ($dubbed_files !== null && $subbed_files !== null) {
                $subbed_files = array_merge($subbed_files, $dubbed_files);
                $dubbed_files = [];
            }
            $this->log("Smart merge: Moved " . $dubbed_count . " dubbed episodes to subbed group");
        } elseif ($should_merge_to_dubbed) {
            $dubbed_episodes = array_merge($dubbed_episodes, $subbed_episodes);
            $subbed_episodes = [];
            if ($dubbed_original !== null && $subbed_original !== null) {
                $dubbed_original = array_merge($dubbed_original, $subbed_original);
                $subbed_original = [];
            }
            if ($dubbed_master !== null && $subbed_master !== null) {
                $dubbed_master = array_merge($dubbed_master, $subbed_master);
                $subbed_master = [];
            }
            if ($dubbed_files !== null && $subbed_files !== null) {
                $dubbed_files = array_merge($dubbed_files, $subbed_files);
                $subbed_files = [];
            }
            $this->log("Smart merge: Moved " . $subbed_count . " subbed episodes to dubbed group");
        }
    }

    private function extract_episode_numbers($episodes) {
        $numbers = [];
        foreach ($episodes as $key => $episode) {
            if (preg_match('/^S(\d+)E(\d+)$/', $key, $matches)) {
                $numbers[] = intval($matches[2]);
            }
        }
        return $numbers;
    }
    
    private function filter_episodes_by_season_s2e1($episodes_data, $target_season, $keep_target = true) {
        if (!is_array($episodes_data)) {
            return [];
        }
        
        $filtered = [];
        foreach ($episodes_data as $key => $episode) {
            if (preg_match('/^S(\d+)E(\d+)$/', $key, $matches)) {
                $season = intval($matches[1]);
                if (($keep_target && $season == $target_season) || (!$keep_target && $season != $target_season)) {
                    $filtered[$key] = $episode;
                }
            } elseif (is_array($episode) && isset($episode['season'])) {
                $season = intval($episode['season']);
                if (($keep_target && $season == $target_season) || (!$keep_target && $season != $target_season)) {
                    $filtered[$key] = $episode;
                }
            }
        }
        
        return $filtered;
    }
    
    private function filter_episodes_by_season_index($episodes_data, $target_season, $keep_target = true) {
        if (!is_array($episodes_data)) {
            return [];
        }
        
        $filtered = [];
        foreach ($episodes_data as $index => $episode) {
            if (is_array($episode) && isset($episode['season'])) {
                $season = intval($episode['season']);
                if (($keep_target && $season == $target_season) || (!$keep_target && $season != $target_season)) {
                    $filtered[$index] = $episode;
                }
            } else {
                if (!$keep_target) {
                    $filtered[$index] = $episode;
                }
            }
        }
        
        return $filtered;
    }
    
    protected function clean_episode_title($title) {
        $title = trim($title);
        $title = preg_replace('/^(ตอนที่|Episode|EP|E)\s*\d+\s*:?\s*/i', '', $title);
        $title = preg_replace('/\s+/', ' ', $title);
        return $title ?: 'Episode';
    }
    
    
    protected function generate_episode_description($episode) {
        $description = "Episode {$episode['episode']}";
        if ($episode['season'] > 1) {
            $description = "Season {$episode['season']}, " . $description;
        }
        
        $language_count = count($episode['languages']);
        if ($language_count > 0) {
            $languages = array_keys($episode['languages']);
            $lang_names = array_map(function($lang) {
                return $this->language_mappings[$lang] ?? $lang;
            }, $languages);
            $description .= " - Available in: " . implode(', ', $lang_names);
        }
        
        return $description;
    }
    
    protected function determine_video_quality($languages) {
        $quality_indicators = [];
        
        foreach ($languages as $lang_data) {
            if (isset($lang_data['files']['resolutions'])) {
                foreach ($lang_data['files']['resolutions'] as $resolution) {
                    if (is_array($resolution) && isset($resolution['resolution'])) {
                        $quality_indicators[] = $resolution['resolution'];
                    }
                }
            }
        }
        
        if (in_array('1080p', $quality_indicators)) return 'HD 1080p';
        if (in_array('720p', $quality_indicators)) return 'HD 720p';
        if (in_array('480p', $quality_indicators)) return 'SD 480p';
        if (in_array('360p', $quality_indicators)) return 'SD 360p';
        
        return 'Standard';
    }
    
    protected function calculate_file_size($files) {
        if (empty($files)) return 'Unknown';
        
        $total_size = 0;
        $size_count = 0;
        
        foreach ($files as $file_data) {
            if (is_array($file_data) && isset($file_data['file_size'])) {
                $total_size += intval($file_data['file_size']);
                $size_count++;
            }
        }
        
        if ($size_count > 0) {
            $avg_size = $total_size / $size_count;
            return $this->format_file_size($avg_size);
        }
        
        return 'Estimated 200-500 MB';
    }
    
    protected function format_file_size($bytes) {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        }
        return $bytes . ' bytes';
    }
    
    protected function fallback_episode_detection($html, $target_season = null) {
        $this->log("Attempting fallback episode detection");
        
        $episodes_data = $this->detect_episodes_and_seasons($html, $target_season);
        
        if (empty($episodes_data)) {
            return false;
        }
        
        return [
            'fallback_mode' => true,
            'episodes' => $episodes_data
        ];
    }
    
    protected function detect_episodes_and_seasons($html, $target_season = null) {
        $this->log("Detecting episodes and seasons from HTML");
        
        $episodes = [];
        $seasons = [];
        $episode_weights = [];
        
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($html);
        libxml_clear_errors();
        
        $xpath = new DOMXPath($dom);
        
        $episode_elements = $xpath->query("//div[contains(@class, 'episode') or contains(@class, 'ep-')]");
        
        foreach ($episode_elements as $element) {
            $text = trim($element->textContent);
            $html_content = $dom->saveHTML($element);
            
            $episode_matches = [];
            $season_matches = [];
            $total_weight = 0;
            
            foreach ($this->detection_patterns['episodes'] as $confidence => $pattern_data) {
                foreach ($pattern_data['patterns'] as $pattern) {
                    if (preg_match($pattern, $text, $matches)) {
                        $episode_num = intval($matches[1]);
                        $episode_matches[] = [
                            'number' => $episode_num,
                            'weight' => $pattern_data['weight'],
                            'confidence' => $confidence
                        ];
                        $total_weight += $pattern_data['weight'];
                    }
                }
            }
            
            foreach ($this->detection_patterns['seasons'] as $confidence => $pattern_data) {
                foreach ($pattern_data['patterns'] as $pattern) {
                    if (preg_match($pattern, $text, $matches)) {
                        $season_num = intval($matches[1]);
                        $season_matches[] = [
                            'number' => $season_num,
                            'weight' => $pattern_data['weight'],
                            'confidence' => $confidence
                        ];
                    }
                }
            }
            
            if (!empty($episode_matches)) {
                $best_episode = $episode_matches[0];
                $season_num = !empty($season_matches) ? $season_matches[0]['number'] : 1;
                
                if ($target_season && $season_num != $target_season) {
                    continue;
                }
                
                $episode_key = "S{$season_num}E{$best_episode['number']}";
                
                if (!isset($episodes[$episode_key])) {
                    $episodes[$episode_key] = [
                        'season' => $season_num,
                        'episode' => $best_episode['number'],
                        'title' => $text,
                        'languages' => []
                    ];
                }
                
                $language_data = $this->extract_language_players_from_element($element);
                if (!empty($language_data)) {
                    $episodes[$episode_key]['languages'] = array_merge(
                        $episodes[$episode_key]['languages'],
                        $language_data
                    );
                }
                
                $seasons[$season_num] = true;
                $episode_weights[$episode_key] = $total_weight;
            }
        }
        
        $confidence = $this->calculate_detection_confidence($episodes, $seasons, $episode_weights);
        $this->log("Fallback detection confidence: $confidence%");
        
        if ($confidence < 60) {
            $this->log("Confidence too low for fallback detection");
            return [];
        }
        
        return array_values($episodes);
    }
    
    protected function extract_language_players_from_element($element) {
        $languages = [];
        
        $links = $element->getElementsByTagName('a');
        foreach ($links as $link) {
            $href = $link->getAttribute('href');
            $text = trim($link->textContent);
            
            if (empty($href)) continue;
            
            $lang_type = 'dubbed';
            if (preg_match('/sub|subtitle|ซับ/i', $text)) {
                $lang_type = 'subbed';
            }
            
            if (!isset($languages[$lang_type])) {
                $languages[$lang_type] = [];
            }
            
            $languages[$lang_type][] = [
                'url' => $href,
                'group' => 0,
                'type' => 'embed',
                'sound' => $lang_type,
                'id' => null
            ];
        }
        
        return $languages;
    }
    
    protected function calculate_detection_confidence($episodes, $seasons, $episode_weights) {
        $score = 0;
        
        if (!empty($episodes)) {
            $score += 30;
        }
        
        if (count($episodes) >= 6) {
            $score += 20;
        }
        
        if (!empty($seasons)) {
            $score += 20;
        }
        
        if (!empty($episode_weights) && max($episode_weights) >= 8) {
            $score += 10;
        }
        
        if (in_array(1, $episodes)) {
            $score += 20;
        }
        
        return min(100, $score);
    }
    
    protected function process_fallback_episodes($post_id, $config, $target_season = null) {
        $this->log("Processing fallback episodes for post ID: $post_id");
        
        $episodes_data = $config['episodes'];
        $processed_episodes = [];
        $total_episodes = count($episodes_data);
        $successful_episodes = 0;
        
        foreach ($episodes_data as $episode_data) {
            $this->log("Processing fallback episode S{$episode_data['season']}E{$episode_data['episode']}");
            
            $episode_result = $this->process_single_episode($post_id, $episode_data);
            
            if ($episode_result) {
                $processed_episodes[] = $episode_result;
                $successful_episodes++;
                $this->log("Successfully processed fallback S{$episode_data['season']}E{$episode_data['episode']}");
            } else {
                $this->log("Failed to process fallback S{$episode_data['season']}E{$episode_data['episode']}");
            }
        }
        
        if ($successful_episodes > 0) {
            $this->save_episodes_hybrid($post_id, $processed_episodes, $target_season);
            $this->log("Fallback processing completed: $successful_episodes/$total_episodes episodes");
            return true;
        }
        
        $this->log("Fallback processing failed - no episodes processed");
        return false;
    }
    
    public function test_series_detection($url) {
        try {
            $html = $this->fetch_html($url);
            if (!$html) {
                return [
                    'success' => false,
                    'error' => 'Could not fetch HTML from URL'
                ];
            }
            
            $main_player_url = $this->extract_main_player($html);
            if (!$main_player_url) {
                return [
                    'success' => false,
                    'error' => 'No main player found'
                ];
            }
            
            $player_html = $this->fetch_html($main_player_url);
            if (!$player_html) {
                return [
                    'success' => false,
                    'error' => 'Could not fetch player HTML'
                ];
            }
            
            $config = $this->extract_config($player_html);
            
            if ($config) {
                $seasons_data = $this->extract_season_episodes($config);
                
                return [
                    'success' => true,
                    'detection_type' => 'config',
                    'seasons_found' => count($seasons_data),
                    'seasons_data' => $seasons_data,
                    'player_url' => $main_player_url
                ];
            } else {
                $fallback_config = $this->fallback_episode_detection($player_html);
                
                if ($fallback_config) {
                    return [
                        'success' => true,
                        'detection_type' => 'fallback',
                        'episodes_found' => count($fallback_config['episodes']),
                        'episodes' => $fallback_config['episodes'],
                        'confidence' => $fallback_config['confidence'],
                        'player_url' => $main_player_url
                    ];
                }
            }
            
            return [
                'success' => false,
                'error' => 'No series data could be detected'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
    
    public function clear_scraping_data($post_id) {
        try {
            $this->log("Public method: Starting clear scraping data for post ID: $post_id");
            parent::clear_scraping_data($post_id);
            $this->log("Successfully cleared series scraping data for post ID: $post_id");
            return [
                'success' => true,
                'message' => 'Series scraping data cleared successfully'
            ];
            
        } catch (Exception $e) {
            $this->log("ERROR: Failed to clear series scraping data: " . $e->getMessage());
            
            return [
                'success' => false,
                'error' => 'Failed to clear series scraping data: ' . $e->getMessage()
            ];
        }
    }
    
    private function calculate_total_seasons($dubbed_episodes, $subbed_episodes) {
        $seasons = [];
        
        foreach ($dubbed_episodes as $key => $episode) {
            if (preg_match('/^S(\d+)E(\d+)$/', $key, $matches)) {
                $seasons[] = intval($matches[1]);
            } elseif (is_array($episode) && isset($episode['season'])) {
                $seasons[] = intval($episode['season']);
            }
        }
        
        foreach ($subbed_episodes as $key => $episode) {
            if (preg_match('/^S(\d+)E(\d+)$/', $key, $matches)) {
                $seasons[] = intval($matches[1]);
            } elseif (is_array($episode) && isset($episode['season'])) {
                $seasons[] = intval($episode['season']);
            }
        }
        
        return empty($seasons) ? 1 : max($seasons);
    }
    
    private function get_latest_season($dubbed_episodes, $subbed_episodes) {
        return $this->calculate_total_seasons($dubbed_episodes, $subbed_episodes);
    }
    
    private function generate_completion_message($processed_episodes, $target_season) {
        $episode_count = count($processed_episodes);
        $season_text = $target_season ? "Season $target_season" : "all seasons";
        
        $dubbed_count = 0;
        $subbed_count = 0;
        
        foreach ($processed_episodes as $episode) {
            if (isset($episode['languages']['dubbed'])) {
                $dubbed_count++;
            }
            if (isset($episode['languages']['subbed'])) {
                $subbed_count++;
            }
        }
        
        $message = "Successfully processed $episode_count episodes for $season_text";
        if ($dubbed_count > 0) {
            $message .= " ($dubbed_count dubbed";
            if ($subbed_count > 0) {
                $message .= ", $subbed_count subbed)";
            } else {
                $message .= ")";
            }
        } elseif ($subbed_count > 0) {
            $message .= " ($subbed_count subbed)";
        }
        
        return $message;
    }
    
    private function add_episode_counts_to_series_info(&$series_info, $dubbed_episodes, $subbed_episodes) {
        $series_info['total_dubbed_episodes'] = count($dubbed_episodes);
        $series_info['total_subbed_episodes'] = count($subbed_episodes);
        $series_info['total_episodes'] = max(count($dubbed_episodes), count($subbed_episodes));
        
        $seasons_dubbed = [];
        $seasons_subbed = [];
        
        foreach ($dubbed_episodes as $key => $episode) {
            if (preg_match('/^S(\d+)E(\d+)$/', $key, $matches)) {
                $season = intval($matches[1]);
                if (!isset($seasons_dubbed[$season])) {
                    $seasons_dubbed[$season] = 0;
                }
                $seasons_dubbed[$season]++;
            }
        }
        
        foreach ($subbed_episodes as $key => $episode) {
            if (preg_match('/^S(\d+)E(\d+)$/', $key, $matches)) {
                $season = intval($matches[1]);
                if (!isset($seasons_subbed[$season])) {
                    $seasons_subbed[$season] = 0;
                }
                $seasons_subbed[$season]++;
            }
        }
        
        $series_info['seasons_dubbed'] = $seasons_dubbed;
        $series_info['seasons_subbed'] = $seasons_subbed;
    }
}

class SeriedayHDScraper extends SeriesScraper {

    private $post_id;

    public function scrape_serieday($post_id, $series_url, $options = []) {
        try {
            $this->post_id = $post_id;
            $progress = ScrapingProgressManager::getInstance();
            
            $this->log("Starting SeriedayHD scraping for post ID: $post_id, URL: $series_url");
            $progress->updateProgress('fetch_html', 20, 'กำลังดาวน์โหลดหน้าเว็บ SeriedayHD');

            $html = $this->fetch_html($series_url);
            if (!$html) {
                $error_msg = 'Could not fetch HTML from URL: ' . $series_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_html');
                return false;
            }
            $progress->completeStep('fetch_html');

            $progress->updateProgress('extract_metadata', 30, 'กำลังดึงข้อมูลซีรีย์ (ชื่อ, คะแนน, โปสเตอร์)');
            $this->extract_serieday_metadata($post_id, $html, $series_url);
            $this->log("Extracted SeriedayHD metadata");
            $progress->completeStep('extract_metadata');

            $progress->updateProgress('detect_season', 40, 'กำลังค้นหา ID ของซีรีย์');
            $post_id_match = $this->extract_post_id_from_html($html);
            if (!$post_id_match) {
                $error_msg = 'Could not extract post ID from HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'detect_season');
                return false;
            }

            $progress->updateProgress('detect_season', 70, 'กำลังตรวจสอบซีซั่น');
            $target_season = $this->detect_target_season($post_id, $options, $series_url);
            $this->log("Target season: $target_season");
            $progress->completeStep('detect_season');

            $progress->updateProgress('find_player', 50, 'กำลังค้นหาตอนทั้งหมด');
            $episodes_data = $this->extract_all_episodes($post_id_match, $target_season, $series_url);
            if (empty($episodes_data)) {
                $error_msg = 'No episodes found for the series';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'find_player');
                return false;
            }
            $progress->completeStep('find_player');

            $result = $this->process_serieday_episodes($post_id, $episodes_data, $target_season);

            if ($result) {
                $this->extract_episode_duration($post_id, $episodes_data);
            }

            return $result;

        } catch (Exception $e) {
            $error_msg = 'SeriedayHD scraping exception: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }

    private function extract_post_id_from_html($html) {
        $patterns = [
            '/"post_id":\s*(\d+)/',
            '/postid["\']?\s*[:=]\s*["\']?(\d+)["\']?/',
            '/post_id["\']?\s*[:=]\s*["\']?(\d+)["\']?/',
            '/data-postid["\']?\s*[:=]\s*["\']?(\d+)["\']?/',
            '/halim_ajax_player.*postid["\']?\s*[:=]\s*["\']?(\d+)["\']?/',
            '/wp-json\/wp\/v2\/posts\/(\d+)/',
            '/post-(\d+)/',
            '/"postID":\s*"?(\d+)"?/',
            '/postID\s*=\s*["\']?(\d+)["\']?/',
            '/var\s+postid\s*=\s*["\']?(\d+)["\']?/',
            '/window\.postid\s*=\s*["\']?(\d+)["\']?/',
            '/halim_cfg\s*=\s*{[^}]*"post_id":\s*(\d+)/',
            '/"post_id":\s*"?(\d+)"?[^}]*}/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $post_id = intval($matches[1]);
                $this->log("Extracted post ID: $post_id using pattern: $pattern");
                return $post_id;
            }
        }

        $this->log("No post ID found with any pattern. HTML length: " . strlen($html));
        return false;
    }

    private function extract_serieday_metadata($post_id, $html, $series_url) {
        $this->log("Extracting SeriedayHD metadata for post ID: $post_id");

        $title = $this->extract_serieday_title($html);
        if ($title) {
            wp_update_post([
                'ID' => $post_id,
                'post_title' => $title
            ]);
            $this->log("Title extracted and saved: $title");
        }

        $imdb_rating = $this->extract_serieday_imdb_rating($html);
        if ($imdb_rating) {
            update_post_meta($post_id, 'imdb_rating', $imdb_rating);
            $this->log("IMDb rating saved: $imdb_rating");
        }

        $youtube_id = $this->extract_serieday_youtube_id($html);
        if ($youtube_id) {
            update_post_meta($post_id, 'linkvideo', $youtube_id);
            $this->log("YouTube ID saved: $youtube_id");
        }

        $poster_url = $this->extract_serieday_poster($html);
        if ($poster_url) {
            update_post_meta($post_id, '_featured_image_url', $poster_url);
            $this->log("Poster URL saved: $poster_url");
        }

        $this->log("SeriedayHD metadata extraction completed");
    }

    private function extract_serieday_title($html) {
        $patterns = [
            '/<meta[^>]*property=["\']og:title["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
            '/<title>([^<]+)<\/title>/i',
            '/<h1[^>]*class=["\'][^"\']*entry-title[^"\']*["\'][^>]*>([^<]+)<\/h1>/i',
            '/<h1[^>]*>([^<]+)<\/h1>/i'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $title = trim(html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8'));
                if (!empty($title) && $title !== 'Auto Draft') {
                    $this->log("Title extracted: $title using pattern: $pattern");
                    return $title;
                }
            }
        }

        $this->log("No title found for SeriedayHD");
        return false;
    }

    private function extract_serieday_imdb_rating($html) {
        $patterns = [
            '/alt="[^"]*IMDb[^"]*"[^>]*>(\d+\.?\d*)/i',
            '/IMDb[^"]*"[^>]*>(\d+\.?\d*)/i',
            '/<span[^>]*class=["\'][^"\']*score[^"\']*["\'][^>]*>.*?<noscript>.*?<\/noscript>(\d+\.?\d*)/s',
            '/<span[^>]*class=["\'][^"\']*score[^"\']*["\'][^>]*>.*?data-ll-status="loaded">(\d+\.?\d*)/s',
            '/<span[^>]*class=["\'][^"\']*score[^"\']*["\'][^>]*>.*?>(\d+\.?\d*)\s*<!--/s',
            '/<span[^>]*class=["\'][^"\']*score[^"\']*["\'][^>]*>.*?>(\d+\.?\d*)\s*<\/span>/s',
            '/IMDb[:\s]*(\d+\.?\d*)[\/\s]*10/',
            '/rating["\']?\s*[:=]\s*["\']?(\d+\.?\d*)["\']?/',
            '/score["\']?\s*[:=]\s*["\']?(\d+\.?\d*)["\']?/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $rating = floatval($matches[1]);
                if ($rating >= 1.0 && $rating <= 10.0) {
                    $this->log("IMDb rating extracted: $rating using pattern: $pattern");
                    return $rating;
                }
            }
        }

        $this->log("No IMDb rating found for SeriedayHD");
        return false;
    }

    private function extract_serieday_youtube_id($html) {
        $this->log("Searching for YouTube ID in HTML content");

        $patterns = [
            '/img\.youtube\.com\/vi\/([a-zA-Z0-9_-]+)\//',
            '/youtube\.com\/vi\/([a-zA-Z0-9_-]+)\//',
            '/iframe[^>]*src=["\'][^"\']*youtube\.com\/embed\/([a-zA-Z0-9_-]+)["\']/',
            '/youtube\.com\/embed\/([a-zA-Z0-9_-]+)/',
            '/youtube\.com\/watch\?v=([a-zA-Z0-9_-]+)/',
            '/youtu\.be\/([a-zA-Z0-9_-]+)/',
            '/data-video-id=["\']([a-zA-Z0-9_-]+)["\']/',
            '/youtube-id["\']?\s*[:=]\s*["\']?([a-zA-Z0-9_-]+)["\']?/',
            '/trailer[^>]*youtube[^>]*["\']([a-zA-Z0-9_-]+)["\']/',
            '/"video_id":\s*["\']([a-zA-Z0-9_-]+)["\']/',
            '/youtube[^"\']*["\']([a-zA-Z0-9_-]{11})["\']/',
            '/v=([a-zA-Z0-9_-]{11})/',
            '/embed\/([a-zA-Z0-9_-]{11})/',
            '/<div[^>]*class=["\'][^"\']*trailer[^"\']*["\'][^>]*>.*?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/s',
            '/<section[^>]*class=["\'][^"\']*trailer[^"\']*["\'][^>]*>.*?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/s',
            '/trailer.*?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/',
            '/preview.*?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/',
            '/teaser.*?youtube\.com\/embed\/([a-zA-Z0-9_-]+)/'
        ];

        foreach ($patterns as $i => $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $youtube_id = $matches[1];
                if (preg_match('/^[a-zA-Z0-9_-]{10,12}$/', $youtube_id)) {
                    $this->log("YouTube ID extracted: $youtube_id using pattern " . ($i + 1) . ": $pattern");
                    return $youtube_id;
                } else {
                    $this->log("Pattern " . ($i + 1) . " matched but YouTube ID format invalid: $youtube_id");
                }
            }
        }

        $this->log("No YouTube ID found for SeriedayHD");
        return false;
    }

    private function extract_serieday_poster($html) {
        $patterns = [
            '/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
            '/<meta[^>]*name=["\']twitter:image["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*class=["\'][^"\']*poster[^"\']*["\'][^>]*src=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*class=["\'][^"\']*cover[^"\']*["\'][^>]*src=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*class=["\'][^"\']*thumbnail[^"\']*["\'][^>]*src=["\']([^"\']+)["\'][^>]*>/i',
            '/<img[^>]*src=["\']([^"\']+)["\'][^>]*class=["\'][^"\']*poster[^"\']*["\']>/i',
            '/<div[^>]*class=["\'][^"\']*poster[^"\']*["\'][^>]*><img[^>]*src=["\']([^"\']+)["\']/',
            '/poster["\']?\s*[:=]\s*["\']([^"\']+)["\']/',
            '/image["\']?\s*[:=]\s*["\']([^"\']+\.(?:jpg|jpeg|png|webp))["\']/'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $poster_url = $matches[1];
                if (preg_match('/\.(jpg|jpeg|png|webp)$/i', $poster_url) && (filter_var($poster_url, FILTER_VALIDATE_URL) || strpos($poster_url, 'http') === 0)) {
                    $this->log("Poster image extracted: $poster_url using pattern: $pattern");
                    return $poster_url;
                }
            }
        }

        $this->log("No poster image found for SeriedayHD");
        return false;
    }

    private function extract_episode_duration($post_id, $episodes_data) {
        $this->log("Extracting episode duration from EP1 M3U8 file");

        if (empty($episodes_data['dubbed']) && empty($episodes_data['subbed'])) {
            $this->log("No episodes data available for duration extraction");
            return false;
        }

        $first_episode = null;
        if (!empty($episodes_data['dubbed'])) {
            $first_episode = reset($episodes_data['dubbed']);
        } elseif (!empty($episodes_data['subbed'])) {
            $first_episode = reset($episodes_data['subbed']);
        }

        if (!$first_episode || !isset($first_episode['m3u8_url'])) {
            $this->log("No M3U8 URL found for first episode");
            return false;
        }

        $master_m3u8_url = $first_episode['m3u8_url'];
        $this->log("Fetching master M3U8 content from: $master_m3u8_url");

        $master_content = $this->fetch_m3u8_with_headers($master_m3u8_url);
        if (!$master_content) {
            $this->log("Failed to fetch master M3U8 content");
            return false;
        }

        $duration = $this->extract_duration_from_master($master_content, $master_m3u8_url);
        if ($duration > 0) {
            update_post_meta($post_id, 'movie_duration', $duration);
            $this->log("Episode duration extracted and saved: $duration minutes");
            return $duration;
        }

        $this->log("Could not calculate duration from M3U8 content");
        return false;
    }

    private function fetch_m3u8_with_headers($url) {
        $this->log("Fast M3U8 fetch: $url");

        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 8);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept: application/vnd.apple.mpegurl, */*',
                'Referer: https://main.24playerhd.com/'
            ]);

            $content = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($content === false || empty(trim($content)) || $http_code !== 200) {
                $this->log("Failed to fetch M3U8 via cURL: $url (HTTP: $http_code)");
                return false;
            }

            $this->log("M3U8 fetched via cURL: " . strlen($content) . " chars");
            return $content;
        }

        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Accept: application/vnd.apple.mpegurl, */*',
                    'Referer: https://main.24playerhd.com/'
                ],
                'timeout' => 8,
                'ignore_errors' => true
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);

        $content = @file_get_contents($url, false, $context);

        if ($content === false || empty(trim($content))) {
            $this->log("Failed to fetch M3U8: $url");
            return false;
        }

        $this->log("M3U8 fetched: " . strlen($content) . " chars");
        return $content;
    }

    private function extract_duration_from_master($master_content, $base_url) {
        $this->log("Extracting duration from master playlist (ESTIMATION MODE)");

        if (strpos($master_content, '#EXT-X-STREAM-INF:') !== false) {
            $this->log("Master M3U8 detected (contains resolution info, not segments)");

            $estimated_duration = $this->estimate_duration_from_context();
            if ($estimated_duration > 0) {
                $this->log("Estimated duration: $estimated_duration minutes (typical episode length)");
                return $estimated_duration;
            }
        }

        if (strpos($master_content, '#EXTINF:') !== false) {
            $this->log("Segment M3U8 detected (contains duration info)");
            $duration = $this->calculate_m3u8_duration_fast($master_content);
            if ($duration > 0) {
                $this->log("Calculated duration from segments: $duration minutes");
                return $duration;
            }
        }

        $this->log("Cannot determine duration from M3U8 content");
        return 0;
    }

    private function estimate_duration_from_context() {
        $this->log("Estimating duration based on typical episode lengths");

        $typical_durations = [
            45,
            60,
            30,
            90,
            25
        ];

        $selected_duration = $typical_durations[0];
        $this->log("Selected typical duration: $selected_duration minutes");

        return $selected_duration;
    }



    private function calculate_m3u8_duration_fast($m3u8_content) {
        $this->log("Fast duration calculation from M3U8 content");

        $total_duration = 0;
        $segment_count = 0;

        if (preg_match_all('/#EXTINF:(\d+\.?\d*)/', $m3u8_content, $matches)) {
            foreach ($matches[1] as $duration) {
                $total_duration += floatval($duration);
                $segment_count++;
            }
        }

        if ($total_duration > 0) {
            $duration_minutes = round($total_duration / 60);
            $this->log("Fast calculation: $segment_count segments, $total_duration seconds = $duration_minutes minutes");
            return $duration_minutes;
        }

        if (preg_match('/#EXT-X-TARGETDURATION:(\d+)/', $m3u8_content, $matches)) {
            $target_duration = intval($matches[1]);
            $ts_count = substr_count($m3u8_content, '.ts');
            if ($ts_count > 0) {
                $estimated_duration = round(($target_duration * $ts_count) / 60);
                $this->log("Fast estimation: $ts_count segments × {$target_duration}s = $estimated_duration minutes");
                return $estimated_duration;
            }
        }

        $this->log("Fast calculation failed");
        return 0;
    }

    private function calculate_m3u8_duration($m3u8_content) {
        return $this->calculate_m3u8_duration_fast($m3u8_content);
    }

    private function extract_all_episodes($series_post_id, $target_season, $series_url) {
        $this->log("Extracting all episodes for post ID: $series_post_id, season: $target_season");

        $episodes_data = [
            'dubbed' => [],
            'subbed' => []
        ];

        $available_languages = $this->detect_available_languages_serieday($series_url);
        $max_episodes = $this->detect_max_episodes_serieday($series_post_id);
        $this->log("🎯 Final max episodes for SeriedayHD: $max_episodes");

        for ($episode = 1; $episode <= $max_episodes; $episode++) {
            $episode_progress = 55 + (($episode - 1) / $max_episodes) * 30;
            $this->update_scraping_status($this->post_id, 'processing', "กำลังดึงตอนที่ $episode/$max_episodes", intval($episode_progress));
            $this->log("Processing episode $episode");

            foreach ($available_languages as $language) {
                $episode_data = $this->get_episode_data($series_post_id, $episode, $language);

                if ($episode_data && $episode_data['success']) {
                    $language_type = ($language === 'Thai') ? 'dubbed' : 'subbed';

                    $episodes_data[$language_type]["S{$target_season}E{$episode}"] = [
                        'episode_id' => $episode_data['episode_id'],
                        'iframe_url' => $episode_data['iframe_url'],
                        'm3u8_url' => $episode_data['m3u8_url'],
                        'original_url' => $episode_data['m3u8_url']
                    ];

                    $lang_desc = ($language === 'Thai') ? 'Thai dubbed' : 'Thai subbed';
                    $this->log("Successfully extracted episode $episode ($lang_desc)");
                } else {
                    $lang_desc = ($language === 'Thai') ? 'Thai dubbed' : 'Thai subbed';
                    $this->log("Failed to extract episode $episode ($lang_desc)");
                }

                usleep(200000);
            }
        }

        $dubbed_count = count($episodes_data['dubbed']);
        $subbed_count = count($episodes_data['subbed']);
        $this->log("✅ Extracted episodes - Dubbed (Thai): $dubbed_count/$max_episodes, Subbed (Thai): $subbed_count/$max_episodes");
        return $episodes_data;
    }

    private function detect_available_languages_serieday($series_url) {
        $this->log("Detecting available languages for SeriedayHD: $series_url");

        $html = $this->fetch_html($series_url);
        if (!$html) {
            $this->log("Could not fetch HTML for language detection");
            return ['Thai'];
        }

        $lang_select_pattern = '/<select[^>]*id=["\']Lang_select["\'][^>]*>(.*?)<\/select>/is';
        if (preg_match($lang_select_pattern, $html, $matches)) {
            $select_content = $matches[1];

            $option_pattern = '/<option[^>]*value=["\']([^"\']*)["\'][^>]*>([^<]*)<\/option>/i';
            preg_match_all($option_pattern, $select_content, $option_matches, PREG_SET_ORDER);

            $available_languages = [];
            foreach ($option_matches as $option) {
                $value = trim($option[1]);
                $text = trim($option[2]);
                $available_languages[] = $value;
                $this->log("Found language option: $value ($text)");
            }

            if (empty($available_languages)) {
                $this->log("No language options found, defaulting to Thai");
                return ['Thai'];
            }

            $this->log("Available languages: " . implode(', ', $available_languages));
            return $available_languages;
        }

        $this->log("Lang_select not found, defaulting to Thai");
        return ['Thai'];
    }

    private function detect_max_episodes_serieday($series_post_id) {
        $this->log("Detecting maximum episodes for SeriedayHD post ID: $series_post_id");

        $post_title = get_the_title($series_post_id);
        $this->log("Post title: $post_title");

        $title_episode_count = $this->extract_episode_count_from_title($post_title);
        if ($title_episode_count > 0) {
            $this->log("Episode count from title: $title_episode_count");
            return $title_episode_count;
        }

        $this->log("Could not detect from title, checking episodes manually...");

        $found_episodes = [];
        $max_found = 0;
        $consecutive_failures = 0;
        $max_consecutive_failures = 5;
        $check_limit = 50;

        for ($episode = 1; $episode <= $check_limit; $episode++) {
            $episode_data = $this->get_episode_data($series_post_id, $episode, 'Thai');

            if ($episode_data && $episode_data['success']) {
                $found_episodes[] = $episode;
                $max_found = $episode;
                $consecutive_failures = 0;
                $this->log("Episode $episode exists");
            } else {
                $consecutive_failures++;
                $this->log("Episode $episode not found (consecutive failures: $consecutive_failures)");

                if ($consecutive_failures >= $max_consecutive_failures && $max_found > 0) {
                    $gap_tolerance = 3;
                    $episodes_in_last_range = 0;
                    for ($i = max(1, $episode - 10); $i < $episode; $i++) {
                        if (in_array($i, $found_episodes)) {
                            $episodes_in_last_range++;
                        }
                    }

                    if ($episodes_in_last_range < 2) {
                        $this->log("Reached $max_consecutive_failures consecutive failures with low episode density, stopping detection at episode $max_found");
                        break;
                    }
                }
            }

            usleep(200000);
        }

        if ($max_found === 0) {
            $this->log("No episodes detected, defaulting to 16");
            return 16;
        }

        $episode_count = count($found_episodes);
        $final_count = max($max_found, $episode_count);

        $this->log("Episodes found: " . implode(', ', $found_episodes));
        $this->log("Maximum episode number: $max_found, Total episodes found: $episode_count, Final count: $final_count");

        return $final_count;
    }

    private function extract_episode_count_from_title($title) {
        $patterns = [
            '/EP\s*1-(\d+)/i',
            '/ตอนที่\s*1-(\d+)/i',
            '/Episode\s*1-(\d+)/i',
            '/EP\s*(\d+)\s*จบ/i',
            '/ตอน\s*(\d+)\s*จบ/i',
            '/(\d+)\s*ตอน\s*จบ/i',
            '/(\d+)\s*EP\s*จบ/i',
            '/EP\s*(\d+)\s*END/i',
            '/(\d+)\s*Episodes?/i',
            '/Total\s*(\d+)\s*EP/i',
            '/รวม\s*(\d+)\s*ตอน/i',
            '/ครบ\s*(\d+)\s*ตอน/i',
            '/\[(\d+)\s*ตอน\]/i',
            '/\((\d+)\s*EP\)/i',
            '/EP\s*(\d+)$/i'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $title, $matches)) {
                $episode_count = intval($matches[1]);
                if ($episode_count > 0 && $episode_count <= 100) {
                    $this->log("Extracted episode count from title using pattern $pattern: $episode_count");
                    return $episode_count;
                }
            }
        }

        return 0;
    }

    private function get_episode_data($series_post_id, $episode, $language) {
        $post_data = "action=halim_ajax_player&episode={$episode}&server=1&postid={$series_post_id}&lang=" . urlencode($language);

        $ajax_response = $this->make_ajax_request('https://www.serieday-hd.com/api/get.php', $post_data);

        if (!$ajax_response || !$ajax_response['success']) {
            return ['success' => false, 'error' => 'AJAX request failed'];
        }

        $iframe_match = preg_match('/<iframe[^>]*src=["\'](https:\/\/main\.24playerhd\.com\/[^"\']*?)["\']/i', $ajax_response['content'], $matches);
        if (!$iframe_match) {
            return ['success' => false, 'error' => 'No iframe found'];
        }

        $iframe_url = $matches[1];

        $id_match = preg_match('/[?&]id=([^&]+)/', $iframe_url, $matches);
        if (!$id_match) {
            return ['success' => false, 'error' => 'No ID found in iframe URL'];
        }

        $episode_id = $matches[1];
        $m3u8_url = "https://main.24playerhd.com/newplaylist/{$episode_id}/{$episode_id}.m3u8";

        return [
            'success' => true,
            'episode_id' => $episode_id,
            'iframe_url' => $iframe_url,
            'm3u8_url' => $m3u8_url
        ];
    }

    private function make_ajax_request($url, $post_data) {
        $args = [
            'method' => 'POST',
            'timeout' => 30,
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded',
                'X-Requested-With' => 'XMLHttpRequest',
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer' => 'https://www.serieday-hd.com/'
            ],
            'body' => $post_data
        ];

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            $this->log("AJAX request error: " . $response->get_error_message());
            return ['success' => false, 'error' => $response->get_error_message()];
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $content = wp_remote_retrieve_body($response);

        if ($status_code !== 200) {
            $this->log("AJAX request failed with status: $status_code");
            return ['success' => false, 'error' => "HTTP $status_code"];
        }

        return ['success' => true, 'content' => $content];
    }

    private function process_serieday_episodes($post_id, $episodes_data, $target_season) {
        $this->log("Processing SeriedayHD episodes data for Season $target_season");
        $this->update_scraping_status($post_id, 'processing', 'กำลังประมวลผลข้อมูลตอน', 85);

        $dubbed_episodes = $episodes_data['dubbed'] ?? [];
        $subbed_episodes = $episodes_data['subbed'] ?? [];

        $dubbed_count = count($dubbed_episodes);
        $subbed_count = count($subbed_episodes);

        $this->log("Found $dubbed_count dubbed episodes and $subbed_count subbed episodes");

        if ($dubbed_count === 0 && $subbed_count === 0) {
            $error_msg = 'No episodes found for processing';
            $this->log("ERROR: $error_msg");
            $this->update_scraping_status($post_id, 'failed', $error_msg);
            return false;
        }

        $this->update_scraping_status($post_id, 'processing', 'กำลังแปลงรูปแบบข้อมูล', 90);
        $processed_episodes = $this->convert_serieday_to_22hdd_format($dubbed_episodes, $subbed_episodes, $target_season);

        if (empty($processed_episodes)) {
            $error_msg = 'Failed to convert episodes to 22hdd format';
            $this->log("ERROR: $error_msg");
            $this->update_scraping_status($post_id, 'failed', $error_msg);
            return false;
        }

        $this->update_scraping_status($post_id, 'processing', 'กำลังบันทึกข้อมูลตอน', 95);
        $this->save_episodes_hybrid($post_id, $processed_episodes, $target_season);

        $this->update_scraping_status($post_id, 'processing', 'กำลังบันทึกไฟล์ M3U8', 98);
        $this->save_serieday_m3u8_files($post_id, $dubbed_episodes, $subbed_episodes, $target_season);

        $total_episodes = $dubbed_count + $subbed_count;
        $status_msg = "ดึงข้อมูลสำเร็จ: พากย์ไทย $dubbed_count ตอน + ซับไทย $subbed_count ตอน = รวม $total_episodes ตอน (ซีซั่น $target_season)";
        $this->update_scraping_status($post_id, 'completed', $status_msg, 100);
        $this->log($status_msg);

        return true;
    }

    private function save_serieday_m3u8_files($post_id, $dubbed_episodes, $subbed_episodes, $target_season) {
        $this->log("Saving SeriedayHD M3U8 files for Season $target_season");

        $all_episodes = [];

        foreach ($dubbed_episodes as $episode => $data) {
            $all_episodes[$episode]['dubbed'] = $data;
        }

        foreach ($subbed_episodes as $episode => $data) {
            $all_episodes[$episode]['subbed'] = $data;
        }

        foreach ($all_episodes as $episode => $languages) {
            foreach ($languages as $language => $data) {
                if (isset($data['m3u8_url'])) {
                    $this->save_serieday_episode_m3u8($post_id, $target_season, $episode, $language, $data['m3u8_url']);
                }
            }
        }
    }

    private function save_serieday_episode_m3u8($post_id, $season, $episode, $language, $m3u8_url) {
        $episode_dir = $this->upload_dir . "post-$post_id/$language/season_$season/episode_$episode/";

        if (!file_exists($episode_dir)) {
            wp_mkdir_p($episode_dir);
        }

        $m3u8_content = $this->fetch_m3u8_with_headers($m3u8_url);
        if (!$m3u8_content) {
            $this->log("Failed to fetch M3U8 content for S{$season}E{$episode} ($language)");
            return false;
        }

        $saved_files = [];
        $base_path = "post-$post_id/$language/season_$season/episode_$episode/";

        $master_original_file = $episode_dir . 'master_original.m3u8';
        if (file_put_contents($master_original_file, $m3u8_content) === false) {
            $this->log("Failed to save master_original M3U8 file for S{$season}E{$episode} ($language)");
            return false;
        }
        $saved_files['master_original'] = $this->base_url . $base_path . 'master_original.m3u8';
        $saved_files['master_original_url'] = $m3u8_url;
        $this->log("Saved original master file for $language S{$season}E{$episode}");

        if (preg_match_all('/([^#\n\r\s]+\.m3u8)/m', $m3u8_content, $matches)) {
            foreach ($matches[1] as $quality_file) {
                $quality_file = trim($quality_file);

                if (strpos($quality_file, 'http') === 0) {
                    $quality_url = $quality_file;
                } else {
                    $quality_url = 'https://main.24playerhd.com' . $quality_file;
                }

                $this->log("Downloading quality file from: $quality_url");

                $quality_content = $this->fetch_m3u8_with_headers($quality_url);
                if ($quality_content && !empty(trim($quality_content))) {
                    $filename = basename($quality_file);
                    $quality_path = $episode_dir . $filename;

                    if (file_put_contents($quality_path, $quality_content) !== false) {
                        $saved_files['resolutions'][$filename] = $this->base_url . $base_path . $filename;
                        $this->log("Successfully saved quality file: $filename for S{$season}E{$episode} ($language) - Size: " . strlen($quality_content) . " bytes");
                    } else {
                        $this->log("Failed to save quality file: $filename for S{$season}E{$episode} ($language)");
                    }
                } else {
                    $this->log("Failed to download or empty content for quality file: $quality_url");
                }
            }
        }

        $custom_master = $this->create_custom_master($m3u8_content, $saved_files['resolutions'] ?? []);
        $custom_master_file = $episode_dir . 'master.m3u8';
        file_put_contents($custom_master_file, $custom_master);
        $saved_files['master'] = $this->base_url . $base_path . 'master.m3u8';
        $this->log("Created custom master file for $language S{$season}E{$episode}");

        $resolution_count = isset($saved_files['resolutions']) ? count($saved_files['resolutions']) : 0;
        $total_files = 2 + $resolution_count;

        $this->log("Saved M3U8 files for S{$season}E{$episode} ($language): $total_files files (2 master + $resolution_count resolution files)");

        if ($resolution_count === 0) {
            $this->log("WARNING: No resolution files were downloaded for S{$season}E{$episode} ($language)");
        }

        return $saved_files;
    }

    protected function create_custom_master($original_content, $local_resolutions = []) {
        if (empty($local_resolutions)) {
            return $original_content;
        }

        $lines = explode("\n", $original_content);
        $custom_content = "#EXTM3U\n#EXT-X-VERSION:3\n";

        $current_stream_info = null;
        foreach ($lines as $line) {
            $line = trim($line);

            if (strpos($line, '#EXT-X-STREAM-INF:') === 0) {
                $current_stream_info = $line;
                continue;
            }

            if (strpos($line, '.m3u8') !== false && strpos($line, '#') !== 0 && $current_stream_info) {
                $filename = basename($line);
                if (isset($local_resolutions[$filename])) {
                    $custom_content .= $current_stream_info . "\n";
                    $relative_url = parse_url($local_resolutions[$filename], PHP_URL_PATH);
                    $custom_content .= $relative_url . "\n";
                }
                $current_stream_info = null;
            }
        }

        return $custom_content;
    }

    private function convert_serieday_to_22hdd_format($dubbed_episodes, $subbed_episodes, $target_season) {
        $this->log("Converting SeriedayHD episodes to 22hdd format for Season $target_season");

        $processed_episodes = [];
        $date_added = current_time('Y-m-d H:i:s');

        $all_episodes = [];

        foreach ($dubbed_episodes as $episode_key => $episode_data) {
            if (preg_match('/S(\d+)E(\d+)/', $episode_key, $matches)) {
                $season = intval($matches[1]);
                $episode_num = intval($matches[2]);

                if (!isset($all_episodes[$episode_key])) {
                    $all_episodes[$episode_key] = [
                        'season' => $season,
                        'episode' => $episode_num,
                        'title' => "Episode $episode_num",
                        'languages' => []
                    ];
                }

                $all_episodes[$episode_key]['languages']['dubbed'] = [
                    'files' => [
                        'master' => $episode_data['m3u8_url'],
                        'master_original_url' => $episode_data['original_url']
                    ],
                    'original_url' => $episode_data['original_url'],
                    'player_url' => $episode_data['iframe_url'] ?? ''
                ];
            }
        }

        foreach ($subbed_episodes as $episode_key => $episode_data) {
            if (preg_match('/S(\d+)E(\d+)/', $episode_key, $matches)) {
                $season = intval($matches[1]);
                $episode_num = intval($matches[2]);

                if (!isset($all_episodes[$episode_key])) {
                    $all_episodes[$episode_key] = [
                        'season' => $season,
                        'episode' => $episode_num,
                        'title' => "Episode $episode_num",
                        'languages' => []
                    ];
                }

                $all_episodes[$episode_key]['languages']['subbed'] = [
                    'files' => [
                        'master' => $episode_data['m3u8_url'],
                        'master_original_url' => $episode_data['original_url']
                    ],
                    'original_url' => $episode_data['original_url'],
                    'player_url' => $episode_data['iframe_url'] ?? ''
                ];
            }
        }

        foreach ($all_episodes as $episode_key => $episode_data) {
            $processed_episodes[] = $episode_data;
            $this->log("Converted episode $episode_key to 22hdd format");
        }

        $this->log("Converted " . count($processed_episodes) . " episodes to 22hdd format");
        return $processed_episodes;
    }

    private function download_and_save_m3u8($post_id, $season, $episode, $type, $m3u8_url) {
        $this->log("Downloading M3U8 for S{$season}E{$episode} ($type): $m3u8_url");

        $m3u8_content = $this->fetch_html($m3u8_url);
        if (!$m3u8_content) {
            $this->log("Failed to fetch M3U8 content");
            return false;
        }

        $episode_dir = $this->upload_dir . "post-$post_id/$type/season_$season/episode_$episode/";

        if (!file_exists($episode_dir)) {
            wp_mkdir_p($episode_dir);
        }

        $master_file = $episode_dir . 'master.m3u8';
        if (file_put_contents($master_file, $m3u8_content) === false) {
            $this->log("Failed to save master M3U8 file");
            return false;
        }

        $files = ['master.m3u8' => $master_file];

        if (preg_match_all('/^([^#\n\r]+\.m3u8)$/m', $m3u8_content, $matches)) {
            foreach ($matches[1] as $quality_file) {
                $quality_url = "https://main.24playerhd.com/m3u8/" . dirname($m3u8_url) . "/$quality_file";
                $quality_content = $this->fetch_html($quality_url);

                if ($quality_content) {
                    $quality_path = $episode_dir . basename($quality_file);
                    if (file_put_contents($quality_path, $quality_content) !== false) {
                        $files[basename($quality_file)] = $quality_path;
                        $this->log("Saved quality file: " . basename($quality_file));
                    }
                }
            }
        }

        $this->log("Saved M3U8 files for S{$season}E{$episode} ($type): " . count($files) . " files");
        return $files;
    }


}

function start_series_scraping($post_id, $series_url, $options = []) {
    $target_season = isset($options['target_season']) ? $options['target_season'] : null;
    $scraper = new SeriesScraper();
    return $scraper->scrape($post_id, $series_url, ['target_season' => $target_season]);
}

function test_enhanced_series_detection($url) {
    $scraper = new SeriesScraper();
    return $scraper->test_series_detection($url);
}

function get_enhanced_series_scraper() {
    return new SeriesScraper();
}

if (!function_exists('series_scraper_log')) {
    function series_scraper_log($message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Series Scraper] ' . $message);
        }
    }
} 