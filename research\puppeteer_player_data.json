{"scriptCount": 15, "m3u8ScriptCount": 1, "m3u8Scripts": ["\n\n    var getUrlParameter = function getUrlParameter(sParam) {\n        var sPageURL = decodeURIComponent(window.location.search.substring(1)),\n            sURLVariables = sPageURL.split('&'),\n            sParameterName,\n            i;\n\n        for (i = 0; i < sURLVariables.length; i++) {\n            sParameterName = sURLVariables[i].split('=');\n\n            if (sParameterName[0] === sParam) {\n                return sParameterName[1] === undefined ? true : sParameterName[1];\n            }\n       "], "m3u8Urls": ["https://main.24playerhd.com/newplaylist/Failed%20to%20connect%20to%20MySQL%20-%20Too%20many%20connections/Failed%20to%20connect%20to%20MySQL%20-%20Too%20many%20connections.m3u8"]}