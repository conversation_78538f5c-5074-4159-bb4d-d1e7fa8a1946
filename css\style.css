.wrap {
    font-family: Arial, sans-serif;
    margin: 0 auto;
    width: 99%;
    float: left;
}
.vu-master-server-field {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    border: 2px solid #0073aa;
    border-radius: 4px;
    background-color: #eef6fb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.vu-master-server-field input {
    flex: 1;
    padding: 10px;
    border: 1px solid #0073aa;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 16px;
}
.vu-server-field {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.vu-server-field input,
.vu-server-field select,
.vu-server-field button {
    margin-right: 5px;
    height: 40px;
}
.vu-server-field input,
.cdn-select {
    flex: 1;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}
.vu-check-server,
.vu-server-status,
.vu-remove-server,
.vu-add-cdn {
    flex: 0.5;
    padding: 5px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    color: #fff;
    height: 40px;
    font-size: 14px;
}
.vu-check-server {
    background-color: #0073aa;
}
.vu-server-status.connected {
    background-color: #28a745;
}
.vu-server-status.not-connected {
    background-color: #dc3545;
}
.vu-remove-server {
    background-color: #dc3232;
}
.vu-add-cdn {
    background-color: #ffc107;
    color: #000;
    flex: 0.25;
}
.vu-check-server:hover,
.vu-server-status:hover,
.vu-remove-server:hover,
.vu-add-cdn:hover {
    opacity: 0.9;
}
.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}
#vu-add-server,
#vu-save-settings {
    height: 50px;
    background: #00a92c;
    color: #fff;
    font-size: 17px;
    font-weight: 700;
    width: calc(50% - 2px);
    border: 2px solid #099700;
    border-radius: 6px;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: background 0.3s;
}
#vu-add-server:hover,
#vu-save-settings:hover {
    background: #099700;
}
#vu-api-post-types-container {
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex-wrap: wrap;
}
.vu-api-group {
    display: flex;
    flex-direction: row;
    gap: 8px;
    width: 100%;
    justify-content: space-between;
    margin-bottom: 10px;
}
#vu-api-key-container,
#vu-post-types-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    margin: 0 10px;
    width: 194px;
}
#vu-access-token-container label {
    text-align: center;
}
#vu-api-group-right {
    width: 57%;
    display: flex;
}
#vu-api-group-left {
    width: 43%;
}
.vu-api-field {
    display: grid;
    grid-template-columns: 120px 1fr;
    align-items: center;
    width: 100%;
}
#vu-api-key-container label,
#vu-post-types-container label {
    width: 80px;
    min-width: 80px;
}
#vu-api-key {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}
#vu-post-types {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    flex: 1;
}
#vu-post-types label {
    display: flex;
    align-items: center;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
    cursor: pointer;
    width: auto;
}
#vu-post-types input[type=checkbox] {
    margin-right: 10px;
}
#vu-access-token-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 10px;
    flex: 4;
}
#vu_access_token {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
    resize: vertical;
}
#cdn-popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    padding: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 600px;
    max-height: 80%;
    overflow-y: auto;
    z-index: 1000;
}
#cdn-popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
}
#cdn-popup-actions {
    position: absolute;
    top: 0;
    right: 0;
    margin: 5px;
    display: flex;
    gap: 5px;
}
#cdn-popup-actions button {
    padding: 8px 15px;
    cursor: pointer;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    background-color: #0073aa;
    color: #fff;
}
#cdn-popup-actions button:hover {
    opacity: 0.9;
}
#cdn-popup input {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 87%;
    box-sizing: border-box;
    margin: 10px 0 0 0;
}
#cdn-popup button {
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}
#cdn-popup #save-cdn-domain {
    background-color: #0073aa;
    color: #fff;
    width: 200px;
    margin-right: 10px;
}
#cdn-popup #cancel-cdn-domain {
    background-color: #dc3232;
    color: #fff;
    width: 200px;
}
#cdn-popup button:hover {
    opacity: 0.9;
}
#cdn-list-container {
    margin-top: 20px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
#cdn-list {
    list-style-type: none;
    padding: 0;
}
.cdn-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #ddd;
    background: #f7f7f7;
    padding-left: 10px;
}
.cdn-list-item:last-child {
    border-bottom: none;
}
.cdn-remove-button {
    background-color: #dc3232;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    padding: 5px 10px;
}
.cdn-remove-button:hover {
    opacity: 0.9;
}
.repeater-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
}
.repeater-container .repeater-field {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
}
.repeater-container .repeater-field input {
    flex: 1;
    padding: 10px;
    margin-right: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}
.repeater-container .repeater-field button {
    background-color: #dc3232;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
.repeater-container .repeater-field button:hover {
    opacity: 0.9;
}
#add-cdn-field {
    background-color: #0073aa;
    color: #fff;
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
    margin-bottom: 10px;
}
#add-cdn-field:hover {
    opacity: 0.9;
}
.form-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}
@media (max-width: 768px) {
    .vu-api-group-left,
    .vu-api-group-right {
        width: 100%;
    }
    #vu-api-post-types-container {
        flex-direction: column;
        align-items: flex-start;
    }
    #vu-api-key-container,
    #vu-post-types-container {
        width: 100%;
        margin: 10px 0;
    }
    .vu-server-field input,
    .vu-master-server-field input {
        width: 100%;
        margin-bottom: 10px;
    }
    .vu-server-field,
    .vu-master-server-field {
        flex-direction: column;
        align-items: stretch;
    }
    .vu-check-server,
    .vu-server-status,
    .vu-remove-server,
    .vu-add-cdn {
        flex: none;
        width: 100%;
        margin-bottom: 5px;
    }
    .vu-add-cdn {
        flex: none;
    }
}

/* Video Unavailable Notification Popup */
.video-unavailable-popup {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.video-unavailable-popup .swal2-title {
    color: #e74c3c;
    font-size: 1.5rem;
    font-weight: 600;
}

.video-unavailable-popup .swal2-content {
    color: #2c3e50;
    font-size: 1rem;
    line-height: 1.5;
}

.video-unavailable-popup .swal2-confirm {
    background-color: #e74c3c;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    font-weight: 600;
    transition: background-color 0.3s ease;
}

.video-unavailable-popup .swal2-confirm:hover {
    background-color: #c0392b;
}

.video-unavailable-popup .swal2-icon.swal2-warning {
    border-color: #f39c12;
    color: #f39c12;
}

/* Notification Badge */
.scraping-notification-badge {
    position: relative;
    display: inline-block;
}

.scraping-notification-badge::after {
    content: '';
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    background-color: #e74c3c;
    border-radius: 50%;
    border: 2px solid #fff;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Video Status Indicators */
.video-status-unavailable {
    color: #e74c3c;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.video-status-unavailable::before {
    content: '⚠';
    font-size: 1.2em;
}

.video-status-processing {
    color: #f39c12;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.video-status-processing::before {
    content: '⏳';
    font-size: 1.2em;
}

.video-status-completed {
    color: #27ae60;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.video-status-completed::before {
    content: '✅';
    font-size: 1.2em;
}
