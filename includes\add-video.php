<?php

$background_plugin_path = WP_PLUGIN_DIR . '/wp-background-processing-master/classes/';
if ( ! class_exists( 'WP_Async_Request' ) ) {
    require_once $background_plugin_path . 'wp-async-request.php';
}
if ( ! class_exists( 'WP_Background_Process' ) ) {
    require_once $background_plugin_path . 'wp-background-process.php';
}

function add_custom_meta_box_movie() {
    add_meta_box(
        'movie_meta_box',
        'Movie Information',
        'movie_meta_box_callback',
        ['movie', 'serie', 'anime', 'adult'],
        'normal',
        'high'
    );
    
    add_meta_box(
        'series_scraper_meta_box',
        'Series Scraper',
        'series_scraper_meta_box_callback',
        ['serie', 'anime'],
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_custom_meta_box_movie');

function custom_menu_order($menu_ord) {
    if (!$menu_ord) return true;
    return array(
        'index.php',
		'edit.php?post_type=movie', 
        'edit.php?post_type=serie', 
        'edit.php?post_type=anime', 
        'edit.php?post_type=adult', 
    );
}
add_filter('custom_menu_order', 'custom_menu_order');
add_filter('menu_order', 'custom_menu_order');

function custom_post_types_in_query($query) {
    if (!is_admin() && $query->is_main_query()) {
        if ($query->is_home()) {
            $tax_query = array(
                'relation' => 'OR',
                array(
                    'taxonomy' => 'adult_category',
                    'field'    => 'slug',
                    'terms'    => 'erotic-r18',
                ),
                array(
                    'taxonomy' => 'adult_category',
                    'operator' => 'NOT EXISTS',
                )
            );
            $query->set('tax_query', $tax_query);
            $query->set('post_type', array('movie', 'adult'));
        } elseif ($query->is_search() || $query->is_archive() || $query->is_category()) {
            $query->set('post_type', array('movie', 'serie', 'anime', 'adult'));
        }
    }
}
add_action('pre_get_posts', 'custom_post_types_in_query');
add_theme_support("post-thumbnails", array('movie'));

function custom_movieyear() {
    $labels = array(
        'name'                       => _x('Movie Year', 'Movie Year General Name', 'text_domain'),
        'singular_name'              => _x('Movie Year', 'Movie YearSingular Name', 'text_domain'),
        'menu_name'                  => __('Movie Year', 'text_domain'),
        'all_items'                  => __('All Movie Year', 'text_domain'),
        'parent_item'                => __('Parent Movie Year', 'text_domain'),
        'parent_item_colon'          => __('Parent Movie Year:', 'text_domain'),
        'new_item_name'              => __('New Movie Year', 'text_domain'),
        'add_new_item'               => __('Add Movie Year', 'text_domain'),
        'edit_item'                  => __('Edit Movie Year', 'text_domain'),
        'update_item'                => __('Update Movie Year', 'text_domain'),
        'view_item'                  => __('View Movie Year', 'text_domain'),
        'separate_items_with_commas' => __('Separate Movie Year with commas', 'text_domain'),
        'add_or_remove_items'        => __('Add or remove Movie Year', 'text_domain'),
        'choose_from_most_used'      => __('Choose from the most used', 'text_domain'),
        'popular_items'              => __('Popular Movie Year', 'text_domain'),
        'search_items'               => __('Search Movie Year', 'text_domain'),
        'not_found'                  => __('Not Found', 'text_domain'),
        'no_terms'                   => __('No Movie Year', 'text_domain'),
        'items_list'                 => __('Movie Year list', 'text_domain'),
        'items_list_navigation'      => __('Movie Year list navigation', 'text_domain'),
    );
    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => false,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
    );
    register_taxonomy('years', array('movie', 'serie'), $args);
}
add_action('init', 'custom_movieyear', 0);

function custom_imdb() {
    $labels = array(
        'name'                       => _x('IMDB Rating', 'IMDB Rating General Name', 'text_domain'),
        'singular_name'              => _x('IMDB Rating', 'IMDB Rating Singular Name', 'text_domain'),
        'menu_name'                  => __('IMDB Rating', 'text_domain'),
        'all_items'                  => __('All IMDB Rating', 'text_domain'),
        'parent_item'                => __('Parent IMDB Rating', 'text_domain'),
        'parent_item_colon'          => __('Parent IMDB Rating:', 'text_domain'),
        'new_item_name'              => __('New IMDB Rating', 'text_domain'),
        'add_new_item'               => __('Add IMDB Rating', 'text_domain'),
        'edit_item'                  => __('Edit IMDB Rating', 'text_domain'),
        'update_item'                => __('Update IMDB Rating', 'text_domain'),
        'view_item'                  => __('View IMDB Rating', 'text_domain'),
        'separate_items_with_commas' => __('Separate IMDB Rating with commas', 'text_domain'),
        'add_or_remove_items'        => __('Add or remove IMDB Rating', 'text_domain'),
        'choose_from_most_used'      => __('Choose from the most used', 'text_domain'),
        'popular_items'              => __('Popular IMDB Rating', 'text_domain'),
        'search_items'               => __('Search IMDB Rating', 'text_domain'),
        'not_found'                  => __('Not Found', 'text_domain'),
        'no_terms'                   => __('No IMDB Rating', 'text_domain'),
        'items_list'                 => __('IMDB Rating list', 'text_domain'),
        'items_list_navigation'      => __('IMDB Rating list navigation', 'text_domain'),
    );
    $args = array(
        'labels'                     => $labels,
        'hierarchical'               => false,
        'public'                     => true,
        'show_ui'                    => true,
        'show_admin_column'          => true,
        'show_in_nav_menus'          => true,
        'show_tagcloud'              => true,
    );
    register_taxonomy('imdb', array('movie', 'serie'), $args);
}
add_action('init', 'custom_imdb', 0);

function custom_post_type_movie() {
    $labels = array(
        'name'               => 'Movie',
        'singular_name'      => 'Movie',
        'menu_name'          => 'ภาพยนตร์',
        'name_admin_bar'     => 'Movie',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Movie',
        'new_item'           => 'New Movie',
        'edit_item'          => 'Edit Movie',
        'view_item'          => 'View Movie',
        'all_items'          => 'All Movies',
        'search_items'       => 'Search Movies',
        'parent_item_colon'  => 'Parent Movies:',
        'not_found'          => 'No movies found.',
        'not_found_in_trash' => 'No movies found in Trash.'
    );
    $args = array(
        'labels'              => $labels,
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'movie'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => null,
        'supports'            => array('title', 'author', 'thumbnail', 'excerpt', 'comments'),
        'taxonomies'          => array('category', 'post_tag')
    );

    register_post_type('movie', $args);
}
add_action('init', 'custom_post_type_movie');

function custom_post_type_serie() {
    $labels = array(
        'name'               => 'Serie',
        'singular_name'      => 'Serie',
        'menu_name'          => 'ซีรี่ย์',
        'name_admin_bar'     => 'Serie',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Serie',
        'new_item'           => 'New Serie',
        'edit_item'          => 'Edit Serie',
        'view_item'          => 'View Serie',
        'all_items'          => 'All Series',
        'search_items'       => 'Search Series',
        'parent_item_colon'  => 'Parent Series:',
        'not_found'          => 'No series found.',
        'not_found_in_trash' => 'No series found in Trash.'
    );
    $args = array(
        'labels'              => $labels,
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'serie'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => null,
        'supports'            => array('title', 'author', 'thumbnail', 'excerpt', 'comments'),
        'taxonomies'          => array('category', 'post_tag')
    );

    register_post_type('serie', $args);
}
add_action('init', 'custom_post_type_serie');

function custom_post_type_anime() {
    $labels = array(
        'name'               => 'Anime',
        'singular_name'      => 'Anime',
        'menu_name'          => 'อนิเมะ',
        'name_admin_bar'     => 'Anime',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Anime',
        'new_item'           => 'New Anime',
        'edit_item'          => 'Edit Anime',
        'view_item'          => 'View Anime',
        'all_items'          => 'All Anime',
        'search_items'       => 'Search Anime',
        'parent_item_colon'  => 'Parent Anime:',
        'not_found'          => 'No anime found.',
        'not_found_in_trash' => 'No anime found in Trash.'
    );
    $args = array(
        'labels'              => $labels,
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'anime'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => null,
        'supports'            => array('title', 'author', 'thumbnail', 'excerpt', 'comments'),
        'taxonomies'          => array('category', 'post_tag')
    );

    register_post_type('anime', $args);
}
add_action('init', 'custom_post_type_anime');

function custom_post_type_adult() {
    $labels = array(
        'name'               => 'Adult',
        'singular_name'      => 'Adult',
        'menu_name'          => 'หนังโป๊',
        'name_admin_bar'     => 'Adult',
        'add_new'            => 'Add New',
        'add_new_item'       => 'Add New Adult',
        'new_item'           => 'New Adult',
        'edit_item'          => 'Edit Adult',
        'view_item'          => 'View Adult',
        'all_items'          => 'All Adult',
        'search_items'       => 'Search Adult',
        'parent_item_colon'  => 'Parent Adult:',
        'not_found'          => 'No Adult found.',
        'not_found_in_trash' => 'No Adult found in Trash.'
    );
    $args = array(
        'labels'              => $labels,
        'public'              => true,
        'publicly_queryable'  => true,
        'show_ui'             => true,
        'show_in_menu'        => true,
        'query_var'           => true,
        'rewrite'             => array('slug' => 'adult'),
        'capability_type'     => 'post',
        'has_archive'         => true,
        'hierarchical'        => false,
        'menu_position'       => null,
        'supports'            => array('title', 'author', 'thumbnail', 'excerpt', 'comments'),
    	'taxonomies' => array( 'post_tag', 'adult_category')
    );

    register_post_type('adult', $args);
}
add_action('init', 'custom_post_type_adult');

function serie_custom_taxonomy() {
    $labels = array(
        'name' => _x('Serie Categories', 'taxonomy general name'),
        'singular_name' => _x('Serie Category', 'taxonomy singular name'),
        'search_items' =>  __('Search Serie Categories'),
        'all_items' => __('All Serie Categories'),
        'parent_item' => __('Parent Serie Category'),
        'parent_item_colon' => __('Parent Serie Category:'),
        'edit_item' => __('Edit Serie Category'),
        'update_item' => __('Update Serie Category'),
        'add_new_item' => __('Add New Serie Category'),
        'new_item_name' => __('New Serie Category Name'),
        'menu_name' => __('Serie Categories'),
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'series/category'), 
    );
    register_taxonomy('serie_category', array('serie'), $args);
}

add_action('init', 'serie_custom_taxonomy', 0);

function anime_custom_taxonomy() {
    $labels = array(
        'name' => _x('Anime Categories', 'taxonomy general name'),
        'singular_name' => _x('Anime Category', 'taxonomy singular name'),
        'search_items' =>  __('Search Anime Categories'),
        'all_items' => __('All Anime Categories'),
        'parent_item' => __('Parent Anime Category'),
        'parent_item_colon' => __('Parent Anime Category:'),
        'edit_item' => __('Edit Anime Category'),
        'update_item' => __('Update Anime Category'),
        'add_new_item' => __('Add New Anime Category'),
        'new_item_name' => __('New Anime Category Name'),
        'menu_name' => __('Anime Categories'),
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'animes/category'),
    );
    register_taxonomy('anime_category', array('anime'), $args);
}
add_action('init', 'anime_custom_taxonomy', 0);

function adult_custom_taxonomy() {
    $labels = array(
        'name' => _x('Adult Categories', 'taxonomy general name'),
        'singular_name' => _x('Adult Category', 'taxonomy singular name'),
        'search_items' =>  __('Search Adult Categories'),
        'all_items' => __('All Adult Categories'),
        'parent_item' => __('Parent Adult Category'),
        'parent_item_colon' => __('Parent Adult Category:'),
        'edit_item' => __('Edit Adult Category'),
        'update_item' => __('Update Adult Category'),
        'add_new_item' => __('Add New Adult Category'),
        'new_item_name' => __('New Adult Category Name'),
        'menu_name' => __('Adult Categories'),
    );
    $args = array(
        'labels' => $labels,
        'hierarchical' => true,
        'public' => true,
        'show_ui' => true,
        'show_admin_column' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'rewrite' => array('slug' => 'adults/category'), 
    );
    register_taxonomy('adult_category', array('adult'), $args);
}
add_action('init', 'adult_custom_taxonomy', 0);

add_action('admin_footer', 'enqueue_custom_validation_script');
function enqueue_custom_validation_script() {
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        function validateRepeaterFields() {
            var isValid = true;
            var dubbedEpisodes = [];
            var subbedEpisodes = [];
            var duplicateDetails = '';
            var foundDuplicates = new Set();

            var startEpisode = $('#serie_start_episode_number').val();
            startEpisode = startEpisode ? parseInt(startEpisode) : 0;

            $('input[name="serie_dubbed_episodes[]"]').each(function(index) {
                dubbedEpisodes.push({ value: $(this).val().trim(), ep: startEpisode + index + 1 });
            });

            $('input[name="serie_subbed_episodes[]"]').each(function(index) {
                subbedEpisodes.push({ value: $(this).val().trim(), ep: startEpisode + index + 1 });
            });

            dubbedEpisodes.forEach(function(item, index) {
                dubbedEpisodes.forEach(function(innerItem, innerIndex) {
                    if (innerItem.value === item.value && item.value !== "" && index !== innerIndex && !foundDuplicates.has(item.ep)) {
                        duplicateDetails += 'EP ' + item.ep + ' is duplicated with EP ' + innerItem.ep + ' in Thai Dubbed section\n';
                        foundDuplicates.add(item.ep);
                        foundDuplicates.add(innerItem.ep);
                    }
                });
            });

            subbedEpisodes.forEach(function(item, index) {
                subbedEpisodes.forEach(function(innerItem, innerIndex) {
                    if (innerItem.value === item.value && item.value !== "" && index !== innerIndex && !foundDuplicates.has(item.ep)) {
                        duplicateDetails += 'EP ' + item.ep + ' is duplicated with EP ' + innerItem.ep + ' in Thai Subbed section\n';
                        foundDuplicates.add(item.ep);
                        foundDuplicates.add(innerItem.ep); 
                    }
                });
            });

            dubbedEpisodes.forEach(function(dubbedItem) {
                subbedEpisodes.forEach(function(subbedItem) {
                    if (subbedItem.value === dubbedItem.value && dubbedItem.value !== "" && !foundDuplicates.has(dubbedItem.ep)) {
                        duplicateDetails += 'EP ' + dubbedItem.ep + ' in Thai Dubbed is duplicated with EP ' + subbedItem.ep + ' in Thai Subbed\n';
                        foundDuplicates.add(dubbedItem.ep);
                        foundDuplicates.add(subbedItem.ep);
                    }
                });
            });

            if (duplicateDetails !== '') {
                Swal.fire({
                    icon: 'error',
                    title: 'Duplicate Detected',
                    text: duplicateDetails,
                });
                isValid = false;
            }

            return isValid;
        }

        $('#post').on('submit', function(e) {
            if (!validateRepeaterFields()) {
                e.preventDefault();
            }
        });
    });
    </script>
    <?php
}



function add_movie_parts_meta_box() {
    add_meta_box('movie_parts', 'ภาคของภาพยนตร์', 'display_movie_parts', array('movie', 'serie', 'anime'), 'normal', 'high');
}
add_action('add_meta_boxes', 'add_movie_parts_meta_box');

function display_movie_parts($post) {
    $parts = get_post_meta($post->ID, 'movie_parts_data', true);
    ?>
    <div id="movie_parts_wrapper">
        <?php if ($parts) : ?>
            <?php foreach ($parts as $key => $part) : ?>
                <div class="movie_part_wrapper">
                    <label>ภาคของภาพยนตร์ <?php echo $key + 1; ?>:</label>
                    <input type="text" name="movie_parts_data[]" value="<?php echo esc_attr($part); ?>" >
                    <button type="button" class="remove_movie_part">Remove</button>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    <button type="button" id="add_movie_part">Add Part</button>
    <?php
}

function save_movie_parts($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) 
        return $post_id;
    if (wp_doing_ajax())
        return $post_id;
    if (function_exists('get_current_screen')) {
        $screen = get_current_screen();
        if ($screen && isset($screen->base) && $screen->base != 'post') 
            return $post_id;
    }
    $parts = isset($_POST['movie_parts_data']) ? $_POST['movie_parts_data'] : array();
    $filtered_parts = array();
    foreach ($parts as $part) {
        if (!empty($part)) {
            $filtered_parts[] = sanitize_text_field($part);
        }
    }
    update_post_meta($post_id, 'movie_parts_data', $filtered_parts);
}
add_action('save_post', 'save_movie_parts');

function custom_movie_parts_js() {   ?>
    <script>
        jQuery(document).ready(function($) {
            var movie_parts_wrapper = $('#movie_parts_wrapper');
            var add_movie_part_button = $('#add_movie_part');
            function addMoviePartInput() {
                var part_number = $('.movie_part_wrapper', movie_parts_wrapper).length + 1;
                var part_html = '<div class="movie_part_wrapper">' +
                    '<label>ภาคของภาพยนตร์ ' + part_number + ':</label>' +
                    '<input type="text" name="movie_parts_data[]" value="">' +
                    '<button type="button" class="remove_movie_part">Remove</button>' +
                    '</div>';
                $(movie_parts_wrapper).append(part_html);
            }
            $(add_movie_part_button).click(function(e) {
                e.preventDefault();
                addMoviePartInput();
            });
            $(movie_parts_wrapper).on('click', '.remove_movie_part', function(e) {
                e.preventDefault();
                $(this).parent('.movie_part_wrapper').remove();
            });
        });
    </script>
    <?php
}
add_action('admin_footer', 'custom_movie_parts_js'); 

function update_related_posts($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) 
        return $post_id;
    $related_urls = get_post_meta($post_id, 'movie_parts_data', true);
    $related_post_ids = array();
    $related_post_urls = array();
    if ($related_urls) {
        foreach ($related_urls as $url) {
            $related_post_id = url_to_postid($url);
            if($related_post_id) {
                $related_post_ids[] = $related_post_id;
            }
        }
    }
    foreach ($related_post_ids as $related_post_id) {
        $related_post_url = get_permalink($related_post_id);
        if ($related_post_url) {
            $related_post_urls[] = $related_post_url;
        }
    }
    foreach ($related_post_ids as $related_post_id) {
        update_post_meta($related_post_id, 'movie_parts_data', $related_post_urls);
    }
    return $post_id;
}
add_action('save_post', 'update_related_posts');

function serie_episode_styles() { ?>
    <style>
        .episode_wrapper,
        .episode_subbed_wrapper,
        .movie_part_wrapper,
        .m3u8_episode_wrapper {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            width: 100%;
        }
        #auto_update_date_meta_box input {
            margin: 3px;
        }
        .episode_wrapper label,
        .episode_subbed_wrapper label,
        .movie_part_wrapper label,
        .m3u8_episode_wrapper label {
            flex: 0 0 160px;
            margin-right: 10px;
        }
        .episode_wrapper input[type="text"],
        .episode_subbed_wrapper input[type="text"],
        .movie_part_wrapper input[type="text"],
        .m3u8_episode_wrapper input[type="text"] {
            padding: 5px;
            margin-right: 10px;
			width: 100%;
        }
        .readonly-input {
            width: 250px !important;
            background-color: #f1f1f1;
            border: 1px solid #ccc;
            cursor: not-allowed;
            color: #666;
        }
        .readonly-input[readonly] {
            background-color: #e9ecef;
        }
        .remove_episode,
        .remove_subbed_episode,
        .remove_dubbed_episode,
        .remove_movie_part,
        .remove_dubbed_m3u8_episode,
        .remove_subbed_m3u8_episode {
            flex: 0 0 auto;
            background-color: #ff0000;
            color: #ffffff;
            border: none;
            padding: 5px 10px;
            cursor: pointer;
            height: 30px;
        }
        button#add_dubbed_episode,
        button#add_subbed_episode,
        button#add_movie_part,
        button#add_dubbed_m3u8_episode,
        button#add_subbed_m3u8_episode {
            width: 100%;
            height: 40px;
            margin-top: 10px;
            font-size: 15px;
            background-color: #4caf50;
            color: #ffffff;
            border: none;
            cursor: pointer;
        }
        .clear::after {
            content: "";
            display: table;
            clear: both;
        }
        #serie_subbed_episode .postbox-container,
        #serie_dubbed_episode .postbox-container,
        #movie_parts .postbox-container {
            display: flex;
            justify-content: space-between;
        }

        #serie_subbed_episode .postbox-header,
        #serie_dubbed_episode .postbox-header,
        #movie_parts .postbox-header {
            background-color: #f1f1f1;
            padding: 10px;
        }
        #serie_subbed_episode .postbox-header h2,
        #serie_dubbed_episode .postbox-header h2,
        #movie_parts .postbox-header h2 {
            margin: 0;
            font-size: 16px;
        }
        #serie_subbed_episode .postbox .inside,
        #serie_dubbed_episode .postbox .inside,
        #movie_parts .postbox .inside {
            padding: 10px;
        }
		.button-view {
		    display: inline-block;
		    text-decoration: none;
		    font-size: 13px;
		    line-height: 2.15384615;
		    min-height: 30px;
		    margin: 0;
		    padding: 0 10px;
		    cursor: pointer;
		    border-width: 1px;
		    border-style: solid;
		    -webkit-appearance: none;
 		   	border-radius: 3px;
		    white-space: nowrap;
		    box-sizing: border-box;
		    margin-right: 9px;
		}
    </style>
<?php
}
add_action('admin_footer', 'serie_episode_styles');

function serie_episode_scripts() {
    global $post;
    if (isset($post) && ($post->post_type == 'serie' || $post->post_type == 'anime')) {
        $start_episode = get_post_meta($post->ID, 'serie_start_episode_number', true);
        if (empty($start_episode)) {
            $start_episode = 0;
        }
?>
<script type="text/javascript">
jQuery(document).ready(function($) {
    var start_episode = <?php echo esc_js($start_episode); ?>; 
    var dubbed_wrapper = $('#serie_dubbed_episodes_wrapper');
    var add_dubbed_button = $('#add_dubbed_episode');

    $(add_dubbed_button).click(function(e) {
        e.preventDefault();
        var episode_number = $('.episode_wrapper', dubbed_wrapper).length + 1 + start_episode; 
        var episode_html = '<div class="episode_wrapper">' +
            '<label>Episode พากย์ไทย ' + episode_number + ':</label>' +
            '<input type="text" name="serie_dubbed_episodes[]" value="">' +
            '<button type="button" class="remove_dubbed_episode">Remove</button>' +
            '</div>';
        $(dubbed_wrapper).append(episode_html);
    });

    $(dubbed_wrapper).on('click', '.remove_dubbed_episode', function(e) {
        e.preventDefault();
        $(this).parent('.episode_wrapper').remove();
    });

    var subbed_wrapper = $('#serie_subbed_episodes_wrapper');
    var add_subbed_button = $('#add_subbed_episode');

    $(add_subbed_button).click(function(e) {
        e.preventDefault();
        var episode_number = $('.episode_wrapper', subbed_wrapper).length + 1 + start_episode; 
        var episode_html = '<div class="episode_wrapper">' +
            '<label>Episode บรรยายไทย ' + episode_number + ':</label>' +
            '<input type="text" name="serie_subbed_episodes[]" value="">' +
            '<button type="button" class="remove_subbed_episode">Remove</button>' +
            '</div>';
        $(subbed_wrapper).append(episode_html);
    });

    $(subbed_wrapper).on('click', '.remove_subbed_episode', function(e) {
        e.preventDefault();
        $(this).parent('.episode_wrapper').remove();
    });
    
    var dubbed_m3u8_wrapper = $('#serie_dubbed_m3u8_episodes_wrapper');
    var add_dubbed_m3u8_button = $('#add_dubbed_m3u8_episode');

    $(add_dubbed_m3u8_button).click(function(e) {
        e.preventDefault();
        var episode_number = $('.m3u8_episode_wrapper', dubbed_m3u8_wrapper).length + 1 + start_episode; 
        var episode_html = '<div class="m3u8_episode_wrapper">' +
            '<label>M3U8 Episode พากย์ไทย ' + episode_number + ':</label>' +
            '<input type="text" name="serie_dubbed_m3u8_episodes[]" value="" placeholder="M3U8 URL">' +
            '<button type="button" class="remove_dubbed_m3u8_episode">Remove</button>' +
            '</div>';
        $(dubbed_m3u8_wrapper).append(episode_html);
    });

    $(dubbed_m3u8_wrapper).on('click', '.remove_dubbed_m3u8_episode', function(e) {
        e.preventDefault();
        $(this).parent('.m3u8_episode_wrapper').remove();
    });
    
    var subbed_m3u8_wrapper = $('#serie_subbed_m3u8_episodes_wrapper');
    var add_subbed_m3u8_button = $('#add_subbed_m3u8_episode');

    $(add_subbed_m3u8_button).click(function(e) {
        e.preventDefault();
        var episode_number = $('.m3u8_episode_wrapper', subbed_m3u8_wrapper).length + 1 + start_episode; 
        var episode_html = '<div class="m3u8_episode_wrapper">' +
            '<label>M3U8 Episode บรรยายไทย ' + episode_number + ':</label>' +
            '<input type="text" name="serie_subbed_m3u8_episodes[]" value="" placeholder="M3U8 URL">' +
            '<button type="button" class="remove_subbed_m3u8_episode">Remove</button>' +
            '</div>';
        $(subbed_m3u8_wrapper).append(episode_html);
    });

    $(subbed_m3u8_wrapper).on('click', '.remove_subbed_m3u8_episode', function(e) {
        e.preventDefault();
        $(this).parent('.m3u8_episode_wrapper').remove();
    });
});
</script>
<?php
    }
}
add_action('admin_footer', 'serie_episode_scripts');

function generate_serie_url($episodes) {
    $url = home_url('serie/');
    if ($episodes) {
        $url .= implode('/', $episodes) . '/';
    }
    return $url;
}

function display_serie_url() {
    $episodes = get_post_meta(get_the_ID(), 'serie_episodes', true);
    $url = generate_serie_url($episodes);
    echo 'URL: <a href="' . esc_url($url) . '">' . esc_url($url) . '</a>';
}
add_action('woocommerce_single_product_summary', 'display_serie_url', 25);

function generate_anime_url($episodes) {
    $url = home_url('anime/');
    if ($episodes) {
        $url .= implode('/', $episodes) . '/';
    }
    return $url;
}

function display_anime_url() {
    $episodes = get_post_meta(get_the_ID(), 'anime_episodes', true);
    $url = generate_anime_url($episodes);
    echo 'URL: <a href="' . esc_url($url) . '">' . esc_url($url) . '</a>';
}
add_action('woocommerce_single_product_summary', 'display_anime_url', 25);
add_theme_support('post-thumbnails', array('movie', 'serie', 'anime', 'adult'));

function check_repeater_fields($post_id) {
    if (!('movie' === get_post_type($post_id) || 'page' === get_post_type($post_id) || 'adult' === get_post_type($post_id))) {
        return;
    }
    $repeater_fields = array('serie_dubbed_episodes', 'serie_subbed_episodes');
    foreach ($repeater_fields as $field) {
        $episodes = get_post_meta($post_id, $field, true);
        if (is_array($episodes) && !empty($episodes)) {
            $filtered_episodes = array();
            foreach ($episodes as $episode) {
                if (!empty($episode)) {
                    $filtered_episodes[] = sanitize_text_field($episode);
                }
            }
            update_post_meta($post_id, $field, $filtered_episodes);
        } else {
            delete_post_meta($post_id, $field);
        }
    }
}
add_action('save_post', 'check_repeater_fields');
add_action('publish_post', 'check_repeater_fields');

function movie_meta_box_callback($post) {
    $post_type = get_post_type($post);
    $synopsis = get_post_meta($post->ID, 'synopsis', true);
    $youtube_url = get_post_meta($post->ID, 'youtube_url', true);
    $featured_image_url = get_post_meta($post->ID, '_featured_image_url', true);
    $hls_mode = get_option('vu_hls_mode', true);
    $scraping_mode = get_option('vu_scraping_mode', false);
    wp_nonce_field('movie_meta_box_nonce', 'meta_box_nonce');
    echo '<style>
    .custom-full-width-input {
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 10px;
    }
    .custom-meta-box-label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    .custom-half-width-input {
        width: 78%;
        box-sizing: border-box;
        display: inline-block;
        margin-bottom: 8px;
    }
    .custom-status-width-input {
        width: 21%;
        box-sizing: border-box;
        display: inline-block;
        vertical-align: top;
        margin-bottom: 8px;
    }
    #download_status {
        margin-top: 10px;
        font-weight: bold;
    }
    </style>';
    if ($post_type === 'movie') {
        if ($scraping_mode) {
            $scraping_url = get_post_meta($post->ID, 'scraping_url', true);
            
            echo '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">';
            echo '<h3 style="color: white; margin: 0 0 15px 0; font-size: 18px; text-align: center;">🎬 Movie Scraping System</h3>';
            echo '<label for="scraping_url" style="color: white; font-weight: bold; display: block; margin-bottom: 8px;">Movie URL to Scrape:</label>';
            echo '<div style="display: flex; gap: 10px; align-items: center;">';
            echo '<input type="text" id="scraping_url" name="scraping_url" value="' . esc_attr($scraping_url) . '" style="flex: 1; padding: 12px; border: none; border-radius: 6px; font-size: 14px;" placeholder="เช่น https://22-hdd.com/the-old-guard2/">';
            echo '<button type="button" id="start_scraping" class="button button-primary" style="padding: 12px 20px; height: auto; background: #28a745; border: none; border-radius: 6px; color: white; font-weight: bold;">🚀 Start Scraping</button>';
            echo '<button type="button" id="clear_scraping" class="button button-secondary" style="padding: 12px 20px; height: auto; background: #dc3545; border: none; border-radius: 6px; color: white; font-weight: bold;">🗑️ Clear Data</button>';
            echo '</div>';

            echo '<div id="scraping_progress" style="margin-top: 15px; display: none; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.2);">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                    <div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid #fff; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 10px;"></div>
                    <span style="color: #fff; font-weight: bold; font-size: 14px;" id="progress_text">เริ่มต้นการ scraping...</span>
                </div>
                <div style="background: rgba(255,255,255,0.2); border-radius: 10px; height: 12px; overflow: hidden;">
                    <div id="progress_bar" style="background: linear-gradient(90deg, #00d4ff, #00b4d8, #0077b6); height: 100%; width: 0%; transition: width 0.5s ease; border-radius: 10px; position: relative;">
                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%); animation: shimmer 2s infinite;"></div>
                    </div>
                </div>
                <div id="progress_details" style="color: rgba(255,255,255,0.8); font-size: 12px; margin-top: 8px;"></div>
            </div>';

            echo '</div>';

            echo '<style>
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            @keyframes shimmer {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }
            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.1); opacity: 0.7; }
                100% { transform: scale(1); opacity: 1; }
            }
            #scraping_progress, #series_scraping_progress {
                position: relative;
                overflow: hidden;
            }
            .scraping-notification-badge {
                position: relative;
                display: inline-block;
            }
            .scraping-notification-badge::after {
                content: "";
                position: absolute;
                top: -5px;
                right: -5px;
                width: 12px;
                height: 12px;
                background-color: #e74c3c;
                border-radius: 50%;
                border: 2px solid #fff;
                animation: pulse 2s infinite;
            }
            </style>';
        }
        
        $linkvideo = get_post_meta($post->ID, 'linkvideo', true);
        $movie_duration = get_post_meta($post->ID, 'movie_duration', true);
        $imdb_rating = get_post_meta($post->ID, 'imdb_rating', true);
        
        if (!$imdb_rating) {
            $imdb_terms = wp_get_post_terms($post->ID, 'imdb', array('fields' => 'names'));
            if (!empty($imdb_terms)) {
                $imdb_rating = floatval($imdb_terms[0]);
                update_post_meta($post->ID, 'imdb_rating', $imdb_rating);
            }
        }
        
        echo '<label for="custom_linkvideo" class="custom-meta-box-label">YouTube ID</label>';
        echo '<input type="text" id="custom_linkvideo" name="linkvideo" value="' . esc_attr($linkvideo) . '" class="custom-full-width-input" placeholder="YouTube ID">';
        echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 10px;">';
        echo '<div>';
        echo '<label for="movie_duration" class="custom-meta-box-label">⏱️ Movie Duration (minutes)</label>';
        echo '<input type="number" id="movie_duration" name="movie_duration" value="' . esc_attr($movie_duration) . '" class="custom-full-width-input" placeholder="ระยะเวลา (นาที)" style="width: 100%;">';
        echo '</div>';
        echo '<div>';
        echo '<label for="imdb_rating" class="custom-meta-box-label">⭐ IMDB Rating</label>';
        echo '<input type="number" step="0.1" min="0" max="10" id="imdb_rating" name="imdb_rating" value="' . esc_attr($imdb_rating) . '" class="custom-full-width-input" placeholder="คะแนน IMDB (0-10)" style="width: 100%;">';
        echo '</div>';
        echo '</div>';
        
        if ($hls_mode) {
        $gdrivedubbed = get_post_meta($post->ID, 'gdrivedubbed', true);
        $gdrivesubbed = get_post_meta($post->ID, 'gdrivesubbed', true);
        echo '<label for="custom_gdrivedubbed" class="custom-meta-box-label">Google Drive พากย์ไทย</label>';
        echo '<div style="display: grid;gap: 5px;margin-bottom: 10px;grid-template-columns: 1fr;">';
        echo '<input type="text" id="custom_gdrivedubbed" name="gdrivedubbed" value="' . esc_attr($gdrivedubbed) . '" class="custom-full-width-input" placeholder="Google Drive พากย์ไทย" style="width:100%;">';
        echo '</div>';
        echo '<label for="custom_gdrivesubbed" class="custom-meta-box-label">Google Drive ซับไทย</label>';
        echo '<div style="display: grid;gap: 5px;margin-bottom: 10px;grid-template-columns: 1fr;">';
        echo '<input type="text" id="custom_gdrivesubbed" name="gdrivesubbed" value="' . esc_attr($gdrivesubbed) . '" class="custom-full-width-input" placeholder="Google Drive ซับไทย" style="width:100%;">';
        echo '</div>';
            echo '<label for="custom_server_ip_dubbed" class="custom-meta-box-label">Server IP Dubbed</label>';
            echo '<div style="display: grid; grid-template-columns: 1fr 180px 140px; grid-gap: 5px;">';
            echo '<input type="text" id="custom_server_ip_dubbed" name="server_ip_dubbed" value="' . esc_attr(get_post_meta($post->ID, 'server_ip_dubbed', true)) . '" class="custom-half-width-input" readonly style="width: 100%; height: 38px;">';
            echo '<input type="text" id="custom_status_dubbed" name="status_dubbed" value="' . esc_attr(get_post_meta($post->ID, 'video_status_dubbed', true)) . '" class="custom-status-width-input" readonly style="width: 100%; height: 38px;">';
            if (!empty($gdrivedubbed)) {
                $video_id_dubbed = md5(extract_google_drive_id($gdrivedubbed));
                $embed_link_dubbed = "https://player.movie2free.tv/embed/" . $video_id_dubbed;
                echo '<a href="' . esc_url($embed_link_dubbed) . '" target="_blank" class="button-view" style="width: 100%; height: 38px; display: flex; justify-content: center; align-items: center;">View พากย์ไทย</a>';
            }
            echo '</div>';
            echo '<label for="custom_server_ip_subbed" class="custom-meta-box-label">Server IP Subbed</label>';
            echo '<div style="display: grid; grid-template-columns: 1fr 180px 140px; grid-gap: 5px;">';
            echo '<input type="text" id="custom_server_ip_subbed" name="server_ip_subbed" value="' . esc_attr(get_post_meta($post->ID, 'server_ip_subbed', true)) . '" class="custom-half-width-input" readonly style="width: 100%; height: 38px;">';
            echo '<input type="text" id="custom_status_subbed" name="status_subbed" value="' . esc_attr(get_post_meta($post->ID, 'video_status_subbed', true)) . '" class="custom-status-width-input" readonly style="width: 100%; height: 38px;">';
            if (!empty($gdrivesubbed)) {
                $video_id_subbed = md5(extract_google_drive_id($gdrivesubbed));
                $embed_link_subbed = "https://player.movie2free.tv/embed/" . $video_id_subbed;
                echo '<a href="' . esc_url($embed_link_subbed) . '" target="_blank" class="button-view" style="width: 100%; height: 38px; display: flex; justify-content: center; align-items: center;">View ซับไทย</a>';
            }
            echo '</div>';
        }
        
        if ($scraping_mode) {
            $m3u8_dubbed = get_post_meta($post->ID, 'm3u8_dubbed', true);
            $m3u8_subbed = get_post_meta($post->ID, 'm3u8_subbed', true);
            $scraping_dubbed_status = get_post_meta($post->ID, 'scraping_dubbed_status', true);
            $scraping_subbed_status = get_post_meta($post->ID, 'scraping_subbed_status', true);
            
            echo '<label for="custom_m3u8_dubbed" class="custom-meta-box-label">M3U8 URL พากย์ไทย (Thai)</label>';
            echo '<div style="display: grid;gap: 5px;margin-bottom: 10px;grid-template-columns: 1fr 120px;">';
            echo '<input type="text" id="custom_m3u8_dubbed" name="m3u8_dubbed" value="' . esc_attr($m3u8_dubbed) . '" class="custom-full-width-input" placeholder="M3U8 URL พากย์ไทย" style="width:100%;">';
            echo '<input type="text" id="scraping_dubbed_status" name="scraping_dubbed_status" value="' . esc_attr($scraping_dubbed_status ?: 'Status') . '" class="custom-status-width-input" readonly style="width: 100%; height: 38px;" placeholder="Dubbed Status">';
            echo '</div>';
            echo '<label for="custom_m3u8_subbed" class="custom-meta-box-label">M3U8 URL ซับไทย (Subthai)</label>';
            echo '<div style="display: grid;gap: 5px;margin-bottom: 10px;grid-template-columns: 1fr 120px;">';
            echo '<input type="text" id="custom_m3u8_subbed" name="m3u8_subbed" value="' . esc_attr($m3u8_subbed) . '" class="custom-full-width-input" placeholder="M3U8 URL ซับไทย" style="width:100%;">';
            echo '<input type="text" id="scraping_subbed_status" name="scraping_subbed_status" value="' . esc_attr($scraping_subbed_status ?: 'Status') . '" class="custom-status-width-input" readonly style="width: 100%; height: 38px;" placeholder="Subbed Status">';
            echo '</div>';

            echo '<script>
            jQuery(document).ready(function($) {
                function updateField(fieldId, value, label, forceUpdate = false) {
                    var currentValue = $("#" + fieldId).val();
                    if (value && (currentValue === "" || forceUpdate)) {
                        $("#" + fieldId).val(value);
                        showProgressUpdate(label + " ✓");
                        return true;
                    }
                    return false;
                }

                var initialUrl = $("#scraping_url").val().trim();
                if (initialUrl && initialUrl.length > 10) {
                    $("#start_scraping").removeClass("button-secondary").addClass("button-primary").css("background", "#28a745");
                }
                
                var scrapingTriggered = false;
                
                $("#scraping_url").on("paste", function(e) {
                    setTimeout(function() {
                        var pastedUrl = $("#scraping_url").val().trim();
                        if (pastedUrl && pastedUrl.length > 10 && !scrapingTriggered) {
                            scrapingTriggered = true;
                            
                            if (!isValidUrl(pastedUrl)) {
                                showValidationPopup("❌ URL ไม่ถูกต้อง", "กรุณาใส่ URL ที่ถูกต้อง เช่น https://example.com");
                                return;
                            }
                            
                            var currentYouTube = $("#custom_linkvideo").val();
                            var currentDuration = $("#movie_duration").val();
                            var currentIMDb = $("#imdb_rating").val();
                            
                            if (!currentYouTube || !currentDuration || !currentIMDb) {
                                setTimeout(function() {
                                    $("#start_scraping").trigger("click");
                                }, 1000);
                            }
                        }
                    }, 100);
                });
                
                function isValidUrl(string) {
                    try {
                        new URL(string);
                        return true;
                    } catch (e) {
                        return false;
                    }
                }
                
                function showValidationPopup(title, message) {
                    if (typeof Swal !== "undefined") {
                        Swal.fire({
                            title: title,
                            text: message,
                            icon: "error",
                            confirmButtonText: "ตกลง"
                        });
                    } else {
                        alert(title + "\n" + message);
                    }
                }
                
                function showProgressUpdate(message) {
                    $("#progress_details").html(message);
                }
                
                function checkScrapingStatus(postId) {
                    var postType = $("#post_type").val() || "movie";
                    var actionName = (postType === "movie") ? "check_scraping_status" : "check_series_scraping_status";
                    var progressBarId = (postType === "movie") ? "#progress_bar" : "#series_progress_bar";
                    var progressTextId = (postType === "movie") ? "#progress_text" : "#series_progress_text";
                    var startButtonId = (postType === "movie") ? "#start_scraping" : "#start_series_scraping";
                    var progressContainerId = (postType === "movie") ? "#scraping_progress" : "#series_scraping_progress";
                    
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: actionName,
                            post_id: postId,
                            nonce: "' . wp_create_nonce('movie_scraping_nonce') . '"
                        },
                        success: function(response) {
                            if (response && response.success) {
                                var data = response.data;
                                var progress = data.progress || 0;
                                var hasNewData = false;
                                
                                console.log("Scraping status response:", data);
                                
                                if (data.youtube_id) {
                                    var youtubeField = (postType === "movie") ? "custom_linkvideo" : "linkvideo";
                                    if (updateField(youtubeField, data.youtube_id, "YouTube ID", true)) {
                                        hasNewData = true;
                                    }
                                }
                                
                                if (data.duration) {
                                    var durationField = (postType === "movie") ? "movie_duration" : "series_duration";
                                    if (updateField(durationField, data.duration, "ระยะเวลา " + data.duration + " นาที", true)) {
                                        hasNewData = true;
                                    }
                                }
                                
                                if (data.imdb_rating) {
                                    var imdbField = (postType === "movie") ? "imdb_rating" : "series_imdb_rating";
                                    if (updateField(imdbField, data.imdb_rating, "IMDB Rating " + data.imdb_rating, true)) {
                                        hasNewData = true;
                                    }
                                }
                                
                                if (data.dubbed_url) {
                                    var dubbedField = (postType === "movie") ? "custom_m3u8_dubbed" : "m3u8_dubbed";
                                    if (updateField(dubbedField, data.dubbed_url, "M3U8 พากย์", true)) {
                                        hasNewData = true;
                                    }
                                }
                                
                                if (data.subbed_url) {
                                    var subbedField = (postType === "movie") ? "custom_m3u8_subbed" : "m3u8_subbed";
                                    if (updateField(subbedField, data.subbed_url, "M3U8 ซับ", true)) {
                                        hasNewData = true;
                                    }
                                }
                                
                                if (data.title) {
                                    var currentTitle = $("#title").val();
                                    if (!currentTitle || currentTitle.trim() === "" || currentTitle === "Auto Draft" || currentTitle === "ฉบับร่าง" || currentTitle === "Draft") {
                                        $("#title").val(data.title);
                                        showProgressUpdate("ชื่อเรื่อง: " + data.title + " ✓");
                                        hasNewData = true;
                                    }
                                }
                                
                                if (data.poster_url && $("#_featured_image_url").val() === "") {
                                    $("#_featured_image_url").val(data.poster_url);
                                    showProgressUpdate("✅ Poster URL: " + data.poster_url);
                                    hasNewData = true;
                                }
                                
                                $(progressBarId).css("width", progress + "%");
                                
                                if (data.status === "completed") {
                                    var completedText = (postType === "movie") ? "🎉 เสร็จสิ้นการ scraping!" : "🎉 เสร็จสิ้นการ scraping ซีรีย์!";
                                    $(progressTextId).text(completedText);
                                    $(startButtonId).prop("disabled", false).text("🚀 Start Scraping");
                                    
                                    setTimeout(function() {
                                        $(progressContainerId).fadeOut();
                                    }, 5000);
                                } else if (data.status === "failed" || data.status === "video_unavailable") {
                                    $(progressTextId).text("❌ เกิดข้อผิดพลาด: " + data.message);
                                    $(startButtonId).prop("disabled", false).text("🚀 Start Scraping");
                                    showProgressUpdate("❌ ไม่สามารถดึงข้อมูลได้");
                                } else {
                                    $(progressTextId).text(data.message || "🔄 กำลังประมวลผล...");
                                    
                                    setTimeout(function() {
                                        checkScrapingStatus(postId);
                                    }, 100);
                                }
                            }
                        },
                        error: function() {
                            setTimeout(function() {
                                checkScrapingStatus(postId);
                            }, 1000);
                        }
                    });
                }
                
                function validateScrapingData() {
                    var scrapingUrl = $("#scraping_url").val().trim();
                    var issues = [];
                    var warnings = [];
                    
                    if (!scrapingUrl) {
                        issues.push("• URL ที่ต้องการ scrape");
                    } else if (!isValidUrl(scrapingUrl)) {
                        issues.push("• URL ไม่ถูกต้อง");
                    }
                    
                    var currentYouTube = $("#custom_linkvideo").val().trim();
                    var currentDuration = $("#movie_duration").val().trim();
                    var currentIMDb = $("#imdb_rating").val().trim();
                    
                    if (!currentYouTube) warnings.push("• YouTube ID ยังไม่มี");
                    if (!currentDuration) warnings.push("• ระยะเวลาหนังยังไม่มี");
                    if (!currentIMDb) warnings.push("• คะแนน IMDB ยังไม่มี");
                    
                    var message = "";
                    if (issues.length > 0) {
                        message += "❌ ข้อมูลที่ต้องแก้ไข:\n" + issues.join("\n") + "\n\n";
                    }
                    if (warnings.length > 0) {
                        message += "⚠️ ข้อมูลที่จะถูกเติมอัตโนมัติ:\n" + warnings.join("\n");
                    }
                    
                    return {
                        isValid: issues.length === 0,
                        message: message || "✅ ข้อมูลพร้อมสำหรับ scraping"
                    };
                }
                
                $("#start_scraping").on("click", function() {
                    var scrapingUrl = $("#scraping_url").val().trim();
                    var postId = ' . intval($post->ID) . ';
                    
                    var validationResult = validateScrapingData();
                    if (!validationResult.isValid) {
                        showValidationPopup("📋 ตรวจสอบข้อมูลก่อน Scraping", validationResult.message);
                        return;
                    }
                    
                    $(this).prop("disabled", true).text("🔄 เริ่มต้น...");
                    $("#scraping_progress").fadeIn();
                    $("#progress_bar").css("width", "0%");
                    $("#progress_text").text("🚀 เริ่มต้นการ scraping...");
                    
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "start_movie_scraping",
                            post_id: postId,
                            scraping_url: scrapingUrl,
                            nonce: "' . wp_create_nonce('movie_scraping_nonce') . '"
                        },
                        success: function(response) {
                            if (response && response.success) {
                                $("#progress_bar").css("width", "30%");
                                $("#progress_text").text("📡 เชื่อมต่อสำเร็จ กำลังดึงข้อมูล...");
                                setTimeout(function() {
                                    checkScrapingStatus(postId);
                                }, 1000);
                            } else {
                                var message = "เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ";
                                if (response && response.data && response.data.message) {
                                    message = response.data.message;
                                } else if (response && response.message) {
                                    message = response.message;
                                }
                                $("#progress_text").text("❌ เกิดข้อผิดพลาด: " + message);
                                $("#start_scraping").prop("disabled", false).text("🚀 Start Scraping");
                            }
                        },
                        error: function() {
                            $("#progress_text").text("❌ เกิดข้อผิดพลาดในการเชื่อมต่อ");
                            $("#start_scraping").prop("disabled", false).text("🚀 Start Scraping");
                        }
                    });
                });
                
                $("#scraping_url").on("input", function() {
                    var url = $(this).val().trim();
                    if (url && url.length > 10 && (url.includes("http") || url.includes("www"))) {
                        $("#start_scraping").removeClass("button-secondary").addClass("button-primary").text("🚀 Start Scraping").css("background", "#28a745");
                    } else {
                        $("#start_scraping").removeClass("button-primary").addClass("button-secondary").text("🚀 Start Scraping").css("background", "#6c757d");
                    }
                });
                
                $("#clear_scraping").on("click", function() {
                    showCustomClearConfirm();
                });
                
                function showCustomClearConfirm() {
                    var modal = $("<div>", {
                        id: "clear-confirm-modal",
                        css: {
                            position: "fixed",
                            top: 0,
                            left: 0,
                            width: "100%",
                            height: "100%",
                            backgroundColor: "rgba(0,0,0,0.7)",
                            zIndex: 999999,
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center"
                        }
                    });
                    
                    var dialog = $("<div>", {
                        css: {
                            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                            padding: "30px",
                            borderRadius: "15px",
                            color: "white",
                            textAlign: "center",
                            maxWidth: "500px",
                            width: "90%",
                            boxShadow: "0 20px 60px rgba(0,0,0,0.3)",
                            transform: "scale(0.8)",
                            transition: "all 0.3s ease"
                        }
                    });
                    
                    dialog.html(`
                        <div style="font-size: 60px; margin-bottom: 20px;">🗑️</div>
                        <h3 style="margin: 0 0 15px 0; font-size: 24px;">ยืนยันการลบข้อมูล</h3>
                        <p style="margin: 0 0 25px 0; font-size: 16px; opacity: 0.9;">
                            คุณต้องการล้างข้อมูล scraping ทั้งหมดหรือไม่?<br>
                            <strong style="color: #ffeb3b;">⚠️ การดำเนินการนี้จะลบไฟล์และโฟลเดอร์ทั้งหมด</strong>
                        </p>
                        <div style="display: flex; gap: 15px; justify-content: center;">
                            <button id="cancel-clear" style="padding: 12px 25px; border: none; border-radius: 8px; background: rgba(255,255,255,0.2); color: white; font-weight: bold; cursor: pointer; transition: all 0.3s;">❌ ยกเลิก</button>
                            <button id="confirm-clear" style="padding: 12px 25px; border: none; border-radius: 8px; background: #dc3545; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s;">🗑️ ลบข้อมูล</button>
                        </div>
                    `);
                    
                    modal.append(dialog);
                    $("body").append(modal);
                    
                    setTimeout(function() {
                        dialog.css("transform", "scale(1)");
                    }, 50);
                    
                    $("#cancel-clear").on("click", function() {
                        closeModal();
                    });
                    
                    $("#confirm-clear").on("click", function() {
                        closeModal();
                        performClearData();
                    });
                    
                    modal.on("click", function(e) {
                        if (e.target === modal[0]) {
                            closeModal();
                        }
                    });
                    
                    function closeModal() {
                        dialog.css("transform", "scale(0.8)");
                        setTimeout(function() {
                            modal.remove();
                        }, 300);
                    }
                }
                
                function performClearData() {
                    var postId = ' . intval($post->ID) . ';
                    var clearBtn = $("#clear_scraping");
                    
                    clearBtn.prop("disabled", true).text("🔄 Clearing...");
                    
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "clear_movie_scraping",
                            post_id: postId,
                            nonce: "' . wp_create_nonce('movie_scraping_nonce') . '"
                        },
                        success: function(response) {
                            if (response && response.success) {
                                showSuccessMessage("✅ ล้างข้อมูลและไฟล์เรียบร้อยแล้ว");
                                setTimeout(function() {
                                location.reload();
                                }, 1500);
                            } else {
                                var message = "เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ";
                                if (response && response.data && response.data.message) {
                                    message = response.data.message;
                                } else if (response && response.message) {
                                    message = response.message;
                                }
                                showErrorMessage("❌ เกิดข้อผิดพลาด: " + message);
                            }
                        },
                        error: function() {
                            showErrorMessage("❌ เกิดข้อผิดพลาดในการเชื่อมต่อ");
                        },
                        complete: function() {
                            clearBtn.prop("disabled", false).text("🗑️ Clear Data");
                        }
                    });
                }
                
                function showSuccessMessage(message) {
                    showMessage(message, "#28a745");
                }
                
                function showErrorMessage(message) {
                    showMessage(message, "#dc3545");
                }
                
                function showMessage(message, color) {
                    var toast = $("<div>", {
                        css: {
                            position: "fixed",
                            top: "20px",
                            right: "20px",
                            background: color,
                            color: "white",
                            padding: "15px 25px",
                            borderRadius: "8px",
                            fontSize: "16px",
                            fontWeight: "bold",
                            zIndex: 999999,
                            boxShadow: "0 4px 15px rgba(0,0,0,0.3)",
                            transform: "translateX(400px)",
                            transition: "all 0.3s ease"
                        },
                        text: message
                    });
                    
                    $("body").append(toast);
                    
                    setTimeout(function() {
                        toast.css("transform", "translateX(0)");
                    }, 50);
                    
                    setTimeout(function() {
                        toast.css("transform", "translateX(400px)");
                        setTimeout(function() {
                            toast.remove();
                        }, 300);
                    }, 3000);
                }
            });
            </script>';
        }
        if ($hls_mode) {
            $gdrivedubbed = get_post_meta($post->ID, 'gdrivedubbed', true);
            $gdrivesubbed = get_post_meta($post->ID, 'gdrivesubbed', true);
        echo '<label for="custom_server_ip_dubbed" class="custom-meta-box-label">Server IP Dubbed</label>';
        echo '<div style="display: grid; grid-template-columns: 1fr 180px 140px; grid-gap: 5px;">';
        echo '<input type="text" id="custom_server_ip_dubbed" name="server_ip_dubbed" value="' . esc_attr(get_post_meta($post->ID, 'server_ip_dubbed', true)) . '" class="custom-half-width-input" readonly style="width: 100%; height: 38px;">';
        echo '<input type="text" id="custom_status_dubbed" name="status_dubbed" value="' . esc_attr(get_post_meta($post->ID, 'video_status_dubbed', true)) . '" class="custom-status-width-input" readonly style="width: 100%; height: 38px;">';
        if (!empty($gdrivedubbed)) {
            $video_id_dubbed = md5(extract_google_drive_id($gdrivedubbed));
            $embed_link_dubbed = "https://player.movie2free.tv/embed/" . $video_id_dubbed;
            echo '<a href="' . esc_url($embed_link_dubbed) . '" target="_blank" class="button-view" style="width: 100%; height: 38px; display: flex; justify-content: center; align-items: center;">View พากย์ไทย</a>';
        }
        echo '</div>';
        echo '<label for="custom_server_ip_subbed" class="custom-meta-box-label">Server IP Subbed</label>';
        echo '<div style="display: grid; grid-template-columns: 1fr 180px 140px; grid-gap: 5px;">';
        echo '<input type="text" id="custom_server_ip_subbed" name="server_ip_subbed" value="' . esc_attr(get_post_meta($post->ID, 'server_ip_subbed', true)) . '" class="custom-half-width-input" readonly style="width: 100%; height: 38px;">';
        echo '<input type="text" id="custom_status_subbed" name="status_subbed" value="' . esc_attr(get_post_meta($post->ID, 'video_status_subbed', true)) . '" class="custom-status-width-input" readonly style="width: 100%; height: 38px;">';
        if (!empty($gdrivesubbed)) {
            $video_id_subbed = md5(extract_google_drive_id($gdrivesubbed));
            $embed_link_subbed = "https://player.movie2free.tv/embed/" . $video_id_subbed;
            echo '<a href="' . esc_url($embed_link_subbed) . '" target="_blank" class="button-view" style="width: 100%; height: 38px; display: flex; justify-content: center; align-items: center;">View ซับไทย</a>';
        }
        echo '</div>';
        }
        
        if ($scraping_mode) {
            $m3u8_dubbed = get_post_meta($post->ID, 'm3u8_dubbed', true);
            $m3u8_subbed = get_post_meta($post->ID, 'm3u8_subbed', true);

        }
    }
    if ($post_type === 'serie' || $post_type === 'anime') {
        echo '<label for="custom_linkvideo" class="custom-meta-box-label">🎬 YouTube URL</label>';
        echo '<input type="text" id="custom_linkvideo" name="youtube_url" value="' . esc_attr($youtube_url) . '" class="custom-full-width-input" placeholder="YouTube URL">';
    }
    echo '<label for="_featured_image_url" class="custom-meta-box-label">Featured Image URL</label>';
    echo '<input type="text" id="_featured_image_url" name="_featured_image_url" value="' . esc_attr($featured_image_url) . '" class="custom-full-width-input" placeholder="Featured Image URL">';
    if ($post_type === 'adult') {
        echo '<label for="adult_image_download_url" class="custom-meta-box-label">Adult Image Download URL</label>';
        echo '<input type="text" id="adult_image_download_url" name="adult_image_download_url" value="' . esc_attr(get_post_meta($post->ID, 'adult_image_download_url', true)) . '" class="custom-full-width-input" placeholder="ใส่ URL ที่นี่">';
        echo '<div id="download_status"></div>';
        
        if ($hls_mode) {
        $adult_drive_link = get_post_meta($post->ID, 'adult_drive_link', true);
        echo '<label for="custom_adult_drive_link" class="custom-meta-box-label">Adult Drive Link</label>';
        echo '<input type="text" id="custom_adult_drive_link" name="adult_drive_link" value="' . esc_attr($adult_drive_link) . '" class="custom-full-width-input" placeholder="Adult Drive Link">';
        if (!empty($adult_drive_link)) {
            $video_id_adult = md5(extract_google_drive_id($adult_drive_link));
            $embed_link_adult = "https://player.movie2free.tv/embed/" . $video_id_adult;
            echo '<a href="' . esc_url($embed_link_adult) . '" target="_blank" class="button-view">View</a>';
        }
        echo '<label for="custom_server_ip_adult" class="custom-meta-box-label">Server IP</label>';
        echo '<input type="text" id="custom_server_ip_adult" name="server_ip_adult" value="' . esc_attr(get_post_meta($post->ID, 'server_ip_adult', true)) . '" class="custom-half-width-input" readonly>';
        echo '<input type="text" id="custom_status_adult" name="status_adult" value="' . esc_attr(get_post_meta($post->ID, 'video_status_adult', true)) . '" class="custom-status-width-input" readonly>';
        }
        
        if ($scraping_mode) {
            $adult_m3u8_url = get_post_meta($post->ID, 'adult_m3u8_url', true);
            echo '<label for="custom_adult_m3u8_url" class="custom-meta-box-label">Adult M3U8 URL</label>';
            echo '<input type="text" id="custom_adult_m3u8_url" name="adult_m3u8_url" value="' . esc_attr($adult_m3u8_url) . '" class="custom-full-width-input" placeholder="Adult M3U8 URL">';
            if (!empty($adult_m3u8_url)) {
                echo '<a href="' . esc_url($adult_m3u8_url) . '" target="_blank" class="button-view">View M3U8</a>';
            }
            echo '<label for="custom_adult_m3u8_server" class="custom-meta-box-label">M3U8 Processing Server</label>';
            echo '<input type="text" id="custom_adult_m3u8_server" name="adult_m3u8_server" value="' . esc_attr(get_post_meta($post->ID, 'adult_m3u8_server', true)) . '" class="custom-half-width-input" readonly placeholder="Processing Server">';
            echo '<input type="text" id="custom_adult_m3u8_status" name="adult_m3u8_status" value="' . esc_attr(get_post_meta($post->ID, 'adult_m3u8_status', true)) . '" class="custom-status-width-input" readonly placeholder="Status">';
        }
    }
    if ($post_type !== 'serie' && $post_type !== 'anime' && $post_type !== 'adult') {
        $settings = array('wpautop' => true, 'media_buttons' => true, 'textarea_name' => 'synopsis', 'textarea_rows' => 10, 'teeny' => false);
        wp_editor($synopsis, 'synopsis', $settings);
    }
}

function enqueue_adult_meta_box_scripts($hook) {
    global $post;
    if (($hook == 'post.php' || $hook == 'post-new.php') && isset($post) && $post->post_type == 'adult') {
        wp_enqueue_script('jquery');
        $script = '
        jQuery(document).ready(function($){
            var isDownloading = false;

            function debounce(func, wait) {
                var timeout;
                return function() {
                    var context = this, args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function() {
                        func.apply(context, args);
                    }, wait);
                };
            }

            function toggleInformation() {
                if($("#in-adult_category-11781-2").is(":checked")) {
                    $(".custom-half-width-input, .custom-status-width-input, .custom-meta-box-label, #_featured_image_url, #custom_adult_drive_link").hide();
                } else {
                    $(".custom-half-width-input, .custom-status-width-input, .custom-meta-box-label, #_featured_image_url, #custom_adult_drive_link").show();
                }
            }

            toggleInformation();

            $("#in-adult_category-11781-2").on("change", function(){
                toggleInformation();
            });

            $("#adult_image_download_url").on("input", debounce(function(){
                if(isDownloading) return;

                var url = $(this).val().trim();
                if(url){
                    isDownloading = true;
                    $("#download_status").html("กำลังตรวจจับรูปภาพ...");
                    if(url.startsWith("//")){
                        url = window.location.protocol + url;
                    }
                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "download_adult_images",
                            url: url,
                            nonce: "' . wp_create_nonce('adult_meta_box_nonce') . '",
                            post_id: ' . intval($post->ID) . ',
                            offset: 0
                        },
                        success: function(response){
                            if(response.success){
                                $("#download_status").html("พบรูปภาพทั้งหมด: " + response.data.total_images + " รูป");
                                resetContentArea();
                                downloadBatch(url, 0);
                            } else {
                                $("#download_status").html(response.data.message);
                                isDownloading = false;
                            }
                        },
                        error: function(){
                            $("#download_status").html("เกิดข้อผิดพลาดในการตรวจจับรูปภาพ");
                            isDownloading = false;
                        }
                    });
                } else {
                    isDownloading = false;
                }
            }, 500));

            function downloadBatch(url, offset){
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "download_adult_images",
                        url: url,
                        nonce: "' . wp_create_nonce('adult_meta_box_nonce') . '",
                        post_id: ' . intval($post->ID) . ',
                        offset: offset
                    },
                    success: function(response){
                        if(response.success){
                            var start = offset + 1;
                            var end = offset + response.data.images_downloaded;
                            $("#download_status").append("<br>กำลังดาวน์โหลดรูปภาพที่ " + start + " ถึง " + end);
                            if(response.data.html){
                                appendContent(response.data.html);
                            }
                            if(response.data.more){
                                downloadBatch(url, response.data.next_offset);
                            } else {
                                $("#download_status").append("<br>ดาวน์โหลดรูปภาพทั้งหมดเสร็จสิ้น");
                                isDownloading = false;
                            }
                        } else {
                            $("#download_status").append("<br>" + response.data.message);
                            isDownloading = false;
                        }
                    },
                    error: function(){
                        $("#download_status").append("<br>เกิดข้อผิดพลาดในการดาวน์โหลดรูปภาพ");
                        isDownloading = false;
                    }
                });
            }

            function appendContent(htmlContent) {
                var $content = $("#content");
                $content.val($content.val() + htmlContent);
            }

            function resetContentArea() {
                $("#content").val("");
            }
        });
        ';
        wp_add_inline_script('jquery', $script);
    }
}
add_action('admin_enqueue_scripts', 'enqueue_adult_meta_box_scripts');

function download_adult_images_callback() {
    check_ajax_referer('adult_meta_box_nonce', 'nonce');

    $url = isset($_POST['url']) ? esc_url_raw($_POST['url']) : '';
    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

    if(empty($url) || $post_id <= 0){
        wp_send_json_error(array('message' => 'URL หรือ Post ID ไม่ถูกต้อง'));
    }

    $args = array(
        'post_type' => 'any',
        'post_status' => 'any',
        'meta_query' => array(
            array(
                'key' => 'adult_image_download_url',
                'value' => $url,
                'compare' => '='
            )
        ),
        'fields' => 'ids',
        'post__not_in' => array($post_id), 
        'posts_per_page' => 1
    );
    $query = new WP_Query($args);

    if($query->have_posts()){
        wp_send_json_error(array('message' => 'URL นี้ถูกใช้ในโพสต์อื่นแล้ว'));
    }

    $response = wp_remote_get($url, array('timeout' => 30));

    if (is_wp_error($response) || wp_remote_retrieve_response_code($response) != 200) {
        wp_send_json_error(array('message' => 'ไม่สามารถดึงข้อมูลจาก URL ได้'));
    }

    $html = wp_remote_retrieve_body($response);

    libxml_use_internal_errors(true);
    $dom = new DOMDocument();
    if(!$dom->loadHTML($html)){
        wp_send_json_error(array('message' => 'ไม่สามารถวิเคราะห์ HTML จาก URL ได้'));
    }
    libxml_clear_errors();

    $xpath = new DOMXPath($dom);
    $image_nodes = $xpath->query("//div[contains(@class, 'article-body')]//img");

    $images = array();
    foreach($image_nodes as $img){
        $src = $img->getAttribute('src');
        if($src){
            if(strpos($src, '//') === 0){
                $src = (is_ssl() ? 'https:' : 'http:') . $src;
            } elseif(parse_url($src, PHP_URL_SCHEME) === null){
                $src = home_url($src);
            }
            $extension = strtolower(pathinfo(parse_url($src, PHP_URL_PATH), PATHINFO_EXTENSION));
            if($extension !== 'gif'){
                $images[] = esc_url_raw($src);
            }
        }
    }

    $images_found = count($images);
    if(empty($images)){
        wp_send_json_error(array('message' => 'ไม่พบรูปภาพในคลาส article-body หรือรูปภาพทั้งหมดเป็นไฟล์ GIF'));
    }

    $downloaded_urls = get_post_meta($post_id, 'downloaded_image_urls', true);
    if(!is_array($downloaded_urls)){
        $downloaded_urls = array();
    }

    $new_images = array_diff($images, $downloaded_urls);
    if(empty($new_images)){
        wp_send_json_error(array('message' => 'ไม่มีรูปภาพใหม่ที่ต้องดาวน์โหลด'));
    }

    $images_to_download = array_slice($new_images, $offset, 20);

    $images_downloaded = 0;
    $uploaded_images_html = '';
    $featured_image_id = 0;

    foreach($images_to_download as $index => $image_url){
        $image_id = download_image_from_url($image_url, $post_id);
        if($image_id){
            $images_downloaded++;
            $image_url_full = wp_get_attachment_url($image_id);
            $uploaded_images_html .= '<img src="' . esc_url($image_url_full) . '" alt="" />';
            if($images_downloaded === 1 && $offset === 0){
                set_post_thumbnail($post_id, $image_id);
                $featured_image_id = $image_id;
            }
            $downloaded_urls[] = $image_url;
        }
    }

    update_post_meta($post_id, 'downloaded_image_urls', $downloaded_urls);

    if($offset === 0){
        update_post_meta($post_id, 'adult_image_download_url', $url);
    }

    $total_new_images = count($new_images);
    $more = ($offset + $images_downloaded < $total_new_images) ? true : false;
    $next_offset = $offset + $images_downloaded;

    wp_send_json_success(array(
        'images_found' => $images_found,
        'images_downloaded' => $images_downloaded,
        'html' => $uploaded_images_html,
        'featured_image' => $featured_image_id,
        'next_offset' => $next_offset,
        'total_images' => $total_new_images,
        'more' => $more
    ));
}
add_action('wp_ajax_download_adult_images', 'download_adult_images_callback');

function download_image_from_url($image_url, $post_id) {
    $path = parse_url($image_url, PHP_URL_PATH);
    $filename = basename($path);
    $path_info = pathinfo($filename);
    $base_filename = $path_info['filename'];

    global $wpdb;
    $existing_attachment_id = $wpdb->get_var($wpdb->prepare(
        "SELECT p.ID 
         FROM {$wpdb->posts} p
         INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
         WHERE p.post_type = 'attachment' 
         AND p.post_title = %s 
         AND pm.meta_key = '_wp_attached_file'
         LIMIT 1",
        $base_filename
    ));

    if ($existing_attachment_id) {
        return $existing_attachment_id;
    }

    $encoded_url = encode_image_url($image_url);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $encoded_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0');
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($response === false || $http_code != 200 || empty($response)) {
        return false;
    }

    $upload_dir = wp_upload_dir();
    $tmp_dir = $upload_dir['basedir'] . '/temp/';
    if (!file_exists($tmp_dir)) {
        if (!wp_mkdir_p($tmp_dir)) {
            return false;
        }
    }

    $tmp_path = $tmp_dir . $filename;
    if (file_exists($tmp_path)) {
        return false;
    }

    if (file_put_contents($tmp_path, $response) === false) {
        @unlink($tmp_path);
        return false;
    }

    $file = array(
        'name' => $filename,
        'type' => wp_check_filetype($filename)['type'],
        'tmp_name' => $tmp_path,
        'error' => 0,
        'size' => filesize($tmp_path),
    );

    require_once(ABSPATH . 'wp-admin/includes/file.php');
    require_once(ABSPATH . 'wp-admin/includes/media.php');
    require_once(ABSPATH . 'wp-admin/includes/image.php');

    $attachment_id = media_handle_sideload($file, $post_id);
    @unlink($tmp_path);

    if (is_wp_error($attachment_id)) {
        return false;
    }

    return $attachment_id;
}

function save_movie_meta_box_data($post_id) {
    $has_movie_nonce = isset($_POST['meta_box_nonce']) && wp_verify_nonce($_POST['meta_box_nonce'], 'movie_meta_box_nonce');
    $has_scraping_nonce = isset($_POST['movie_scraping_nonce']) && wp_verify_nonce($_POST['movie_scraping_nonce'], 'movie_scraping_nonce');
    
    if (!$has_movie_nonce && !$has_scraping_nonce) {
        return;
    }
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    $post_type = get_post_type($post_id);
    $server_ip = get_best_server_ip(get_option('vu_servers', []));
    if ($post_type === 'movie') {
        $original_gdrivedubbed = get_post_meta($post_id, 'gdrivedubbed', true);
        $new_gdrivedubbed = isset($_POST['gdrivedubbed']) ? sanitize_text_field($_POST['gdrivedubbed']) : '';
        if ($original_gdrivedubbed !== $new_gdrivedubbed && !empty($original_gdrivedubbed)) {
            $original_gdrive_id = extract_google_drive_id($original_gdrivedubbed);
            $video_md5 = md5($original_gdrive_id);
            send_delete_request($video_md5, $server_ip);
            delete_post_meta($post_id, 'video_status_dubbed');
        }
        update_post_meta($post_id, 'gdrivedubbed', $new_gdrivedubbed);
        $original_gdrivesubbed = get_post_meta($post_id, 'gdrivesubbed', true);
        $new_gdrivesubbed = isset($_POST['gdrivesubbed']) ? sanitize_text_field($_POST['gdrivesubbed']) : '';
        if ($original_gdrivesubbed !== $new_gdrivesubbed && !empty($original_gdrivesubbed)) {
            $original_gdrive_id = extract_google_drive_id($original_gdrivesubbed);
            $video_md5 = md5($original_gdrive_id);
            send_delete_request($video_md5, $server_ip);
            delete_post_meta($post_id, 'video_status_subbed');
        }
        update_post_meta($post_id, 'gdrivesubbed', $new_gdrivesubbed);
        
        if (isset($_POST['m3u8_dubbed'])) {
            update_post_meta($post_id, 'm3u8_dubbed', esc_url_raw($_POST['m3u8_dubbed']));
        }
        if (isset($_POST['m3u8_subbed'])) {
            update_post_meta($post_id, 'm3u8_subbed', esc_url_raw($_POST['m3u8_subbed']));
        }
        if (isset($_POST['scraping_url'])) {
            update_post_meta($post_id, 'scraping_url', esc_url_raw($_POST['scraping_url']));
        }
    }
    if ($post_type === 'serie' || $post_type === 'anime') {
        $dubbed_episodes = isset($_POST['serie_dubbed_episodes']) ? array_map('sanitize_text_field', $_POST['serie_dubbed_episodes']) : [];
        $original_dubbed_episodes = get_post_meta($post_id, 'serie_dubbed_episodes', true);
        foreach ($dubbed_episodes as $index => $episode_gdrive) {
            if (isset($original_dubbed_episodes[$index])) {
                $original_gdrive_id = extract_google_drive_id($original_dubbed_episodes[$index]);
                $new_gdrive_id = extract_google_drive_id($episode_gdrive);
                if ($original_gdrive_id !== $new_gdrive_id && !empty($original_gdrive_id)) {
                    $video_md5 = md5($original_gdrive_id);
                    send_delete_request($video_md5, $server_ip);
                    delete_post_meta($post_id, 'video_status_episode_dubbed_' . $index);
                }
            }
        }
        update_post_meta($post_id, 'serie_dubbed_episodes', $dubbed_episodes);
        $subbed_episodes = isset($_POST['serie_subbed_episodes']) ? array_map('sanitize_text_field', $_POST['serie_subbed_episodes']) : [];
        $original_subbed_episodes = get_post_meta($post_id, 'serie_subbed_episodes', true);
        foreach ($subbed_episodes as $index => $episode_gdrive) {
            if (isset($original_subbed_episodes[$index])) {
                $original_gdrive_id = extract_google_drive_id($original_subbed_episodes[$index]);
                $new_gdrive_id = extract_google_drive_id($episode_gdrive);
                if ($original_gdrive_id !== $new_gdrive_id && !empty($original_gdrive_id)) {
                    $video_md5 = md5($original_gdrive_id);
                    send_delete_request($video_md5, $server_ip);
                    delete_post_meta($post_id, 'video_status_episode_subbed_' . $index);
                }
            }
        }
        update_post_meta($post_id, 'serie_subbed_episodes', $subbed_episodes);
        $dubbed_m3u8_episodes = isset($_POST['serie_dubbed_m3u8_episodes']) ? array_map('esc_url_raw', $_POST['serie_dubbed_m3u8_episodes']) : [];
        update_post_meta($post_id, 'serie_dubbed_m3u8_episodes', $dubbed_m3u8_episodes);
        $subbed_m3u8_episodes = isset($_POST['serie_subbed_m3u8_episodes']) ? array_map('esc_url_raw', $_POST['serie_subbed_m3u8_episodes']) : [];
        update_post_meta($post_id, 'serie_subbed_m3u8_episodes', $subbed_m3u8_episodes);
        if (isset($_POST['youtube_url'])) {
            update_post_meta($post_id, 'youtube_url', sanitize_text_field($_POST['youtube_url']));
        }
        
        if (isset($_POST['series_url'])) {
            update_post_meta($post_id, 'series_url', esc_url_raw($_POST['series_url']));
        }
        
        if (isset($_POST['target_season'])) {
            update_post_meta($post_id, 'target_season', intval($_POST['target_season']));
        }
    }
    if ($post_type === 'adult') {
        $original_adult_drive_link = get_post_meta($post_id, 'adult_drive_link', true);
        $new_adult_drive_link = sanitize_text_field($_POST['adult_drive_link']);
        if ($original_adult_drive_link !== $new_adult_drive_link && !empty($original_adult_drive_link)) {
            $original_gdrive_id = extract_google_drive_id($original_adult_drive_link);
            $video_md5 = md5($original_gdrive_id);
            send_delete_request($video_md5, $server_ip);
            delete_post_meta($post_id, 'video_status_adult');
        }
        update_post_meta($post_id, 'adult_drive_link', $new_adult_drive_link);
        if (isset($_POST['adult_m3u8_url'])) {
            update_post_meta($post_id, 'adult_m3u8_url', esc_url_raw($_POST['adult_m3u8_url']));
        }
        if (isset($_POST['adult_image_download_url'])) {
            update_post_meta($post_id, 'adult_image_download_url', esc_url_raw($_POST['adult_image_download_url']));
        }
    }
    if (isset($_POST['synopsis']) && $post_type !== 'serie' && $post_type !== 'anime') {
        update_post_meta($post_id, 'synopsis', sanitize_textarea_field($_POST['synopsis']));
    }
            if (isset($_POST['linkvideo'])) {
            update_post_meta($post_id, 'linkvideo', sanitize_text_field($_POST['linkvideo']));
        }
        if (isset($_POST['movie_duration'])) {
            update_post_meta($post_id, 'movie_duration', intval($_POST['movie_duration']));
        }
        if (isset($_POST['imdb_rating'])) {
            $imdb_rating = floatval($_POST['imdb_rating']);
            update_post_meta($post_id, 'imdb_rating', $imdb_rating);
            if ($imdb_rating > 0) {
                wp_set_post_terms($post_id, array(strval($imdb_rating)), 'imdb');
            }
        }
    if (isset($_POST['_featured_image_url'])) {
        update_post_meta($post_id, '_featured_image_url', sanitize_text_field($_POST['_featured_image_url']));
        auto_set_featured_image_from_meta($post_id);
    }
    process_video_on_server($post_id);
}
add_action('save_post', 'save_movie_meta_box_data');

function sync_imdb_taxonomy_to_meta($post_id, $terms, $tt_ids, $taxonomy) {
    if ($taxonomy === 'imdb' && !empty($terms)) {
        $imdb_rating = floatval($terms[0]);
        if ($imdb_rating > 0) {
            update_post_meta($post_id, 'imdb_rating', $imdb_rating);
        }
    }
}
add_action('set_object_terms', 'sync_imdb_taxonomy_to_meta', 10, 4);

add_action('save_post', function ($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (!current_user_can('edit_post', $post_id)) return;
    $post_type = get_post_type($post_id);
    if ($post_type === 'adult' && isset($_POST['content'])) {
        $content = wp_unslash($_POST['content']); 
        preg_match('/<img[^>]+src="([^"]+)"/', $content, $matches);
        if (!empty($matches[1])) {
            $image_url = esc_url_raw($matches[1]);
            $filename = basename(parse_url($image_url, PHP_URL_PATH));
            global $wpdb;
            $attachment_id = $wpdb->get_var($wpdb->prepare(
                "SELECT p.ID 
                 FROM {$wpdb->posts} p
                 INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
                 WHERE p.post_type = 'attachment' 
                 AND pm.meta_key = '_wp_attached_file'
                 AND pm.meta_value LIKE %s
                 LIMIT 1",
                '%' . $filename . '%'
            ));
            if ($attachment_id && !has_post_thumbnail($post_id)) {
                set_post_thumbnail($post_id, $attachment_id);
            }
        }
    }
});

function anime_add_custom_meta_box() {
    add_meta_box(
        'anime_start_episode_meta_box', 
        'Start Episode Number', 
        'anime_start_episode_meta_box_callback', 
        'anime', 
        'side', 
        'default' 
    );
}

function anime_start_episode_meta_box_callback($post) {
    wp_nonce_field('anime_start_episode_save_meta_box_data', 'anime_start_episode_meta_box_nonce');
    $value = get_post_meta($post->ID, 'serie_start_episode_number', true);
    echo '<input type="number" id="serie_start_episode_number" name="serie_start_episode_number" value="'.esc_attr($value).'" min="0" />';
}

function anime_save_meta_box_data($post_id) {
    if (!isset($_POST['anime_start_episode_meta_box_nonce'])) {
        return;
    }
    if (!wp_verify_nonce($_POST['anime_start_episode_meta_box_nonce'], 'anime_start_episode_save_meta_box_data')) {
        return;
    }
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    if (isset($_POST['post_type']) && 'anime' == $_POST['post_type']) {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    } else {
        return;
    }
    if (!isset($_POST['serie_start_episode_number'])) {
        return;
    }
    $start_episode_number = sanitize_text_field($_POST['serie_start_episode_number']);
    update_post_meta($post_id, 'serie_start_episode_number', $start_episode_number);
}
add_action('add_meta_boxes', 'anime_add_custom_meta_box');
add_action('save_post', 'anime_save_meta_box_data');

function delete_attached_media($post_id) {
    $attachments = get_posts(array(
        'post_type' => 'attachment',
        'posts_per_page' => -1,
        'post_parent' => $post_id,
    ));
    if ($attachments) {
        foreach ($attachments as $attachment) {
            if (!wp_delete_attachment($attachment->ID, true)) {
                return false;
            }
        }
    }
    return true;
}

function download_and_set_featured_image($post_id, $image_url) {
    if (!delete_attached_media($post_id)) {
        return;
    }
    if (empty($image_url) || !preg_match('/^https?:\/\//', $image_url)) {
        error_log("download_and_set_featured_image: Invalid URL - $image_url");
        return;
    }
    $encoded_url = encode_image_url($image_url);
    $image = wp_remote_get($encoded_url, array(
        'timeout' => 30,
        'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    ));
    if (is_wp_error($image)) {
        error_log("download_and_set_featured_image: Error fetching image - " . $image->get_error_message());
        return;
    }
    list($english_title, $thai_title, $year) = split_title($post_id);
    $filename = $english_title ?: $thai_title;
    $filename = sanitize_file_name($filename) . '.webp';
    $upload = wp_upload_bits($filename, null, wp_remote_retrieve_body($image));
    if ($upload['error']) {
        error_log("download_and_set_featured_image: Error uploading image - " . $upload['error']);
        return;
    }
    $image_editor = wp_get_image_editor($upload['file']);
    if (is_wp_error($image_editor)) {
        error_log("download_and_set_featured_image: Error getting image editor - " . $image_editor->get_error_message());
        return;
    }
    $image_editor->resize(300, 434, true);
    $image_editor->set_quality(50);
    $saved_image = $image_editor->save($upload['file'], 'image/webp');
    if (is_wp_error($saved_image)) {
        error_log("download_and_set_featured_image: Error saving image - " . $saved_image->get_error_message());
        return;
    }
    $filetype = wp_check_filetype($upload['file'], null);
    $attachment = array(
        'post_mime_type' => $filetype['type'],
        'post_title' => sanitize_file_name($filename),
        'post_content' => '',
        'post_status' => 'inherit'
    );
    $attach_id = wp_insert_attachment($attachment, $upload['file'], $post_id);
    if (is_wp_error($attach_id)) {
        error_log("download_and_set_featured_image: Error inserting attachment - " . $attach_id->get_error_message());
        return;
    }
    require_once(ABSPATH . 'wp-admin/includes/image.php');
    $attach_data = wp_generate_attachment_metadata($attach_id, $upload['file']);
    if (is_wp_error($attach_data)) {
        error_log("download_and_set_featured_image: Error generating attachment metadata - " . $attach_data->get_error_message());
        return;
    }
    wp_update_attachment_metadata($attach_id, $attach_data);
    set_post_thumbnail($post_id, $attach_id);
}

function encode_image_url($url) {
    $parsed = parse_url($url);
    if (!$parsed) return $url;
    $scheme = $parsed['scheme'] ?? 'https';
    $host = $parsed['host'] ?? '';
    $path = $parsed['path'] ?? '';
    $query = isset($parsed['query']) ? '?' . $parsed['query'] : '';
    $fragment = isset($parsed['fragment']) ? '#' . $parsed['fragment'] : '';
    $pathParts = explode('/', $path);
    $encodedParts = array_map(function($part) {
        if (preg_match('/[^\x00-\x7F]/', $part)) {
            return rawurlencode($part);
        }
        return $part;
    }, $pathParts);
    $encodedPath = implode('/', $encodedParts);
    return $scheme . '://' . $host . $encodedPath . $query . $fragment;
}

function auto_set_featured_image_from_meta($post_id) {
    if (has_post_thumbnail($post_id)) {
        return;
    }
    $featured_image_url = get_post_meta($post_id, '_featured_image_url', true);
    if ($featured_image_url) {
        download_and_set_featured_image($post_id, $featured_image_url);
    }
}

function custom_image_sizes() {
    add_image_size('medium', 360, 522); 
}
add_action('after_setup_theme', 'custom_image_sizes');

class Process_Video_Background extends WP_Background_Process {
    protected $action = 'process_video_background';

    protected function task($data) {
        $post_id = $data['post_id'];
        $type = $data['type'];
        $index = isset($data['index']) ? $data['index'] : null;
        $server_ip = isset($data['server_ip']) ? $data['server_ip'] : null;
        $video_id = isset($data['video_id']) ? $data['video_id'] : null;
        $video_url = isset($data['video_url']) ? $data['video_url'] : null;
        $api_key = get_option('vu_api_key', '');

        if (empty($server_ip) || empty($video_id) || empty($video_url)) {
            if (empty($server_ip)) {
                $server_ip = get_best_server_ip(get_option('vu_servers', []));
                update_post_meta($post_id, 'server_ip_' . $type, $server_ip);
            }
            if (empty($video_id) && !empty($video_url)) {
                $video_id = md5(extract_google_drive_id($video_url));
                update_post_meta($post_id, 'video_id_' . $type, $video_id);
            }
        }

        $this->process_individual_video_background($post_id, $video_url, $video_id, $type, $server_ip, $api_key);
        return false;
    }

    protected function process_individual_video_background($post_id, $video_url, $video_id, $type, $server_ip, $api_key) {
        $url = "http://$server_ip/api/getVideo.php?key=$api_key&url=" . urlencode($video_url);
        $response = curl_request($url);

        if (!is_wp_error($response)) {
            $result = json_decode($response, true);
            $post_type = get_post_type($post_id);

            if ($result['status'] == 'completed') {
                update_video_status($post_id, $type, 'completed', $result['url']);
            } elseif ($result['status'] == 'failed') {
                update_video_status($post_id, $type, 'failed');
            } elseif ($result['status'] == 'waiting') {
                update_video_status($post_id, $type, 'waiting');
            } elseif ($result['status'] == 'processing') {
                update_video_status($post_id, $type, 'processing');
            }
        } else {
            custom_log("Failed to get a valid response from server for post_id: $post_id");
        }
    }
}

$process_video_background = new Process_Video_Background();

function remove_data_from_other_servers($video_md5, $exclude_ip) {
    $api_key = get_option('vu_api_key');
    $all_servers = get_option('vu_servers', []);
    if (!is_array($all_servers) || empty($api_key)) {
        return;
    }
    foreach ($all_servers as $server) {
        $server_ip = $server['ip'] ?? null;
        if (!$server_ip) {
            continue;
        }
        if ($server_ip === $exclude_ip) {
            continue;
        }
        $url = "http://$server_ip/api/deleteVideo.php?id=$video_md5&api=$api_key";
        $response = wp_remote_post($url, array(
            'method' => 'POST',
            'timeout' => 30,
        ));
        if (!is_wp_error($response)) {
            $body = wp_remote_retrieve_body($response);
            $result = json_decode($body, true);
        }
    }
}

function send_delete_request($video_md5, $server_ip) {
    if (empty($server_ip)) {
        custom_log("Error: Server IP is required.");
        return;
    }
    $api_key = get_option('vu_api_key');
    $master_server_ip = get_option('vu_master_ip');
    $servers = [$master_server_ip, $server_ip];
    foreach ($servers as $server) {
        if (empty($server)) {
            continue;
        }
        $url = "http://$server/api/deleteVideo.php?id=$video_md5&api=$api_key";
        $response = wp_remote_post($url, array(
            'method' => 'POST',
            'timeout' => 30,
        ));
        if (is_wp_error($response)) {
            custom_log("Failed to delete video on server: $server. Error: " . $response->get_error_message());
        } else {
            $body = wp_remote_retrieve_body($response);
            $result = json_decode($body, true);
            if ($result && isset($result['status']) && $result['status'] === 'success') {
                custom_log("Successfully deleted video on server: $server");
            } else {
                custom_log("Failed to delete video on server: $server. Response: $body");
            }
        }
    }
}

function curl_request($url, $timeout = 10) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        $error_message = curl_error($ch);
        custom_log("cURL Error: $error_message");
        $response = false;
    } else {
        $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    }
    curl_close($ch);
    return $response;
}

function update_video_status($post_id, $type, $status, $url = null) {
    if ($url) {
        update_post_meta($post_id, 'video_url_' . $type, $url);
    }
    update_post_meta($post_id, 'video_status_' . $type, $status);
    $video_md5 = get_post_meta($post_id, 'video_id_' . $type, true);
    $server_ip = get_post_meta($post_id, 'server_ip_' . $type, true);
    remove_data_from_other_servers($video_md5, $server_ip);
}

function get_best_server_ip($servers) {
    if (!is_array($servers)) {
        return null;
    }
    if (empty($servers)) {
        return null;
    }
    $apiKey = get_option('vu_api_key', '');
    if (empty($apiKey)) {
        return null;
    }
    $bestServer = null;
    $maxFreePercent = 0;
    foreach ($servers as $server) {
        $serverIp = $server['ip'] ?? null;
        if (!$serverIp) {
            continue;
        }
        $apiUrl = "http://$serverIp/api/check-disk-space.php?key=" . urlencode($apiKey);
        $response = curl_request($apiUrl, 5);

        if ($response === false) {
            continue;
        }
        $data = json_decode($response, true);
        if (!isset($data['free_percent']) || !is_numeric($data['free_percent'])) {
            continue;
        }
        if ($data['free_percent'] > $maxFreePercent) {
            $maxFreePercent = $data['free_percent'];
            $bestServer = $server;
        }
    }
    return $bestServer['ip'] ?? null;
}

function process_video_on_server($post_id) {
    global $process_video_background;
    $post_type = get_post_type($post_id);
    $servers = get_option('vu_servers', []);
    $api_key = get_option('vu_api_key', '');

    if (!is_array($servers) || empty($servers)) {
        return;
    }

    if ($post_type == 'movie') {
        process_movie($post_id, $servers, $api_key);
    } else if ($post_type == 'serie' || $post_type == 'anime') {
        process_series_anime($post_id, $servers, $api_key);
    } else if ($post_type == 'adult') {
        process_adult($post_id, $servers, $api_key);
    }
}

function process_movie($post_id, $servers, $api_key) {
    global $process_video_background;
    $gdrivedubbed = get_post_meta($post_id, 'gdrivedubbed', true);
    $gdrivesubbed = get_post_meta($post_id, 'gdrivesubbed', true);
    process_video_type($post_id, $gdrivedubbed, 'dubbed', $servers, $api_key);
    process_video_type($post_id, $gdrivesubbed, 'subbed', $servers, $api_key);
    $process_video_background->save()->dispatch();
}

function process_series_anime($post_id, $servers, $api_key) {
    global $process_video_background;
    $serie_dubbed_episodes = get_post_meta($post_id, 'serie_dubbed_episodes', true);
    $serie_subbed_episodes = get_post_meta($post_id, 'serie_subbed_episodes', true);
    if (!empty($serie_dubbed_episodes)) {
        foreach ($serie_dubbed_episodes as $index => $episode_gdrive) {
            process_video_type($post_id, $episode_gdrive, 'episode_dubbed_' . $index, $servers, $api_key, $index);
        }
    }
    if (!empty($serie_subbed_episodes)) {
        foreach ($serie_subbed_episodes as $index => $episode_gdrive) {
            process_video_type($post_id, $episode_gdrive, 'episode_subbed_' . $index, $servers, $api_key, $index);
        }
    }
    $process_video_background->save()->dispatch();
}

function process_adult($post_id, $servers, $api_key) {
    global $process_video_background;
    $adultdrivelink = get_post_meta($post_id, 'adult_drive_link', true);
    process_video_type($post_id, $adultdrivelink, 'adult', $servers, $api_key);
    $process_video_background->save()->dispatch();
}

function process_video_type($post_id, $video_url, $type, $servers, $api_key, $index = null) {
    global $process_video_background;
    if (!empty($video_url)) {
        $current_video_id = get_post_meta($post_id, 'video_id_' . $type, true);
        $new_video_id = md5(extract_google_drive_id($video_url));
        if ($current_video_id !== $new_video_id) {
            update_post_meta($post_id, 'video_id_' . $type, $new_video_id);
        }
        $status = get_post_meta($post_id, 'video_status_' . $type, true);
        $server_ip = get_post_meta($post_id, 'server_ip_' . $type, true);
        if (empty($status) || empty($server_ip)) {
            $new_server_ip = get_best_server_ip($servers);
            update_post_meta($post_id, 'server_ip_' . $type, $new_server_ip);
            $process_video_background->push_to_queue([
                'post_id' => $post_id,
                'type' => $type,
                'video_id' => $new_video_id,
                'video_url' => $video_url,
                'server_ip' => $new_server_ip,
                'index' => $index
            ]);
            return;
        }
        if (!empty($status) && empty($server_ip)) {
            $new_server_ip = check_and_update_video_ip_status($post_id, $new_video_id, $type, $servers, $api_key);
            if (!empty($new_server_ip)) {
                update_post_meta($post_id, 'server_ip_' . $type, $new_server_ip);
                $server_ip = $new_server_ip;
            }
        }
        if (empty($server_ip)) {
            $server_ip = get_best_server_ip($servers);
            update_post_meta($post_id, 'server_ip_' . $type, $server_ip);
        }
        if ($status === 'failed') {
            if (!empty($current_video_id)) {
                send_delete_request($current_video_id, $server_ip);
            }
            $process_video_background->push_to_queue([
                'post_id' => $post_id,
                'type' => $type,
                'video_id' => $new_video_id,
                'video_url' => $video_url,
                'server_ip' => $server_ip,
                'index' => $index
            ]);
		} elseif ($status === 'completed') {
            update_video_status($post_id, $type, 'completed', $video_url);
        } elseif ($status !== 'completed') {
            $process_video_background->push_to_queue([
                'post_id' => $post_id,
                'type' => $type,
                'video_id' => $new_video_id,
                'video_url' => $video_url,
                'server_ip' => $server_ip,
                'index' => $index
            ]);
        }
    }
}

function check_and_update_video_ip_status($post_id, $video_id, $type, $servers, $api_key) {
    $found_ip = null;
    foreach ($servers as $server) {
        $server_ip = $server['ip'];
        $check_url = "http://$server_ip/api/check_status.php?id=$video_id";
        $response = curl_request($check_url);

        if ($response) {
            $response_data = json_decode($response, true);

            if (isset($response_data['status']) && $response_data['status'] !== 'not_found') {
                update_post_meta($post_id, 'server_ip_' . $type, $server_ip);
                update_post_meta($post_id, 'video_status_' . $type, $response_data['status']);
                $found_ip = $server_ip;
                break;
            }
        }
    }

    if (is_null($found_ip)) {
        $new_server_ip = get_best_server_ip($servers);
        update_post_meta($post_id, 'server_ip_' . $type, $new_server_ip);
        return $new_server_ip;
    }

    return $found_ip;
}

function extract_google_drive_id($url) {
    $matches = [];
    preg_match('/[-\w]{25,}/', $url, $matches);
    return $matches[0] ?? $url;
}

function add_cache_alert_menu() {
    add_menu_page('Cache Alerts', 'Cache Alerts', 'edit_posts', 'cache-alerts', 'display_cache_alerts_page');
}
add_action('admin_menu', 'add_cache_alert_menu');

function display_cache_alerts_page() {
    echo '<div class="wrap">';
    echo '<h1>Cache Alerts</h1>';
    if (isset($_GET['deleted']) && $_GET['deleted'] == 'true') {
        echo '<div class="updated"><p>Cache file or alert deleted.</p></div>';
    }
    if (isset($_GET['log_deleted']) && $_GET['log_deleted'] == 'true') {
        echo '<div class="updated"><p>Cache alerts log deleted.</p></div>';
    }
    $alert_log = wp_upload_dir()['basedir'] . "/cache_alerts.log";
    if (file_exists($alert_log)) {
        $alerts = explode("\n", file_get_contents($alert_log));
        foreach ($alerts as $alert) {
            if (!empty($alert)) {
                preg_match("/\(ID: (\d+)\)/", $alert, $matches);
                $post_id = $matches[1];
                $edit_link = get_edit_post_link($post_id);
                echo "<div id='alert-$post_id'><p>$alert <a href='#' onclick='updatePost($post_id); return false;'>Update Post</a> | <a href='#' onclick='deleteAlert($post_id); return false;'>Delete Alert</a> | <a href='$edit_link' target='_blank'>Edit Post</a></p></div>";
            }
        }
    } else {
        echo '<p>No cache alerts found.</p>';
    }
    echo "<a href='" . admin_url('admin.php?page=cache-alerts&delete_log=true') . "' class='button'>Clear Cache Alerts</a>";
    echo '</div>';
}

function cache_alerts_scripts() {
    ?>
    <script type="text/javascript">
    function updatePost(postId) {
        var xhttp = new XMLHttpRequest();
        xhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                alert('Post ID: ' + postId + ' updated successfully.');
                var alertDiv = document.getElementById('alert-' + postId);
                if (alertDiv) {
                    alertDiv.style.display = 'none';
                }
            }
        };
        xhttp.open("GET", "admin.php?page=cache-alerts&update_post=" + postId, true);
        xhttp.send();
    }

    function deleteAlert(postId) {
        var xhttp = new XMLHttpRequest();
        xhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                var alertDiv = document.getElementById('alert-' + postId);
                if (alertDiv) {
                    alertDiv.style.display = 'none';
                }
            }
        };
        xhttp.open("GET", "admin.php?page=cache-alerts&delete_alert=" + postId, true);
        xhttp.send();
    }
    </script>
    <?php
}
add_action('admin_footer', 'cache_alerts_scripts');

function update_post_from_alert($post_id) {
    $post_type = get_post_type($post_id);
    if ($post_type === 'anime' || $post_type === 'serie') {
        updateMetaAndSerieAnime($post_id);
    } elseif ($post_type === 'movie') {
        updateMetaAndMovie($post_id);
    }
    delete_cache_alert($post_id);
}

function delete_cache_alerts_log() {
    $log_file = wp_upload_dir()['basedir'] . "/cache_alerts.log";
    if (file_exists($log_file)) {
        unlink($log_file);
        wp_redirect(admin_url('admin.php?page=cache-alerts&log_deleted=true'));
        exit;
    } else {
        wp_redirect(admin_url('admin.php?page=cache-alerts&log_not_found=true'));
        exit;
    }
}

function delete_cache_alert($post_id) {
    $alert_log = wp_upload_dir()['basedir'] . "/cache_alerts.log";
    if (file_exists($alert_log)) {
        $alerts = file_get_contents($alert_log);
        $updated_alerts = preg_replace("/.*\(ID: " . preg_quote($post_id, '/') . "\).*\n?/", '', $alerts, 1);
        file_put_contents($alert_log, $updated_alerts);
    }
}

if (isset($_GET['delete_alert']) && current_user_can('manage_options')) {
    $post_id_to_delete = intval($_GET['delete_alert']);
    delete_cache_alert($post_id_to_delete);
    wp_redirect(admin_url('admin.php?page=cache-alerts&deleted=true'));
    exit;
}

if (isset($_GET['update_post']) && current_user_can('manage_options')) {
    $post_id_to_update = intval($_GET['update_post']);
    update_post_from_alert($post_id_to_update);
    exit;
}

if (isset($_GET['delete_log']) && current_user_can('manage_options')) {
    delete_cache_alerts_log();
}

function record_cache_alert($post_id, $file_size, $episode = null) {
    $alert_log = wp_upload_dir()['basedir'] . "/cache_alerts.log";
    $post_title = get_the_title($post_id);
    $episode_info = $episode ? " EP: $episode" : "";
    $new_alert = sprintf("Alert: '%s' (ID: %d%s) - File size too small (%d bytes).\n", $post_title, $post_id, $episode_info, $file_size);
    if (file_exists($alert_log)) {
        $existing_alerts = file_get_contents($alert_log);
        if (strpos($existing_alerts, $new_alert) === false) {
            file_put_contents($alert_log, $new_alert, FILE_APPEND);
        }
    } else {
        file_put_contents($alert_log, $new_alert);
    }
}

function checkUrlExists($url) {
    if (filter_var($url, FILTER_VALIDATE_URL)) {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        if ($response === false) {
            return false;
        }
        $response = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        return $response == 200;
    }
    return false;
}

function get_cached_transformed_url($data, $post_id, $i = null, $language = null) {
    if (empty($post_id) || empty($data['source']) || empty($data['key'])) {
        return false;
    }
    $sitename = parse_url(get_site_url(), PHP_URL_HOST);
    $sitename = preg_replace('/^www\./', '', $sitename);
    $player_domain = "https://player.$sitename";
    $post_type = get_post_type($post_id);
    $status_function_map = [
        'movie'  => 'get_movie_status',
        'serie'  => 'get_serie_anime_status',
        'anime'  => 'get_serie_anime_status',
        'adult'  => 'get_adult_status',
    ];
    if (isset($status_function_map[$post_type])) {
        $status_function = $status_function_map[$post_type];
        $status = $status_function($post_id, $i, $language);
        $video_id = get_video_id($post_type, $post_id, $i, $language);
        if (!empty($video_id)) {
            return handle_cache($player_domain, $video_id, $data, $post_id, $i, $language);
        }
    }
}

function handle_cache($player_domain, $video_id, $data, $post_id, $i, $language) {
    $post_type = ucfirst(get_post_type($post_id));
    $upload_dir = wp_upload_dir();
    $cache_base_dir = $upload_dir['basedir'] . "/cache/$post_type";
    if (!file_exists($cache_base_dir)) {
        mkdir($cache_base_dir, 0755, true);
    }
    $cache_key = 'transformed_url_' . md5($post_id . serialize($data['source']) . $data['key'] . $i);
    $cache_dir = "$cache_base_dir/$post_id";
    $cache_file = "$cache_dir/$cache_key.txt";
    if (!file_exists($cache_dir)) {
        mkdir($cache_dir, 0755, true);
    }
    if (file_exists($cache_file) && filesize($cache_file) > 20) {
        $url = file_get_contents($cache_file);
        if ($url !== false) {
            return $url;
        }
    }
    $url = "$player_domain/embed/$video_id/";
    file_put_contents($cache_file, $url);
    return $url;
}

function get_movie_status($post_id, $i, $language) {
    return get_post_meta($post_id, 'video_status_' . $language, true);
}

function get_serie_anime_status($post_id, $i, $language) {
    if ($i !== null) {
        return get_post_meta($post_id, 'video_status_episode_' . $language . '_' . $i, true);
    }
    return false;
}

function get_adult_status($post_id, $i, $language) {
    return get_post_meta($post_id, 'video_status_adult', true);
}

function get_video_id($post_type, $post_id, $i, $language) {
    $video_id_map = [
        'movie' => 'video_id_' . $language,
        'serie' => 'video_id_episode_' . $language . '_' . $i,
        'anime' => 'video_id_episode_' . $language . '_' . $i,
        'adult' => 'video_id_adult',
    ];
    if (isset($video_id_map[$post_type])) {
        return get_post_meta($post_id, $video_id_map[$post_type], true);
    }
    return false;
}

function update_meta_and_serie_anime($post_id) {
    if (!in_array(get_post_type($post_id), array('anime', 'serie'))) {
        return;
    }
    $post_type = ucfirst(get_post_type($post_id));
    $upload_dir = wp_upload_dir();
    $cache_base_dir = $upload_dir['basedir'] . "/cache/" . $post_type;
    clear_cache_directory($cache_base_dir . "/$post_id");
    $old_dubbed_episodes = get_post_meta($post_id, 'old_serie_dubbed_episodes', true) ?: array();
    $old_subbed_episodes = get_post_meta($post_id, 'old_serie_subbed_episodes', true) ?: array();
    $dubbed_episodes = get_post_meta($post_id, 'serie_dubbed_episodes', true);
    $subbed_episodes = get_post_meta($post_id, 'serie_subbed_episodes', true);
    if (!is_array($dubbed_episodes)) {
        $dubbed_episodes = array();
    }
    if (!is_array($subbed_episodes)) {
        $subbed_episodes = array();
    }
    process_episodes($post_id, $dubbed_episodes, $old_dubbed_episodes, 'dubbed', $cache_base_dir);
    process_episodes($post_id, $subbed_episodes, $old_subbed_episodes, 'subbed', $cache_base_dir);
    update_post_meta($post_id, 'old_serie_dubbed_episodes', $dubbed_episodes);
    update_post_meta($post_id, 'old_serie_subbed_episodes', $subbed_episodes);
}

function process_episodes($post_id, $episodes, $old_episodes, $type, $cache_base_dir) {
    if (!is_array($episodes)) {
        return;
    }
    foreach ($episodes as $i => $episode) {
        $cache_dir = $cache_base_dir . "/$post_id";
        $cache_key = 'transformed_url_' . md5($post_id . serialize($episode) . '@!Qazwsx2531' . $i);
        $cache_file = $cache_dir . '/' . $cache_key . ".txt";
        $data = ['source' => $episode, 'key' => '@!Qazwsx2531'];
        if (!isset($old_episodes[$i]) || $episode !== $old_episodes[$i] || (file_exists($cache_file) && filesize($cache_file) <= 20)) {
            get_cached_transformed_url($data, $post_id, $i, $type);
        }
    }
}
add_action('save_post', 'update_meta_and_serie_anime', 10, 1);

function clear_cache_directory($cache_dir) {
    if (is_dir($cache_dir)) {
        foreach (glob($cache_dir . '/*') as $file) {
            if (is_file($file)) unlink($file);
        }
    }
}

function update_meta_and_movie($post_id) {
    if (get_post_type($post_id) !== 'movie') {
        return;
    }
    $post_type = ucfirst(get_post_type($post_id));
    $upload_dir = wp_upload_dir();
    $cache_base_dir = $upload_dir['basedir'] . "/cache/" . $post_type;
    clear_cache_directory($cache_base_dir . "/$post_id");
    $gdrivedubbed = get_post_meta($post_id, 'gdrivedubbed', true);
    $gdrivesubbed = get_post_meta($post_id, 'gdrivesubbed', true);
    $data_dub = array('source' => $gdrivedubbed, 'key' => '@!Qazwsx2531');
    $data_sub = array('source' => $gdrivesubbed, 'key' => '@!Qazwsx2531');
    if (!empty($gdrivedubbed)) {
        get_cached_transformed_url($data_dub, $post_id, null, 'dubbed');
    }
    if (!empty($gdrivesubbed)) {
        get_cached_transformed_url($data_sub, $post_id, null, 'subbed');
    }
}
add_action('save_post', 'update_meta_and_movie', 10, 1);

function start_movie_scraping_ajax() {
    check_ajax_referer('movie_scraping_nonce', 'nonce');
    
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => 'ไม่มีสิทธิ์ในการดำเนินการ'));
    }
    
    $post_id = intval($_POST['post_id']);
    $scraping_url = esc_url_raw($_POST['scraping_url']);
    
    if (empty($post_id) || empty($scraping_url)) {
        wp_send_json_error(array('message' => 'ข้อมูลไม่ครบถ้วน'));
    }
    
    if (get_post_type($post_id) !== 'movie') {
        wp_send_json_error(array('message' => 'โพสต์นี้ไม่ใช่ประเภทหนัง'));
    }
    
    require_once plugin_dir_path(__FILE__) . 'scrapers/MovieScraper.php';
    
    $result = start_movie_scraping($post_id, $scraping_url);
    
    if ($result) {
        wp_send_json_success(array('message' => 'เริ่มต้นการ scrape เรียบร้อยแล้ว'));
    } else {
        wp_send_json_error(array('message' => 'ไม่สามารถเริ่มต้นการ scrape ได้'));
    }
}
add_action('wp_ajax_start_movie_scraping', 'start_movie_scraping_ajax');

function clear_movie_scraping_ajax() {
    check_ajax_referer('movie_scraping_nonce', 'nonce');
    
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => 'ไม่มีสิทธิ์ในการดำเนินการ'));
    }
    
    $post_id = intval($_POST['post_id']);
    
    if (empty($post_id)) {
        wp_send_json_error(array('message' => 'ข้อมูลไม่ครบถ้วน'));
    }
    
    if (get_post_type($post_id) !== 'movie') {
        wp_send_json_error(array('message' => 'โพสต์นี้ไม่ใช่ประเภทหนัง'));
    }
    
    require_once plugin_dir_path(__FILE__) . 'scrapers/MovieScraper.php';
    
    $scraper = new MovieScraper();
    $scraper->clear_scraping_data($post_id);
    
    wp_send_json_success(array('message' => 'ล้างข้อมูลเรียบร้อยแล้ว'));
}
add_action('wp_ajax_clear_movie_scraping', 'clear_movie_scraping_ajax');

function check_scraping_status_ajax() {
    check_ajax_referer('movie_scraping_nonce', 'nonce');
    
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => 'ไม่มีสิทธิ์ในการดำเนินการ'));
    }
    
    $post_id = intval($_POST['post_id']);
    
    if (empty($post_id)) {
        wp_send_json_error(array('message' => 'ข้อมูลไม่ครบถ้วน'));
    }
    
    if (get_post_type($post_id) !== 'movie') {
        wp_send_json_error(array('message' => 'โพสต์นี้ไม่ใช่ประเภทหนัง'));
    }
    
    require_once plugin_dir_path(__FILE__) . 'scrapers/MovieScraper.php';
    
    $scraper = new MovieScraper();
    $status = $scraper->get_scraping_status($post_id);
    
    $youtube_id = get_post_meta($post_id, 'linkvideo', true);
    $duration = get_post_meta($post_id, 'movie_duration', true);
    $imdb_rating = get_post_meta($post_id, 'imdb_rating', true);
    $title = get_the_title($post_id);
    
    if (!$imdb_rating) {
        $imdb_terms = wp_get_post_terms($post_id, 'imdb', array('fields' => 'names'));
        if (!empty($imdb_terms)) {
            $imdb_rating = floatval($imdb_terms[0]);
        }
    }
    
    $progress = get_post_meta($post_id, 'scraping_progress', true) ?: 0;

    $response_data = array(
        'status' => $status['status'] ?? 'pending',
        'message' => $status['message'] ?? '',
        'progress' => intval($progress),
        'youtube_id' => $youtube_id,
        'duration' => $duration,
        'imdb_rating' => $imdb_rating,
        'title' => $title,
        'poster_url' => get_post_meta($post_id, '_featured_image_url', true),
        'debug_info' => array(
            'post_id' => $post_id,
            'has_youtube' => !empty($youtube_id),
            'has_duration' => !empty($duration),
            'has_imdb' => !empty($imdb_rating),
            'has_poster' => !empty(get_post_meta($post_id, '_featured_image_url', true)),
            'scraping_status' => $status['status'] ?? 'unknown',
            'progress' => intval($progress)
        )
    );
    
    $settings = get_option('movie_scraper_settings', array());
    $use_downloaded_files = isset($settings['use_downloaded_files']) ? $settings['use_downloaded_files'] : true;
    
    if ($use_downloaded_files) {
        $dubbed_files = get_post_meta($post_id, 'scraping_dubbed_files', true);
        $subbed_files = get_post_meta($post_id, 'scraping_subbed_files', true);
        
        if (!empty($dubbed_files['master'])) {
            $response_data['dubbed_url'] = $dubbed_files['master'];
        }
        
        if (!empty($subbed_files['master'])) {
            $response_data['subbed_url'] = $subbed_files['master'];
        }
    } else {
        $response_data['dubbed_url'] = get_post_meta($post_id, 'dubbed_master_original_url', true);
        $response_data['subbed_url'] = get_post_meta($post_id, 'subbed_master_original_url', true);
    }
    
    wp_send_json_success($response_data);
}
add_action('wp_ajax_check_scraping_status', 'check_scraping_status_ajax');
add_action('wp_ajax_get_scraping_status', 'handle_get_scraping_status');
add_action('wp_ajax_nopriv_get_scraping_status', 'handle_get_scraping_status');

function handle_get_scraping_status() {
    if (!isset($_POST['post_id']) || !isset($_POST['nonce'])) {
        wp_die('Missing required parameters');
    }
    if (!wp_verify_nonce($_POST['nonce'], 'movie_scraping_nonce')) {
        wp_die('Security check failed');
    }
    $post_id = intval($_POST['post_id']);
    if (!$post_id) {
        wp_send_json_error('Invalid post ID');
        return;
    }
    $scraper = new MovieScraper();
    $status = $scraper->get_scraping_status($post_id);
    $requires_notification = get_post_meta($post_id, 'scraping_requires_user_notification', true);
    $notification_message = get_post_meta($post_id, 'scraping_notification_message', true);

    if ($requires_notification && $notification_message) {
        $status['requires_notification'] = true;
        $status['notification_message'] = $notification_message;
        delete_post_meta($post_id, 'scraping_requires_user_notification');
        delete_post_meta($post_id, 'scraping_notification_message');
    }
    
    wp_send_json_success($status);
}
add_action('wp_ajax_clear_video_notification', 'handle_clear_video_notification');
add_action('wp_ajax_nopriv_clear_video_notification', 'handle_clear_video_notification');

function handle_clear_video_notification() {
    if (!isset($_POST['post_id']) || !isset($_POST['nonce'])) {
        wp_die('Missing required parameters');
    }
    
    if (!wp_verify_nonce($_POST['nonce'], 'movie_scraping_nonce')) {
        wp_die('Security check failed');
    }
    
    $post_id = intval($_POST['post_id']);
    
    if (!$post_id) {
        wp_send_json_error('Invalid post ID');
        return;
    }
    delete_post_meta($post_id, 'scraping_video_unavailable');
    delete_post_meta($post_id, 'scraping_requires_user_notification');
    delete_post_meta($post_id, 'scraping_notification_message');
    delete_post_meta($post_id, 'scraping_notification_data');
    
    wp_send_json_success('Notification cleared');
}

function start_series_scraping_ajax() {
    check_ajax_referer('series_scraping_nonce', 'nonce');
    
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => 'ไม่มีสิทธิ์ในการดำเนินการ'));
    }
    
    $post_id = intval($_POST['post_id']);
    $series_url = esc_url_raw($_POST['series_url']);
    $target_season = isset($_POST['target_season']) ? intval($_POST['target_season']) : null;
    
    if (empty($post_id) || empty($series_url)) {
        wp_send_json_error(array('message' => 'ข้อมูลไม่ครบถ้วน'));
    }
    
    $post_type = get_post_type($post_id);
    if (!in_array($post_type, ['serie', 'anime'])) {
        wp_send_json_error(array('message' => 'โพสต์นี้ไม่ใช่ประเภทซีรี่ย์หรืออนิเมะ'));
    }
    
    require_once plugin_dir_path(__FILE__) . 'scrapers/SeriesScraper.php';
    
    $scraper = new SeriesScraper();
    $result = $scraper->scrape($post_id, $series_url, ['target_season' => $target_season]);
    
    if ($result) {
        wp_send_json_success(array('message' => 'เริ่มต้นการ scrape ซีรี่ย์เรียบร้อยแล้ว'));
    } else {
        wp_send_json_error(array('message' => 'ไม่สามารถเริ่มต้นการ scrape ซีรี่ย์ได้'));
    }
}
add_action('wp_ajax_start_series_scraping', 'start_series_scraping_ajax');

function test_series_detection_ajax() {
    check_ajax_referer('series_scraping_nonce', 'nonce');
    
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => 'ไม่มีสิทธิ์ในการดำเนินการ'));
    }
    
    $series_url = esc_url_raw($_POST['series_url']);
    
    if (empty($series_url)) {
        wp_send_json_error(array('message' => 'กรุณาใส่ URL ซีรี่ย์'));
    }
    
    require_once plugin_dir_path(__FILE__) . 'scrapers/SeriesScraper.php';
    
    $test_result = test_enhanced_series_detection($series_url);
    
    if ($test_result['success']) {
        wp_send_json_success(array(
            'message' => 'ทดสอบการตรวจจับข้อมูลซีรี่ย์สำเร็จ',
            'data' => $test_result['test_results'] ?? $test_result['data'] ?? []
        ));
    } else {
        wp_send_json_error(array('message' => 'ไม่สามารถทดสอบการตรวจจับข้อมูลซีรี่ย์ได้: ' . $test_result['error']));
    }
}
add_action('wp_ajax_test_series_detection', 'test_series_detection_ajax');

function clear_series_scraping_ajax() {
    check_ajax_referer('series_scraping_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    
    if ($post_id <= 0) {
        wp_send_json_error(array('message' => 'Post ID ไม่ถูกต้อง'));
    }
    
    require_once plugin_dir_path(__FILE__) . 'scrapers/SeriesScraper.php';
    $scraper = new SeriesScraper();
    $result = $scraper->clear_scraping_data($post_id);
    
    if ($result['success']) {
        wp_send_json_success(array('message' => 'ล้างข้อมูลสำเร็จ'));
    } else {
        wp_send_json_error(array('message' => $result['error'] ?? 'เกิดข้อผิดพลาดในการล้างข้อมูล'));
    }
}
add_action('wp_ajax_clear_series_scraping', 'clear_series_scraping_ajax');

function cleanup_post_data_before_delete($post_id) {
    $post_type = get_post_type($post_id);

    if (!in_array($post_type, ['movie', 'serie', 'anime', 'adult'])) {
        return;
    }

    error_log("Cleaning up data for post ID: $post_id (type: $post_type) before deletion");

    if ($post_type === 'movie') {
        require_once plugin_dir_path(__FILE__) . 'scrapers/MovieScraper.php';
        $scraper = new MovieScraper();
        $scraper->clear_scraping_data($post_id);
        error_log("Movie scraping data cleared for post ID: $post_id");
    } elseif ($post_type === 'serie') {
        require_once plugin_dir_path(__FILE__) . 'scrapers/SeriesScraper.php';
        $scraper = new SeriesScraper();
        $result = $scraper->clear_scraping_data($post_id);
        error_log("Series scraping data cleared for post ID: $post_id - " . ($result['success'] ? 'Success' : 'Failed'));
    }

    delete_attached_media($post_id);
    error_log("Attached media deleted for post ID: $post_id");

    $upload_dir = wp_upload_dir();
    $post_cache_dir = $upload_dir['basedir'] . '/CacheStream/post-' . $post_id . '/';
    if (is_dir($post_cache_dir)) {
        delete_directory_recursive($post_cache_dir);
        error_log("Cache directory deleted: $post_cache_dir");
    }

    $video_cache_file = $upload_dir['basedir'] . '/CacheStream/video_url_' . $post_id . '.txt';
    if (file_exists($video_cache_file)) {
        unlink($video_cache_file);
        error_log("Video cache file deleted: $video_cache_file");
    }
}

function delete_directory_recursive($dir) {
    if (!is_dir($dir)) {
        return;
    }

    $files = array_diff(scandir($dir), ['.', '..']);

    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        if (is_dir($path)) {
            delete_directory_recursive($path);
        } else {
            unlink($path);
        }
    }

    rmdir($dir);
}

add_action('before_delete_post', 'cleanup_post_data_before_delete');

function test_source_change_detection_ajax() {
    check_ajax_referer('series_scraping_nonce', 'nonce');

    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => 'ไม่มีสิทธิ์ในการดำเนินการ'));
    }

    $post_id = intval($_POST['post_id']);
    $test_url = esc_url_raw($_POST['test_url']);

    if (empty($post_id) || empty($test_url)) {
        wp_send_json_error(array('message' => 'ข้อมูลไม่ครบถ้วน'));
    }

    require_once plugin_dir_path(__FILE__) . 'scrapers/SeriesScraper.php';
    $scraper = new SeriesScraper();

    $stored_url = get_post_meta($post_id, 'scraper_source_url', true);
    $stored_website = get_post_meta($post_id, 'scraper_source_website', true);
    $last_change_type = get_post_meta($post_id, 'scraper_last_change_type', true);

    $change_type = $scraper->check_source_change($post_id, $test_url);

    $response_data = array(
        'stored_url' => $stored_url ?: 'ไม่มี',
        'stored_website' => $stored_website ?: 'ไม่มี',
        'test_url' => $test_url,
        'detected_website' => $scraper->detect_website($test_url),
        'change_type' => $change_type,
        'last_change_type' => $last_change_type ?: 'ไม่มี',
        'action_description' => get_change_type_description($change_type)
    );

    wp_send_json_success($response_data);
}

function get_change_type_description($change_type) {
    switch ($change_type) {
        case 'first_time':
            return 'ครั้งแรก - จะเก็บข้อมูลแหล่งที่มาและดำเนินการ scraping ปกติ';
        case 'website_changed':
            return 'เปลี่ยนเว็บไซต์ - จะลบข้อมูลเดิมทั้งหมดและเริ่มใหม่';
        case 'url_changed':
            return 'เปลี่ยน URL - จะอัพเดทแบบ incremental (เพิ่มตอนใหม่/อัพเดทตอนที่เปลี่ยน)';
        case 'same_source':
            return 'แหล่งเดิม - จะอัพเดทแบบ incremental (เพิ่มตอนใหม่/อัพเดทตอนที่เปลี่ยน)';
        default:
            return 'ไม่ทราบประเภทการเปลี่ยนแปลง';
    }
}

add_action('wp_ajax_test_source_change_detection', 'test_source_change_detection_ajax');

function check_series_scraping_status_ajax() {
    check_ajax_referer('series_scraping_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    
    if ($post_id <= 0) {
        wp_send_json_error(array('message' => 'Post ID ไม่ถูกต้อง'));
    }
    
    $scraping_status = get_post_meta($post_id, 'scraping_status', true);
    $dubbed_episodes = get_post_meta($post_id, 'scraper_dubbed_episodes', true) ?: [];
    $subbed_episodes = get_post_meta($post_id, 'scraper_subbed_episodes', true) ?: [];
    $episodes_count = max(count($dubbed_episodes), count($subbed_episodes));
    
    $overall_status = $scraping_status ?: 'pending';
    
    if ($overall_status === 'processing' && $episodes_count > 0) {
        $overall_status = 'completed';
        update_post_meta($post_id, 'scraping_status', 'completed');
    }
    
    $imdb_rating = get_post_meta($post_id, 'imdb_rating', true);
    $duration = get_post_meta($post_id, 'movie_duration', true);
    $title = get_the_title($post_id);
    $youtube_url = get_post_meta($post_id, 'linkvideo', true);
    $featured_image_url = get_post_meta($post_id, '_featured_image_url', true);
    $auto_fill_data = [];
    
    if ($imdb_rating) {
        $auto_fill_data['imdb_rating'] = $imdb_rating;
    }
    if ($duration) {
        $auto_fill_data['duration'] = $duration;
    }
    if ($title && $title !== 'Auto Draft') {
        $auto_fill_data['title'] = $title;
    }
    if ($youtube_url) {
        $auto_fill_data['youtube_url'] = $youtube_url;
    }
    if ($featured_image_url) {
        $auto_fill_data['featured_image_url'] = $featured_image_url;
    }
    
    $progress = get_post_meta($post_id, 'scraping_progress', true) ?: 0;
    $scraping_message = get_post_meta($post_id, 'scraping_message', true);

    $message = $scraping_message ?: (($overall_status === 'completed') ? 'การ scraping เสร็จสิ้น' : 'กำลังประมวลผล...');

    wp_send_json_success(array(
        'status' => $overall_status,
        'progress' => intval($progress),
        'episodes_count' => $episodes_count,
        'dubbed_count' => count($dubbed_episodes),
        'subbed_count' => count($subbed_episodes),
        'auto_fill_data' => $auto_fill_data,
        'metadata_extracted' => !empty($auto_fill_data),
        'message' => $message
    ));
}
add_action('wp_ajax_check_series_scraping_status', 'check_series_scraping_status_ajax');

function get_series_episodes_html_ajax() {
    check_ajax_referer('series_scraping_nonce', 'nonce');
    
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array('message' => 'ไม่มีสิทธิ์ในการดำเนินการ'));
    }
    
    $post_id = intval($_POST['post_id']);
    
    if (empty($post_id)) {
        wp_send_json_error(array('message' => 'ข้อมูลไม่ครบถ้วน'));
    }
    
    $dubbed_episodes = get_post_meta($post_id, 'scraper_dubbed_episodes', true) ?: [];
    $subbed_episodes = get_post_meta($post_id, 'scraper_subbed_episodes', true) ?: [];
    $series_info = get_post_meta($post_id, 'scraper_series_info', true) ?: [];
    
    $all_episodes = [];
    
    foreach ($dubbed_episodes as $key => $episode) {
        $season = $episode['season'] ?? 1;
        $episode_num = $episode['episode'] ?? 1;
        $sort_key = sprintf('%03d_%03d', $season, $episode_num);
        
        if (!isset($all_episodes[$sort_key])) {
            $all_episodes[$sort_key] = [
                'season' => $season,
                'episode' => $episode_num,
                'title' => $episode['title'] ?? "Episode $episode_num",
                'duration' => $episode['duration'] ?? null,
                'status' => $episode['status'] ?? 'available',
                'quality' => $episode['quality'] ?? 'Standard',
                'date_added' => $episode['date_added'] ?? '',
                'dubbed' => true,
                'subbed' => false,
                'master_original_url' => $episode['master_original_url'] ?? ''
            ];
        } else {
            $all_episodes[$sort_key]['dubbed'] = true;
            if (!empty($episode['master_original_url']) && empty($all_episodes[$sort_key]['master_original_url'])) {
                $all_episodes[$sort_key]['master_original_url'] = $episode['master_original_url'];
            }
        }
    }
    
    foreach ($subbed_episodes as $key => $episode) {
        $season = $episode['season'] ?? 1;
        $episode_num = $episode['episode'] ?? 1;
        $sort_key = sprintf('%03d_%03d', $season, $episode_num);
        
        if (!isset($all_episodes[$sort_key])) {
            $all_episodes[$sort_key] = [
                'season' => $season,
                'episode' => $episode_num,
                'title' => $episode['title'] ?? "Episode $episode_num",
                'duration' => $episode['duration'] ?? null,
                'status' => $episode['status'] ?? 'available',
                'quality' => $episode['quality'] ?? 'Standard',
                'date_added' => $episode['date_added'] ?? '',
                'dubbed' => false,
                'subbed' => true,
                'master_original_url' => $episode['master_original_url'] ?? ''
            ];
        } else {
            $all_episodes[$sort_key]['subbed'] = true;
            if (!empty($episode['master_original_url']) && empty($all_episodes[$sort_key]['master_original_url'])) {
                $all_episodes[$sort_key]['master_original_url'] = $episode['master_original_url'];
            }
        }
    }
    
    ksort($all_episodes);
    
    ob_start();
    echo '<h3 style="margin-top: 0; color: #333;">📺 Episodes List</h3>';
    
    if (empty($all_episodes)) {
        echo '<p style="color: #666; font-style: italic; margin: 10px 0;">No episodes found. Run scraping to populate episodes.</p>';
    } else {
        echo '<div style="margin-bottom: 15px; padding: 10px; background: #f0f8ff; border-radius: 6px;">';
        echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; text-align: center;">';
        
        if (!empty($series_info)) {
            if (isset($series_info['total_seasons'])) {
                echo '<div><strong>🎬 Total Seasons:</strong> ' . esc_html($series_info['total_seasons']) . '</div>';
            }
            if (isset($series_info['total_dubbed_episodes'])) {
                echo '<div><strong>🎙️ Dubbed Episodes:</strong> ' . esc_html($series_info['total_dubbed_episodes']) . '</div>';
            }
            if (isset($series_info['total_subbed_episodes'])) {
                echo '<div><strong>💬 Subbed Episodes:</strong> ' . esc_html($series_info['total_subbed_episodes']) . '</div>';
            }
        }
        echo '</div>';
        echo '</div>';
        
        echo '<div id="episodes_list" style="max-height: 400px; overflow-y: auto;">';
        foreach ($all_episodes as $episode) {
            echo '<div class="episode-item" style="border: 1px solid #ddd; margin: 5px 0; padding: 10px; border-radius: 5px; background: white;">';
            echo '<div style="display: flex; justify-content: space-between; align-items: center;">';
            echo '<div style="flex: 1;">';
            echo '<h4 style="margin: 0 0 5px 0; color: #2c3e50;">S' . esc_html($episode['season']) . 'E' . esc_html($episode['episode']) . ': ' . esc_html($episode['title']) . '</h4>';
            
            if (!empty($episode['duration'])) {
                echo '<p style="margin: 5px 0 0 0; color: #888; font-size: 12px;">⏰ Duration: ' . esc_html($episode['duration']) . ' minutes</p>';
            }
            if (!empty($episode['quality'])) {
                echo '<p style="margin: 5px 0 0 0; color: #888; font-size: 12px;">📺 Quality: ' . esc_html($episode['quality']) . '</p>';
            }
            if (!empty($episode['date_added'])) {
                echo '<p style="margin: 5px 0 0 0; color: #888; font-size: 12px;">📅 Added: ' . esc_html(date('Y-m-d H:i', strtotime($episode['date_added']))) . '</p>';
            }

            if (!empty($episode['master_original_url'])) {
                echo '<div style="margin: 8px 0 0 0; padding: 6px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007cba;">';
                echo '<p style="margin: 0; color: #495057; font-size: 11px; font-weight: bold;">🔗 Original Master URL:</p>';
                echo '<p style="margin: 2px 0 0 0; color: #6c757d; font-size: 10px; word-break: break-all; font-family: monospace;">' . esc_html($episode['master_original_url']) . '</p>';
                echo '</div>';
            }
            echo '</div>';
            echo '<div style="display: flex; gap: 10px; align-items: center;">';
            
            if ($episode['dubbed']) {
                echo '<span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold;">🎙️ พากย์ไทย</span>';
            }
            if ($episode['subbed']) {
                echo '<span style="background: #007cba; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold;">💬 ซับไทย</span>';
            }
            
            $status_color = $episode['status'] === 'available' ? '#46b450' : '#dc3545';
            echo '<span style="background: ' . $status_color . '; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold;">' . esc_html(ucfirst($episode['status'])) . '</span>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
        }
        echo '</div>';
        
        if (!empty($series_info['scraping_message'])) {
            echo '<div style="margin-top: 15px; padding: 10px; background: #e8f5e8; border: 1px solid #4caf50; border-radius: 6px;">';
            echo '<strong style="color: #2e7d32;">📊 Scraping Summary:</strong> ';
            echo '<span style="color: #388e3c;">' . esc_html($series_info['scraping_message']) . '</span>';
            echo '</div>';
        }

        $dubbed_original_urls = [];
        $subbed_original_urls = [];
        $max_episode = 0;

        foreach ($all_episodes as $episode) {
            $max_episode = max($max_episode, $episode['episode']);
        }

        for ($i = 1; $i <= $max_episode; $i++) {
            $dubbed_original_urls[$i - 1] = '';
            $subbed_original_urls[$i - 1] = '';
        }

        foreach ($all_episodes as $episode) {
            $episode_index = $episode['episode'] - 1;
            $original_url = $episode['master_original_url'] ?? '';

            if ($episode['dubbed']) {
                $dubbed_original_urls[$episode_index] = $original_url;
            }
            if ($episode['subbed']) {
                $subbed_original_urls[$episode_index] = $original_url;
            }
        }

        if (!empty($dubbed_original_urls) || !empty($subbed_original_urls)) {
            echo '<div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px;">';
            echo '<h4 style="margin: 0 0 10px 0; color: #495057; font-size: 14px;">🔗 Scraper Original URLs (Filtered Episodes)</h4>';

            if (!empty($dubbed_original_urls)) {
                echo '<div style="margin-bottom: 10px;">';
                $dubbed_count = count(array_filter($dubbed_original_urls));
                echo '<strong style="color: #28a745; font-size: 12px;">🎙️ พากย์ไทย (' . $dubbed_count . '/' . count($dubbed_original_urls) . ' episodes):</strong>';
                echo '<div style="max-height: 150px; overflow-y: auto; margin-top: 5px;">';
                foreach ($dubbed_original_urls as $index => $url) {
                    $episode_num = $index + 1;
                    if (!empty($url)) {
                        echo '<div style="margin: 3px 0; padding: 4px 6px; background: white; border-radius: 3px; border-left: 3px solid #28a745;">';
                        echo '<span style="font-size: 10px; color: #6c757d; font-weight: bold;">EP' . $episode_num . ':</span> ';
                        echo '<span style="font-size: 10px; color: #495057; font-family: monospace; word-break: break-all;">' . esc_html($url) . '</span>';
                        echo '</div>';
                    } else {
                        echo '<div style="margin: 3px 0; padding: 4px 6px; background: #f8f9fa; border-radius: 3px; border-left: 3px solid #dc3545;">';
                        echo '<span style="font-size: 10px; color: #6c757d; font-weight: bold;">EP' . $episode_num . ':</span> ';
                        echo '<span style="font-size: 10px; color: #dc3545; font-style: italic;">ไม่มีข้อมูล (No data)</span>';
                        echo '</div>';
                    }
                }
                echo '</div>';
                echo '</div>';
            }

            if (!empty($subbed_original_urls)) {
                echo '<div>';
                $subbed_count = count(array_filter($subbed_original_urls));
                echo '<strong style="color: #007cba; font-size: 12px;">💬 ซับไทย (' . $subbed_count . '/' . count($subbed_original_urls) . ' episodes):</strong>';
                echo '<div style="max-height: 150px; overflow-y: auto; margin-top: 5px;">';
                foreach ($subbed_original_urls as $index => $url) {
                    $episode_num = $index + 1;
                    if (!empty($url)) {
                        echo '<div style="margin: 3px 0; padding: 4px 6px; background: white; border-radius: 3px; border-left: 3px solid #007cba;">';
                        echo '<span style="font-size: 10px; color: #6c757d; font-weight: bold;">EP' . $episode_num . ':</span> ';
                        echo '<span style="font-size: 10px; color: #495057; font-family: monospace; word-break: break-all;">' . esc_html($url) . '</span>';
                        echo '</div>';
                    } else {
                        echo '<div style="margin: 3px 0; padding: 4px 6px; background: #f8f9fa; border-radius: 3px; border-left: 3px solid #dc3545;">';
                        echo '<span style="font-size: 10px; color: #6c757d; font-weight: bold;">EP' . $episode_num . ':</span> ';
                        echo '<span style="font-size: 10px; color: #dc3545; font-style: italic;">ไม่มีข้อมูล (No data)</span>';
                        echo '</div>';
                    }
                }
                echo '</div>';
                echo '</div>';
            }

            echo '</div>';
        }

    }
    
    $html = ob_get_clean();
    
    wp_send_json_success(array(
        'html' => $html,
        'episodes_count' => count($all_episodes),
        'dubbed_count' => count($dubbed_episodes),
        'subbed_count' => count($subbed_episodes)
    ));
}
add_action('wp_ajax_get_series_episodes_html', 'get_series_episodes_html_ajax');

function series_scraper_meta_box_callback($post) {
    $post_type = get_post_type($post);
    $series_url = get_post_meta($post->ID, 'series_url', true);
    $scraping_mode = get_option('vu_scraping_mode', false);
    
    wp_nonce_field('series_scraping_nonce', 'series_scraping_nonce');
    
    if ($scraping_mode) {
        echo '<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">';
        echo '<h3 style="color: white; margin: 0 0 15px 0; font-size: 18px; text-align: center;">🎬 Series Scraping System</h3>';
        echo '<label for="series_url" style="color: white; font-weight: bold; display: block; margin-bottom: 8px;">Series URL to Scrape:</label>';
        echo '<div style="display: flex; gap: 10px; align-items: center;">';
        echo '<input type="text" id="series_url" name="series_url" value="' . esc_attr($series_url) . '" style="flex: 1; padding: 12px; border: none; border-radius: 6px; font-size: 14px;" placeholder="เช่น https://22-hdd.com/series-name/">';
        echo '<button type="button" id="start_series_scraping" class="button button-primary" style="padding: 12px 20px; height: auto; background: #28a745; border: none; border-radius: 6px; color: white; font-weight: bold;">🚀 Start Scraping</button>';
        echo '<button type="button" id="clear_series_scraping" class="button button-secondary" style="padding: 12px 20px; height: auto; background: #dc3545; border: none; border-radius: 6px; color: white; font-weight: bold;">🗑️ Clear Data</button>';
        echo '</div>';

        echo '<div id="series_scraping_progress" style="margin-top: 15px; display: none; background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; border: 1px solid rgba(255,255,255,0.2);">
            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                <div style="width: 20px; height: 20px; border: 2px solid rgba(255,255,255,0.3); border-top: 2px solid #fff; border-radius: 50%; animation: spin 1s linear infinite; margin-right: 10px;"></div>
                <span style="color: #fff; font-weight: bold; font-size: 14px;" id="series_progress_text">เริ่มต้นการ scraping...</span>
            </div>
            <div style="background: rgba(255,255,255,0.2); border-radius: 10px; height: 12px; overflow: hidden;">
                <div id="series_progress_bar" style="background: linear-gradient(90deg, #00d4ff, #00b4d8, #0077b6); height: 100%; width: 0%; transition: width 0.5s ease; border-radius: 10px; position: relative;">
                    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%); animation: shimmer 2s infinite;"></div>
                </div>
            </div>
            <div id="series_progress_details" style="color: rgba(255,255,255,0.8); font-size: 12px; margin-top: 8px;"></div>
        </div>';

        echo '</div>';
        
        $scraping_dubbed_status = get_post_meta($post->ID, 'scraping_dubbed_status', true);
        $scraping_subbed_status = get_post_meta($post->ID, 'scraping_subbed_status', true);
        $dubbed_episodes = get_post_meta($post->ID, 'scraper_dubbed_episodes', true) ?: [];
        $subbed_episodes = get_post_meta($post->ID, 'scraper_subbed_episodes', true) ?: [];
        $series_info = get_post_meta($post->ID, 'scraper_series_info', true) ?: [];
        
        echo '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">';
        echo '<div>';
        echo '<label for="imdb_rating" class="custom-meta-box-label">⭐ IMDB Rating</label>';
        $imdb_rating = get_post_meta($post->ID, 'imdb_rating', true);
        echo '<input type="text" id="imdb_rating" name="imdb_rating" value="' . esc_attr($imdb_rating) . '" class="custom-full-width-input" placeholder="เช่น 8.5" style="width: 100%;">';
        echo '</div>';
        echo '<div>';
        echo '<label for="duration" class="custom-meta-box-label">⏰ Duration (นาที)</label>';
        $duration = get_post_meta($post->ID, 'movie_duration', true);
        echo '<input type="text" id="duration" name="movie_duration" value="' . esc_attr($duration) . '" class="custom-full-width-input" placeholder="เช่น 120" style="width: 100%;">';
        echo '</div>';
        echo '</div>';
        
        if (!empty($series_url)) {
            echo '<div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border: 1px solid #4a90e2; border-radius: 6px;">';
            echo '<label style="display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50;">🔗 Scraping URL:</label>';
            echo '<a href="' . esc_url($series_url) . '" target="_blank" style="color: #4a90e2; text-decoration: none; word-break: break-all;">' . esc_html($series_url) . '</a>';
            echo '</div>';
        }
        
        echo '<div id="series_episodes_repeater" style="margin-top: 20px; border: 1px solid #ddd; border-radius: 8px; padding: 15px; background: #f9f9f9;">';
        echo '<h3 style="margin-top: 0; color: #333;">📺 Episodes List</h3>';
        
        if (empty($dubbed_episodes) && empty($subbed_episodes)) {
            echo '<p style="color: #666; font-style: italic; margin: 10px 0;">No episodes found. Run scraping to populate episodes.</p>';
        } else {
            echo '<div style="margin-bottom: 15px; padding: 10px; background: #f0f8ff; border-radius: 6px;">';
            echo '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; text-align: center;">';
            
            if (!empty($series_info)) {
                if (isset($series_info['total_seasons'])) {
                    echo '<div><strong>🎬 Total Seasons:</strong> ' . esc_html($series_info['total_seasons']) . '</div>';
                }
                echo '<div><strong>🎙️ Dubbed Episodes:</strong> ' . count($dubbed_episodes) . '</div>';
                echo '<div><strong>💬 Subbed Episodes:</strong> ' . count($subbed_episodes) . '</div>';
                echo '<div><strong>📊 Total Episodes:</strong> ' . max(count($dubbed_episodes), count($subbed_episodes)) . '</div>';
            }
            echo '</div>';
            echo '</div>';
            
            $all_episodes = [];
            
            foreach ($dubbed_episodes as $key => $episode) {
                $season = $episode['season'] ?? 1;
                $episode_num = $episode['episode'] ?? 1;
                $sort_key = sprintf('%03d_%03d', $season, $episode_num);
                
                if (!isset($all_episodes[$sort_key])) {
                    $all_episodes[$sort_key] = [
                        'season' => $season,
                        'episode' => $episode_num,
                        'title' => $episode['title'] ?? "Episode $episode_num",
                        'duration' => $episode['duration'] ?? null,
                        'status' => $episode['status'] ?? 'available',
                        'quality' => $episode['quality'] ?? 'Standard',
                        'date_added' => $episode['date_added'] ?? '',
                        'dubbed' => true,
                        'subbed' => false,
                        'master_original_url' => $episode['master_original_url'] ?? ''
                    ];
                } else {
                    $all_episodes[$sort_key]['dubbed'] = true;
                    if (!empty($episode['master_original_url']) && empty($all_episodes[$sort_key]['master_original_url'])) {
                        $all_episodes[$sort_key]['master_original_url'] = $episode['master_original_url'];
                    }
                }
            }
            
            foreach ($subbed_episodes as $key => $episode) {
                $season = $episode['season'] ?? 1;
                $episode_num = $episode['episode'] ?? 1;
                $sort_key = sprintf('%03d_%03d', $season, $episode_num);
                
                if (!isset($all_episodes[$sort_key])) {
                    $all_episodes[$sort_key] = [
                        'season' => $season,
                        'episode' => $episode_num,
                        'title' => $episode['title'] ?? "Episode $episode_num",
                        'duration' => $episode['duration'] ?? null,
                        'status' => $episode['status'] ?? 'available',
                        'quality' => $episode['quality'] ?? 'Standard',
                        'date_added' => $episode['date_added'] ?? '',
                        'dubbed' => false,
                        'subbed' => true,
                        'master_original_url' => $episode['master_original_url'] ?? ''
                    ];
                } else {
                    $all_episodes[$sort_key]['subbed'] = true;
                    if (!empty($episode['master_original_url']) && empty($all_episodes[$sort_key]['master_original_url'])) {
                        $all_episodes[$sort_key]['master_original_url'] = $episode['master_original_url'];
                    }
                }
            }
            
            ksort($all_episodes);
            
            echo '<div id="episodes_list" style="max-height: 400px; overflow-y: auto;">';
            foreach ($all_episodes as $episode) {
                echo '<div class="episode-item" style="border: 1px solid #ddd; margin: 5px 0; padding: 10px; border-radius: 5px; background: white;">';
                echo '<div style="display: flex; justify-content: space-between; align-items: center;">';
                echo '<div style="flex: 1;">';
                echo '<h4 style="margin: 0 0 5px 0; color: #2c3e50;">S' . esc_html($episode['season']) . 'E' . esc_html($episode['episode']) . ': ' . esc_html($episode['title']) . '</h4>';
                
                if (!empty($episode['duration'])) {
                    echo '<p style="margin: 5px 0 0 0; color: #888; font-size: 12px;">⏰ Duration: ' . esc_html($episode['duration']) . ' minutes</p>';
                }
                if (!empty($episode['quality'])) {
                    echo '<p style="margin: 5px 0 0 0; color: #888; font-size: 12px;">📺 Quality: ' . esc_html($episode['quality']) . '</p>';
                }

                if (!empty($episode['master_original_url'])) {
                    echo '<div style="margin: 8px 0 0 0; padding: 6px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007cba;">';
                    echo '<p style="margin: 0; color: #495057; font-size: 11px; font-weight: bold;">🔗 Original Master URL:</p>';
                    echo '<p style="margin: 2px 0 0 0; color: #6c757d; font-size: 10px; word-break: break-all; font-family: monospace;">' . esc_html($episode['master_original_url']) . '</p>';
                    echo '</div>';
                }
                echo '</div>';
                echo '<div style="display: flex; gap: 10px; align-items: center;">';
                
                if ($episode['dubbed']) {
                    echo '<span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold;">🎙️ พากย์ไทย</span>';
                }
                if ($episode['subbed']) {
                    echo '<span style="background: #007cba; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold;">💬 ซับไทย</span>';
                }
                
                $status_color = $episode['status'] === 'available' ? '#46b450' : '#dc3545';
                echo '<span style="background: ' . $status_color . '; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px; font-weight: bold;">' . esc_html(ucfirst($episode['status'])) . '</span>';
                echo '</div>';
                echo '</div>';
                echo '</div>';
            }
            echo '</div>';


        }
        echo '</div>';

        echo '<style>
        .custom-meta-box-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .custom-full-width-input {
            width: 100%;
            box-sizing: border-box;
            margin-bottom: 10px;
        }
        .custom-status-width-input {
            width: 21%;
            box-sizing: border-box;
            display: inline-block;
            vertical-align: top;
            margin-bottom: 8px;
        }
        </style>';
        
        echo '<script>
    jQuery(document).ready(function($) {
            function updateField(fieldId, value, label, forceUpdate = false) {
                var currentValue = $("#" + fieldId).val();
                if (value && (currentValue === "" || forceUpdate)) {
                    $("#" + fieldId).val(value);
                    showProgressUpdate(label + " ✓");
                    return true;
                }
                return false;
            }
            
            function updateSeriesMetadata(data) {
                var updated = false;
                
                if (data.imdb_rating) {
                    updated = updateField("series_imdb_rating", data.imdb_rating, "IMDB Rating") || updated;
                }
                
                if (data.duration) {
                    updated = updateField("series_duration", data.duration, "Duration") || updated;
                }
                
                if (data.title) {
                    var currentTitle = $("#title").val();
                    if (!currentTitle || currentTitle.trim() === "" || currentTitle === "Auto Draft") {
                        $("#title").val(data.title);
                        updated = true;
                        showProgressUpdate("Title ✓");
                    }
                }
                
                if (data.youtube_url && $("#linkvideo").val() === "") {
                    $("#linkvideo").val(data.youtube_url);
                    updated = true;
                    showProgressUpdate("YouTube URL ✓");
                }
                
                if (data.featured_image_url && $("#_featured_image_url").val() === "") {
                    $("#_featured_image_url").val(data.featured_image_url);
                    updated = true;
                    showProgressUpdate("Featured Image URL ✓");
                }
                
                return updated;
            }
            
            var initialUrl = $("#series_url").val().trim();
            if (initialUrl && initialUrl.length > 10) {
                $("#start_series_scraping").removeClass("button-secondary").addClass("button-primary").css("background", "#28a745");
            }
        
        function isValidUrl(string) {
            try {
                new URL(string);
                return true;
            } catch (e) {
                return false;
            }
        }
        
        function showValidationPopup(title, message) {
                if (typeof Swal !== "undefined") {
                Swal.fire({
                    title: title,
                    text: message,
                        icon: "error",
                        confirmButtonText: "ตกลง"
                });
            } else {
                    alert(title + "\n" + message);
            }
        }
        
                    function showProgressUpdate(message) {
                $("#series_progress_details").html(message);
            }
            
            function refreshEpisodesRepeater(postId) {
                // ป้องกันการเรียกซ้ำภายในเวลาสั้นๆ
                var now = Date.now();
                if (window.lastRefreshTime && (now - window.lastRefreshTime) < 5000) {
                    console.log("Skipping refresh - too soon after last refresh");
                    return;
                }
                window.lastRefreshTime = now;
                
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "get_series_episodes_html",
                        post_id: postId,
                        nonce: "' . wp_create_nonce('series_scraping_nonce') . '"
                    },
                    success: function(response) {
                        if (response && response.success && response.data.html) {
                            $("#series_episodes_repeater").html(response.data.html);
                            showProgressUpdate("🔄 Episodes list updated (" + (response.data.episodes_count || 0) + " episodes)");
                        }
                    },
                    error: function() {
                        console.log("Failed to refresh episodes repeater");
                    }
                });
            }
            
            function checkSeriesScrapingStatus(postId) {
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "check_series_scraping_status",
                        post_id: postId,
                        nonce: "' . wp_create_nonce('series_scraping_nonce') . '"
                    },
                    success: function(response) {
                        if (response && response.success) {
                            var data = response.data;
                            var progress = data.progress || 0;

                            if (data.metadata_extracted && data.auto_fill_data) {
                                updateSeriesMetadata(data.auto_fill_data);
                            }

                            $("#series_progress_bar").css("width", progress + "%");

                            if (data.message) {
                                $("#series_progress_text").text(data.message);
                            }
                            
                            if (data.status === "completed") {
                                $("#series_progress_text").text("🎉 เสร็จสิ้นการ scraping!");
                                $("#start_series_scraping").prop("disabled", false).text("🚀 Start Scraping");
                                
                                var message = "✅ สำเร็จ!";
                                if (data.episodes_count) {
                                    message += " ประมวลผล " + data.episodes_count + " ตอน";
                                }
                                if (data.metadata_extracted) {
                                    var metadata_count = Object.keys(data.auto_fill_data || {}).length;
                                    message += " + Auto-fill " + metadata_count + " ฟิลด์";
                                }
                                showProgressUpdate(message);
                                
                                if (data.auto_fill_data && Object.keys(data.auto_fill_data).length > 0) {
                                    var summary = "<br><strong>📊 Auto-filled ข้อมูล:</strong><br>";
                                    if (data.auto_fill_data.title) {
                                        summary += "• ชื่อเรื่อง: " + data.auto_fill_data.title + "<br>";
                                        $("#title").val(data.auto_fill_data.title);
                                    }
                                    if (data.auto_fill_data.imdb_rating) {
                                        summary += "• IMDB Rating: " + data.auto_fill_data.imdb_rating + "/10<br>";
                                        $("#imdb_rating").val(data.auto_fill_data.imdb_rating);
                                    }
                                    if (data.auto_fill_data.duration) {
                                        summary += "• ระยะเวลา: " + data.auto_fill_data.duration + " นาที<br>";
                                        $("#duration").val(data.auto_fill_data.duration);
                                    }
                                    if (data.auto_fill_data.youtube_url) {
                                        summary += "• YouTube URL: ✓<br>";
                                        $("#custom_linkvideo").val(data.auto_fill_data.youtube_url);
                                    }
                                    if (data.auto_fill_data.featured_image_url) {
                                        summary += "• Featured Image URL: ✓<br>";
                                        $("#_featured_image_url").val(data.auto_fill_data.featured_image_url);
                                    }
                                    $("#series_progress_details").append(summary);
                                }
                                refreshEpisodesRepeater(postId);
                                window.seriesPollingCount = 0;
                                
                                setTimeout(function() {
                                    $("#series_scraping_progress").fadeOut();
                                }, 8000);
                            } else if (data.status === "failed") {
                                $("#series_progress_text").text("❌ เกิดข้อผิดพลาด: " + data.message);
                                $("#start_series_scraping").prop("disabled", false).text("🚀 Start Scraping");
                                showProgressUpdate("❌ ไม่สามารถดึงข้อมูลได้");
                            } else {
                                $("#series_progress_text").text("🔄 กำลังประมวลผล...");                    
                                var current_progress = parseInt($("#series_progress_bar").css("width")) || 0;
                                if (current_progress < 80) {
                                    $("#series_progress_bar").css("width", (current_progress + 10) + "%");
                                }
                                if (data.episodes_count > 0 && current_progress >= 40 && !window.episodesRefreshed) {
                                    window.episodesRefreshed = true;
                                    refreshEpisodesRepeater(postId);
                                }
                                window.seriesPollingCount = (window.seriesPollingCount || 0) + 1;                    
                                if (window.seriesPollingCount > 20) {
                                    $("#series_progress_text").text("⏰ หมดเวลาการประมวลผล - กำลังเติมข้อมูลที่มี...");
                                    refreshEpisodesRepeater(postId);
                                    $("#start_series_scraping").prop("disabled", false).text("🚀 Start Scraping");
                                    setTimeout(function() {
                                        $("#series_scraping_progress").fadeOut();
                                        window.seriesPollingCount = 0;
                                    }, 3000);
                                } else {
                                    setTimeout(function() {
                                        checkSeriesScrapingStatus(postId);
                                    }, 3000);
                                }
                            }
                        }
                    }
                });
            }
            
            function validateScrapingData() {
                var seriesUrl = $("#series_url").val().trim();
                var issues = [];
                
                if (!seriesUrl) {
                    issues.push("• URL ที่ต้องการ scrape");
                } else if (!isValidUrl(seriesUrl)) {
                    issues.push("• URL ไม่ถูกต้อง");
                }
                
                var message = "";
                if (issues.length > 0) {
                    message += "❌ ข้อมูลที่ต้องแก้ไข:\n" + issues.join("\n") + "\n\n";
                }
                
                return {
                    isValid: issues.length === 0,
                    message: message || "✅ ข้อมูลพร้อมสำหรับ scraping"
                };
            }
            
            $("#start_series_scraping").on("click", function() {
                var seriesUrl = $("#series_url").val().trim();
                var postId = ' . intval($post->ID) . ';
                var validationResult = validateScrapingData();
                if (!validationResult.isValid) {
                    showValidationPopup("📋 ตรวจสอบข้อมูลก่อน Scraping", validationResult.message);
                    return;
                }
                                    $(this).prop("disabled", true).text("🔄 เริ่มต้น...");
                    $("#series_scraping_progress").fadeIn();
                    $("#series_progress_bar").css("width", "0%");
                    $("#series_progress_text").text("🚀 เริ่มต้นการ scraping...");
                window.seriesPollingCount = 0;
                window.episodesRefreshed = false;
                window.lastRefreshTime = 0;
                
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "start_series_scraping",
                        post_id: postId,
                        series_url: seriesUrl,
                        nonce: "' . wp_create_nonce('series_scraping_nonce') . '"
                    },
                    success: function(response) {
                        if (response && response.success) {
                            $("#series_progress_bar").css("width", "30%");
                            $("#series_progress_text").text("📡 เชื่อมต่อสำเร็จ กำลังดึงข้อมูล...");
                            setTimeout(function() {
                                checkSeriesScrapingStatus(postId);
                            }, 1000);
                        } else {
                            var message = "เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ";
                            if (response && response.data && response.data.message) {
                                message = response.data.message;
                            } else if (response && response.message) {
                                message = response.message;
                            }
                            $("#series_progress_text").text("❌ เกิดข้อผิดพลาด: " + message);
                            $("#start_series_scraping").prop("disabled", false).text("🚀 Start Scraping");
                        }
                    },
                    error: function() {
                        $("#series_progress_text").text("❌ เกิดข้อผิดพลาดในการเชื่อมต่อ");
                        $("#start_series_scraping").prop("disabled", false).text("🚀 Start Scraping");
                    }
                });
            });
        
            $("#series_url").on("input", function() {
                var url = $(this).val().trim();
                if (url && url.length > 10 && (url.includes("http") || url.includes("www"))) {
                    $("#start_series_scraping").removeClass("button-secondary").addClass("button-primary").text("🚀 Start Scraping").css("background", "#28a745");
                } else {
                    $("#start_series_scraping").removeClass("button-primary").addClass("button-secondary").text("🚀 Start Scraping").css("background", "#6c757d");
                }
            });
            
            var seriesScrapingTriggered = false;
            
            $("#series_url").on("paste", function(e) {
                setTimeout(function() {
                    var pastedUrl = $("#series_url").val().trim();
                    if (pastedUrl && pastedUrl.length > 10 && !seriesScrapingTriggered) {
                        seriesScrapingTriggered = true;
                        
                        if (!isValidUrl(pastedUrl)) {
                            showValidationPopup("❌ URL ไม่ถูกต้อง", "กรุณาใส่ URL ที่ถูกต้อง เช่น https://example.com");
                            return;
                        }
                        
                        var currentIMDb = $("#imdb_rating").val();
                        var currentDuration = $("#duration").val();
                        
                        if (!currentIMDb || !currentDuration) {
                            setTimeout(function() {
                                $("#start_series_scraping").trigger("click");
                            }, 1000);
                        }
                    }
                }, 100);
            });
            
            $("#clear_series_scraping").on("click", function() {
                showCustomClearConfirm();
            });
            
            function showCustomClearConfirm() {
                var modal = $("<div>", {
                    id: "clear-confirm-modal",
                    css: {
                        position: "fixed",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        backgroundColor: "rgba(0,0,0,0.7)",
                        zIndex: 999999,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center"
                    }
                });
                
                var dialog = $("<div>", {
                    css: {
                        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                        padding: "30px",
                        borderRadius: "15px",
                        color: "white",
                        textAlign: "center",
                        maxWidth: "500px",
                        width: "90%",
                        boxShadow: "0 20px 60px rgba(0,0,0,0.3)",
                        transform: "scale(0.8)",
                        transition: "all 0.3s ease"
                    }
                });
                
                dialog.html(`
                    <div style="font-size: 60px; margin-bottom: 20px;">🗑️</div>
                    <h3 style="margin: 0 0 15px 0; font-size: 24px;">ยืนยันการลบข้อมูล</h3>
                    <p style="margin: 0 0 25px 0; font-size: 16px; opacity: 0.9;">
                        คุณต้องการล้างข้อมูล scraping ทั้งหมดหรือไม่?<br>
                        <strong style="color: #ffeb3b;">⚠️ การดำเนินการนี้จะลบไฟล์และโฟลเดอร์ทั้งหมด</strong>
                    </p>
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button id="cancel-clear" style="padding: 12px 25px; border: none; border-radius: 8px; background: rgba(255,255,255,0.2); color: white; font-weight: bold; cursor: pointer; transition: all 0.3s;">❌ ยกเลิก</button>
                        <button id="confirm-clear" style="padding: 12px 25px; border: none; border-radius: 8px; background: #dc3545; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s;">🗑️ ลบข้อมูล</button>
                    </div>
                `);
                
                modal.append(dialog);
                $("body").append(modal);
                
                setTimeout(function() {
                    dialog.css("transform", "scale(1)");
                }, 50);
                
                $("#cancel-clear").on("click", function() {
                    closeModal();
                });
                
                $("#confirm-clear").on("click", function() {
                    closeModal();
                    performClearData();
                });
                
                modal.on("click", function(e) {
                    if (e.target === modal[0]) {
                        closeModal();
                    }
                });
                
                function closeModal() {
                    dialog.css("transform", "scale(0.8)");
                    setTimeout(function() {
                        modal.remove();
                    }, 300);
                }
            }
            
            function performClearData() {
                var postId = ' . intval($post->ID) . ';
                var clearBtn = $("#clear_series_scraping");
                
                clearBtn.prop("disabled", true).text("🔄 Clearing...");
                
                $.ajax({
                    url: ajaxurl,
                    type: "POST",
                    data: {
                        action: "clear_series_scraping",
                        post_id: postId,
                        nonce: "' . wp_create_nonce('series_scraping_nonce') . '"
                    },
                    success: function(response) {
                        if (response && response.success) {
                            showSuccessMessage("✅ ล้างข้อมูลและไฟล์เรียบร้อยแล้ว");
                        setTimeout(function() {
                                        location.reload();
                            }, 1500);
                                } else {
                            var message = "เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ";
                            if (response && response.data && response.data.message) {
                                message = response.data.message;
                            } else if (response && response.message) {
                                message = response.message;
                            }
                            showErrorMessage("❌ เกิดข้อผิดพลาด: " + message);
                        }
                    },
                    error: function() {
                        showErrorMessage("❌ เกิดข้อผิดพลาดในการเชื่อมต่อ");
                    },
                    complete: function() {
                        clearBtn.prop("disabled", false).text("🗑️ Clear Data");
                    }
                });
            }
            
            function showSuccessMessage(message) {
                showMessage(message, "#28a745");
            }
            
            function showErrorMessage(message) {
                showMessage(message, "#dc3545");
            }
            
            function showMessage(message, color) {
                var toast = $("<div>", {
                    css: {
                        position: "fixed",
                        top: "20px",
                        right: "20px",
                        background: color,
                        color: "white",
                        padding: "15px 25px",
                        borderRadius: "8px",
                        fontSize: "16px",
                        fontWeight: "bold",
                        zIndex: 999999,
                        boxShadow: "0 4px 15px rgba(0,0,0,0.3)",
                        transform: "translateX(400px)",
                        transition: "all 0.3s ease"
                    },
                    text: message
                });
                
                $("body").append(toast);
                
                setTimeout(function() {
                    toast.css("transform", "translateX(0)");
                }, 50);
                
                setTimeout(function() {
                    toast.css("transform", "translateX(400px)");
                    setTimeout(function() {
                        toast.remove();
                    }, 300);
                }, 3000);
            }
        });
        </script>';
    }
}

add_filter('manage_posts_columns', 'add_video_status_column');
add_action('manage_posts_custom_column', 'display_video_status_column', 10, 2);
add_filter('manage_edit-movie_sortable_columns', 'make_video_status_sortable');
add_filter('manage_edit-serie_sortable_columns', 'make_video_status_sortable');
add_filter('manage_edit-anime_sortable_columns', 'make_video_status_sortable');
add_filter('manage_edit-adult_sortable_columns', 'make_video_status_sortable');
add_action('pre_get_posts', 'handle_video_status_sorting');
add_action('admin_head', 'add_video_status_header_script');

function add_video_status_column($columns) {
    $columns['video_status'] = 'Video Status';
    return $columns;
}

function make_video_status_sortable($columns) {
    return $columns;
}



function display_video_status_column($column, $post_id) {
    if ($column === 'video_status') {
        $scraper = new MovieScraper();
        $status = $scraper->get_scraping_status($post_id);
        $requires_notification = get_post_meta($post_id, 'scraping_requires_user_notification', true);

        $badge_class = $requires_notification ? 'scraping-notification-badge' : '';
        $has_no_data = empty($status['status']) && empty($status['message']);

        echo '<div class="' . esc_attr($badge_class) . '" data-post-id="' . esc_attr($post_id) . '" data-monitor-scraping="true">';

        if ($has_no_data) {
            echo '<span class="video-status video-status-no-data">';
            echo 'ยังไม่ทำงาน';
            echo '</span>';
        } else {
            echo '<span class="video-status video-status-' . esc_attr($status['status']) . '">';
            echo esc_html($status['message'] ?: $status['status']);
            echo '</span>';
        }

        echo '</div>';

        if ($requires_notification) {
            $notification_message = get_post_meta($post_id, 'scraping_notification_message', true);
            echo '<div style="margin-top: 5px;">';
            echo '<small style="color: #e74c3c;">⚠ ' . esc_html($notification_message) . '</small>';
            echo '</div>';
        }
    }
}

function handle_video_status_sorting($query) {
    if (!is_admin() || !$query->is_main_query()) return;

    $status_filter = isset($_GET['status_filter']) ? $_GET['status_filter'] : 'all';

    if ($status_filter === 'no_data') {
        $query->set('meta_query', [
            'relation' => 'OR',
            [
                'key' => 'scraping_status',
                'compare' => 'NOT EXISTS'
            ],
            [
                'key' => 'scraping_status',
                'value' => '',
                'compare' => '='
            ]
        ]);
    } elseif ($status_filter === 'has_data') {
        $query->set('meta_query', [
            [
                'key' => 'scraping_status',
                'value' => '',
                'compare' => '!='
            ]
        ]);
    }

    $orderby = $query->get('orderby');
    if ($orderby === 'video_status') {
        $query->set('orderby', 'date');
        $query->set('order', 'DESC');
    }
}

function add_video_status_header_script() {
    global $pagenow, $typenow;
    if ($pagenow === 'edit.php' && in_array($typenow, ['movie', 'serie', 'anime', 'adult'])) {
        $current_url = remove_query_arg(['status_filter']);
        ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const statusHeader = document.getElementById('video_status');
            if (statusHeader) {
                const currentFilter = new URLSearchParams(window.location.search).get('status_filter') || 'all';
                statusHeader.style.cursor = 'pointer';

                let arrow = '↕';
                let statusText = 'ทั้งหมด';
                if (currentFilter === 'has_data') {
                    arrow = '●';
                    statusText = 'มีข้อมูล';
                } else if (currentFilter === 'no_data') {
                    arrow = '○';
                    statusText = 'ไม่มีข้อมูล';
                }

                const statusLink = statusHeader.querySelector('a');
                if (statusLink) {
                    statusLink.innerHTML = '<span>Video Status (' + statusText + ') ' + arrow + '</span>';
                    statusLink.style.textDecoration = 'none';
                    statusLink.style.color = 'inherit';
                }

                statusHeader.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    let nextFilter = 'has_data';
                    if (currentFilter === 'all') nextFilter = 'has_data';
                    else if (currentFilter === 'has_data') nextFilter = 'no_data';
                    else nextFilter = 'all';

                    const currentUrl = new URL(window.location);
                    currentUrl.searchParams.delete('orderby');
                    currentUrl.searchParams.delete('order');

                    if (nextFilter === 'all') {
                        currentUrl.searchParams.delete('status_filter');
                    } else {
                        currentUrl.searchParams.set('status_filter', nextFilter);
                    }
                    window.location.href = currentUrl.toString();
                };

                if (statusLink) {
                    statusLink.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        statusHeader.click();
                    };
                }
            }
        });
        </script>
        <?php
    }
}

add_action('admin_enqueue_scripts', 'enqueue_video_notification_scripts');
add_action('wp_enqueue_scripts', 'enqueue_video_notification_scripts');

function enqueue_video_notification_scripts() {
    wp_enqueue_script('jquery');
    wp_localize_script('jquery', 'movieScrapingAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('movie_scraping_nonce'),
        'movieScrapingNonce' => wp_create_nonce('movie_scraping_nonce')
    ));
}
add_action('admin_head', 'add_video_status_admin_styles');

function add_video_status_admin_styles() {
    echo '<style>
        .video-status-unavailable {
            color: #e74c3c;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .video-status-unavailable::before {
            content: "⚠";
            font-size: 1.2em;
        }
        
        .video-status-processing {
            color: #f39c12;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .video-status-processing::before {
            content: "⏳";
            font-size: 1.2em;
        }
        
        .video-status-completed {
            color: #27ae60;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .video-status-completed::before {
            content: "✅";
            font-size: 1.2em;
        }

        .video-status-no-data {
            color: #95a5a6;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .video-status-no-data::before {
            content: "⚪";
            font-size: 1.2em;
        }


    </style>';
}

add_action('admin_notices', 'show_auto_scraping_notices');

function show_auto_scraping_notices() {
    global $post;
    
    if (!$post || !is_object($post)) {
        return;
    }
    
    $screen = get_current_screen();
    if ($screen->base !== 'post') {
        return;
    }
    
    $scraping_status = get_post_meta($post->ID, 'scraping_status', true);
    $requires_notification = get_post_meta($post->ID, 'scraping_requires_user_notification', true);
    $notification_message = get_post_meta($post->ID, 'scraping_notification_message', true);
    
    if ($requires_notification && $notification_message) {
        echo '<div class="notice notice-warning is-dismissible">';
        echo '<p><strong>Movie Scraping Notice:</strong> ' . esc_html($notification_message) . '</p>';
        echo '</div>';
        
        delete_post_meta($post->ID, 'scraping_requires_user_notification');
        delete_post_meta($post->ID, 'scraping_notification_message');
    }
    
    if ($scraping_status === 'processing') {
        echo '<div class="notice notice-info">';
        echo '<p><strong>Movie Scraping:</strong> Currently processing movie data in the background...</p>';
        echo '</div>';
    }
    
    if ($scraping_status === 'completed') {
        $last_updated = get_post_meta($post->ID, 'scraping_updated', true);
        if ($last_updated) {
            $time_diff = human_time_diff(strtotime($last_updated), current_time('timestamp'));
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>Movie Scraping:</strong> Successfully completed ' . $time_diff . ' ago.</p>';
            echo '</div>';
        }
    }
}