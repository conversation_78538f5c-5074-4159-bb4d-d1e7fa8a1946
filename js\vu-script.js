jQuery(document).ready(function($) {
    function connectWebSocket(videoId, videoUrl, serverUrl) {
        var socket = new WebSocket(serverUrl);
        socket.onopen = function() {
            var message = { video_id: videoId, video_url: videoUrl };
            socket.send(JSON.stringify(message));
        };
        socket.onmessage = function(event) {
            var data = JSON.parse(event.data);
            updateStatusInWP(data.video_id, data.status, data.url);
        };
        socket.onerror = function(error) {
            console.error('WebSocket Error: ' + error);
        };
        socket.onclose = function() {
            console.log('WebSocket connection closed');
        };
    }

    function updateStatusInWP(videoId, status, url) {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', ajaxurl + '?action=update_video_status', true);
        xhr.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
        var data = { post_id: videoId, status: status, video_url: url };
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                console.log('Status updated successfully');
            } else if (xhr.readyState === 4) {
                console.error('Failed to update status: ' + xhr.statusText);
            }
        };
        xhr.send(JSON.stringify(data));
    }

    function getServerForVideo(videoId, callback) {
        $.ajax({
            url: ajaxurl + '?action=get_server_for_video',
            method: 'POST',
            data: { video_id: videoId },
            success: function(response) {
                if (response.success) {
                    callback(response.data.server);
                } else {
                    console.error('Server information not found for video ID: ' + videoId);
                }
            },
            error: function() {
                console.error('Error fetching server information for video ID: ' + videoId);
            }
        });
    }

    function handleCdnSelect(select) {
        select.on('change', function() {
            if ($(this).val() === 'add-new') {
                var ip = $(this).closest('.vu-server-field').find('input[name$="[ip]"]').val();
                showCdnPopup(select, ip);
            }
        });
    }

    function showCdnPopup(select, ip) {
        var popup = $('#cdn-popup');
        var cdnListContainer = $('#cdn-list');
        $('#popup-ip').text(ip);
        popup.fadeIn();
        cdnListContainer.empty();
        select.find('option').each(function() {
            if ($(this).val() !== 'add-new') {
                cdnListContainer.append('<div class="cdn-list-item">' + $(this).val() + ' <button type="button" class="cdn-remove-button">Remove</button></div>');
            }
        });
        $('#add-cdn-field').off('click').on('click', function() {
            var cdnFieldHTML = '<div class="cdn-field"><input type="text" class="cdn-domain-input" placeholder="Enter CDN Domain"><button type="button" class="cdn-rm-button">Remove</button></div>';
            cdnListContainer.append(cdnFieldHTML);
        });
        $('#cdn-list').off('click', '.cdn-remove-button').on('click', '.cdn-remove-button', function() {
            $(this).closest('.cdn-list-item').remove();
        });
        $('#cdn-list').off('click', '.cdn-rm-button').on('click', '.cdn-rm-button', function() {
            $(this).closest('.cdn-field').remove();
        });
        $('#save-cdn-domain').off('click').on('click', function() {
            var cdnDomains = [];
            $('#cdn-list .cdn-list-item').each(function() {
                var text = $(this).text().replace('Remove', '').trim();
                cdnDomains.push(text);
            });
            $('#cdn-list .cdn-domain-input').each(function() {
                var cdnDomain = $(this).val().trim();
                if (cdnDomain) {
                    cdnDomains.push(cdnDomain);
                }
            });
            saveCdnDomains(cdnDomains, select, ip);
            popup.fadeOut();
        });
        $('#cancel-cdn-domain').off('click').on('click', function() {
            select.val('');
            popup.fadeOut();
        });
        $('#cdn-import-file').val('');
    }

    function saveCdnDomains(cdnDomains, select, ip) {
        if (!ip) {
            alert('Server IP is missing');
            return;
        }
        $.post(ajaxurl + '?action=save_cdn_domains', { cdn_domains: cdnDomains, server_ip: ip }, function(response) {
            if (response.success) {
                var newCdnList = response.data;
                select.find('option').remove();
                $.each(newCdnList, function(index, domain) {
                    select.append(new Option(domain, domain));
                });
                var cdnListContainer = $('#cdn-list');
                cdnListContainer.empty();
                $.each(newCdnList, function(i, domain) {
                    cdnListContainer.append('<div class="cdn-list-item">' + domain + ' <button type="button" class="cdn-remove-button">Remove</button></div>');
                });
                Swal.fire({
                    title: 'Success',
                    text: response.message,
                    icon: 'success',
                    confirmButtonText: 'OK'
                });
            } else {
                alert(response.data);
            }
        });
    }

    function exportCDN(ip) {
        $.post(ajaxurl + '?action=export_cdn_domains', { server_ip: ip }, function(response) {
            if (response.success) {
                var dataStr = 'data:application/json;charset=utf-8,' + encodeURIComponent(JSON.stringify(response.data, null, 2));
                var dlAnchor = document.createElement('a');
                dlAnchor.setAttribute('href', dataStr);
                dlAnchor.setAttribute('download', ip + '_cdn.json');
                document.body.appendChild(dlAnchor);
                dlAnchor.click();
                dlAnchor.remove();
            } else {
                alert(response.data);
            }
        });
    }

    function importCDN(ip, jsonData) {
        Swal.fire({title: 'Importing...', text: 'Please wait', allowOutsideClick: false, didOpen: () => {Swal.showLoading();}});
        $.post(ajaxurl + '?action=import_cdn_domains', { server_ip: ip, cdn_json: jsonData }, function(response) {
            Swal.close();
            if (response.success) {
                Swal.fire({title: 'Success', text: response.data.message, icon: 'success', confirmButtonText: 'OK'});
                var cdnList = response.data.cdn_list;
                var select = findSelectByIP(ip);
                if (select) {
                    select.find('option').remove();
                    $.each(cdnList, function(index, domain) {
                        select.append(new Option(domain, domain));
                    });
                }
                var cdnListContainer = $('#cdn-list');
                cdnListContainer.empty();
                $.each(cdnList, function(index, domain) {
                    cdnListContainer.append('<div class="cdn-list-item">' + domain + ' <button type="button" class="cdn-remove-button">Remove</button></div>');
                });
            } else {
                alert(response.data);
            }
        });
    }

    function clearCDN(ip) {
        Swal.fire({title: 'Clearing...', text: 'Please wait', allowOutsideClick: false, didOpen: () => {Swal.showLoading();}});
        $.post(ajaxurl + '?action=clear_cdn_domains', { server_ip: ip }, function(response) {
            Swal.close();
            if (response.success) {
                Swal.fire({title: 'Cleared', text: response.data.message, icon: 'success'});
                var select = findSelectByIP(ip);
                if (select) {
                    select.find('option').remove();
                }
                var cdnListContainer = $('#cdn-list');
                cdnListContainer.empty();
            } else {
                alert(response.data);
            }
        });
    }

    function findSelectByIP(ip) {
        var foundField = null;
        $('.vu-server-field').each(function() {
            var inputIP = $(this).find('input[name$="[ip]"]').val();
            if (inputIP === ip) {
                foundField = $(this).find('.cdn-select');
                return false;
            }
        });
        return foundField;
    }

    $(document).on('click', '.vu-add-cdn', function() {
        var select = $(this).siblings('.cdn-select');
        var ip = $(this).closest('.vu-server-field').find('input[name$="[ip]"]').val();
        if (!ip) {
            alert('Please enter IP first.');
            return;
        }
        showCdnPopup(select, ip);
    });

    $(document).on('click', '.vu-check-server', function() {
        var serverField = $(this).closest('.vu-server-field');
        var ip = serverField.find('input[name$="[ip]"]').val();
        $.ajax({
            url: ajaxurl + '?action=check_vu_server_connection',
            method: 'POST',
            data: { ip: ip },
            success: function(response) {
                var statusButton = serverField.find('.vu-server-status');
                if (response.success) {
                    statusButton.removeClass('not-connected').addClass('connected').text('Connected');
                } else {
                    statusButton.removeClass('connected').addClass('not-connected').text('Not Connected');
                }
            },
            error: function() {
                var statusButton = serverField.find('.vu-server-status');
                statusButton.removeClass('connected').addClass('not-connected').text('Error');
            }
        });
    });

    $('#cdn-export-btn').on('click', function() {
        var ip = $('#popup-ip').text();
        if (!ip) {
            alert('No IP found');
            return;
        }
        exportCDN(ip);
    });

    $('#cdn-import-btn').on('click', function() {
        var ip = $('#popup-ip').text();
        if (!ip) {
            alert('No IP found');
            return;
        }
        $('#cdn-import-file').click();
    });

    $('#cdn-import-file').on('change', function() {
        var file = this.files[0];
        if (!file) return;
        var ip = $('#popup-ip').text();
        var reader = new FileReader();
        reader.onload = function(e) {
            var content = e.target.result;
            importCDN(ip, content);
        };
        reader.readAsText(file);
    });

    $('#cdn-clear-btn').on('click', function() {
        var ip = $('#popup-ip').text();
        if (!ip) {
            alert('No IP found');
            return;
        }
        Swal.fire({
            title: 'Clear All?',
            text: 'Are you sure you want to remove all CDN domains for ' + ip + '?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, clear them!',
            cancelButtonText: 'No, cancel!'
        }).then((result) => {
            if (result.isConfirmed) {
                clearCDN(ip);
            }
        });
    });

    $('#vu-server-fields .vu-server-field').each(function() {
        var serverField = $(this);
        var ip = serverField.find('input[name$="[ip]"]').val();
        $.ajax({
            url: ajaxurl + '?action=check_vu_server_connection',
            method: 'POST',
            data: { ip: ip },
            success: function(response) {
                var statusButton = serverField.find('.vu-server-status');
                if (response.success) {
                    statusButton.removeClass('not-connected').addClass('connected').text('Connected');
                } else {
                    statusButton.removeClass('connected').addClass('not-connected').text('Not Connected');
                }
            },
            error: function() {
                var statusButton = serverField.find('.vu-server-status');
                statusButton.removeClass('connected').addClass('not-connected').text('Error');
            }
        });
    });

    $('#vu-add-video').on('click', function() {
        var videoId = $('#video-id').val();
        var videoUrl = $('#video-url').val();
        getServerForVideo(videoId, function(server) {
            var serverUrl = 'ws://' + server.ip + ':8080';
            connectWebSocket(videoId, videoUrl, serverUrl);
        });
    });

    $('#vu-clear').on('click', function() {
        $('.vu-add-video-form')[0].reset();
    });

    // Video Unavailable Notification System
    function checkVideoUnavailableNotifications() {
        const notificationElements = document.querySelectorAll('[data-notification-type="video_unavailable"]');
        
        notificationElements.forEach(element => {
            const postId = element.getAttribute('data-post-id');
            const message = element.getAttribute('data-message');
            const postTitle = element.getAttribute('data-post-title');
            
            if (postId && message) {
                showVideoUnavailableNotification(postTitle, message);
                element.removeAttribute('data-notification-type');
            }
        });
    }

    function showVideoUnavailableNotification(title, message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Video Unavailable',
                text: message,
                icon: 'warning',
                confirmButtonText: 'OK',
                customClass: {
                    popup: 'video-unavailable-popup'
                },
                allowOutsideClick: false,
                allowEscapeKey: false
            });
        } else {
            alert('Video Unavailable: ' + message);
        }
    }

    function updateVideoStatus(postId, status, message) {
        const statusElements = document.querySelectorAll(`[data-post-id="${postId}"] .video-status`);
        
        statusElements.forEach(element => {
            element.className = 'video-status';
            element.textContent = message || status;
            
            switch(status) {
                case 'video_unavailable':
                    element.classList.add('video-status-unavailable');
                    break;
                case 'processing':
                    element.classList.add('video-status-processing');
                    break;
                case 'completed':
                    element.classList.add('video-status-completed');
                    break;
                case 'no_data':
                    element.classList.add('video-status-no-data');
                    break;
                default:
                    element.classList.add('video-status-processing');
            }
        });
    }

    function addNotificationBadge(element) {
        if (!element.classList.contains('scraping-notification-badge')) {
            element.classList.add('scraping-notification-badge');
        }
    }

    function removeNotificationBadge(element) {
        element.classList.remove('scraping-notification-badge');
    }

    function monitorScrapingStatus() {
        const monitorElements = document.querySelectorAll('[data-monitor-scraping="true"]');
        
        monitorElements.forEach(element => {
            const postId = element.getAttribute('data-post-id');
            
            if (postId) {
                fetch(ajaxurl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=get_scraping_status&post_id=${postId}&nonce=${movieScrapingNonce}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateVideoStatus(postId, data.data.status, data.data.message);
                        
                        if (data.data.status === 'video_unavailable') {
                            addNotificationBadge(element);
                        } else if (data.data.status === 'completed') {
                            removeNotificationBadge(element);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error monitoring scraping status:', error);
                });
            }
        });
    }

    // Initialize notification system
    document.addEventListener('DOMContentLoaded', function() {
        checkVideoUnavailableNotifications();
        
        // Monitor scraping status every 30 seconds
        setInterval(monitorScrapingStatus, 30000);
        
        // Initial status check
        monitorScrapingStatus();
    });

    // Handle video unavailable notifications on page load
    if (typeof wp !== 'undefined' && wp.hooks) {
        wp.hooks.addAction('movie_scraping_completed', 'video-unavailable-notifications', function(postId, status) {
            if (status === 'video_unavailable') {
                const element = document.querySelector(`[data-post-id="${postId}"]`);
                if (element) {
                    addNotificationBadge(element);
                }
            }
        });
    }
});
