# 24-HD.com Research Findings

## Overview
การวิจัยเว็บไซต์ 24-hd.com เพื่อพัฒนา HD24MovieScraper สำหรับดึงข้อมูลหนังพากย์ไทย/ซับไทย

## Key Findings

### 1. Website Structure
- **URL Pattern**: `https://www.24-hd.com/[movie-name]/`
- **API Endpoint**: `https://api.24-hd.com/get.php`
- **Player Domain**: `main.24playerhd.com`
- **Cloudflare Protection**: ใช้ Cloudflare ต้องใช้ browser automation

### 2. Metadata Extraction
```json
{
  "title": "ดูหนัง Stolen (2012) คนโคตรระห่ำ เต็มเรื่อง 24-HD.COM",
  "ogTitle": "Stolen (2012) คนโคตรระห่ำ",
  "poster": "https://www.24-hd.com/wp-content/uploads/2023/09/Stolen-2012-คนโคตรระห่ำ.jpg",
  "movieTitle": "Stolen (2012) คนโคตรระห่ำ",
  "imdbRating": "5.5"
}
```

**Extraction Patterns:**
- Title: `<title>` tag
- Movie Title: `.movietext h1`
- Poster: `meta[property="og:image"]`
- IMDb Rating: `span.score` (contains base64 image + rating)

### 3. Language Detection
**Language Select Dropdown:**
```html
<select id="Lang_select">
  <option value="Thai">Thai</option>
  <option value="Sound Track">Sound Track</option>
</select>
```

**Language Types:**
- `Thai` = พากย์ไทย (Thai Dubbed)
- `Sound Track` = ซับไทย (Thai Subtitled)

### 4. AJAX API Calls
**Endpoint:** `https://api.24-hd.com/get.php`

**POST Data Pattern:**
```
action=halim_ajax_player&nonce=&episode=1&postid=[POST_ID]&lang=[LANGUAGE]&server=1
```

**Parameters:**
- `action`: `halim_ajax_player`
- `episode`: `1` (for movies)
- `postid`: Movie ID from page
- `lang`: `Thai` or `Sound Track`
- `server`: `1` (main server)

**Headers Required:**
```
Content-Type: application/x-www-form-urlencoded; charset=UTF-8
Referer: https://www.24-hd.com/
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
```

### 5. Player iframe Pattern
**iframe URL:**
```
https://main.24playerhd.com/index_th.php?id=[ENCODED_ID]&b=[POST_ID]
```

### 6. M3U8 URLs
**Pattern:**
```
https://main.24playerhd.com/newplaylist/[HASH]/[HASH].m3u8
```

**Different URLs for Different Languages:**
- Thai (พากย์): Different hash
- Sound Track (ซับ): Different hash

### 7. Comparison with SeriesDay
**Similarities:**
- Same API endpoint structure (`api.24-hd.com/get.php`)
- Same player domain (`main.24playerhd.com`)
- Same AJAX action (`halim_ajax_player`)
- Same header requirements

**Differences:**
- Movies use `episode=1` (not multiple episodes)
- Language options: `Thai`/`Sound Track` vs `Thai`/`English`
- Simpler structure (no season/episode complexity)

## Implementation Strategy

### 1. Browser Automation Required
- Must use Puppeteer/Selenium due to Cloudflare
- Cannot use simple HTTP requests

### 2. Data Extraction Flow
1. Navigate to movie page with browser
2. Extract metadata from DOM
3. Find language select dropdown
4. For each language option:
   - Select language
   - Trigger AJAX call
   - Capture M3U8 URL
5. Extract iframe URL and analyze player

### 3. Code Structure
```php
class HD24MovieScraper {
    // Similar to SeriesDay but simplified for movies
    // Use same AJAX patterns
    // Extract single movie data (not episodes)
}
```

### 4. Required Headers
```php
$headers = [
    'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
    'Referer' => 'https://www.24-hd.com/',
    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'X-Requested-With' => 'XMLHttpRequest'
];
```

### 5. AJAX Request Pattern
```php
$post_data = [
    'action' => 'halim_ajax_player',
    'nonce' => '',
    'episode' => '1',
    'postid' => $movie_id,
    'lang' => $language, // 'Thai' or 'Sound Track'
    'server' => '1'
];
```

## Next Steps
1. Implement HD24MovieScraper class
2. Add browser automation support
3. Integrate with existing MovieScraper system
4. Test with multiple movies
5. Add error handling for Cloudflare blocks

## Files Generated
- `puppeteer_full_data.json` - Complete research data
- `puppeteer_page.html` - Full page HTML
- `page_screenshot.png` - Visual reference
- `puppeteer_metadata.json` - Extracted metadata
- `puppeteer_languages.json` - Language options
- `puppeteer_iframes.json` - Player iframe data
