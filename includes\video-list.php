<?php

global $wpdb;

function vu_delete_video() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error(['status' => 'error', 'message' => 'Insufficient permissions']);
        exit;
    }
    
    if (!isset($_POST['video_md5']) || !isset($_POST['post_id'])) {
        wp_send_json_error(['status' => 'error', 'message' => 'Missing parameters']);
        exit;
    }
    
    $video_md5 = sanitize_text_field($_POST['video_md5']);
    $api_key = get_option('vu_api_key');
    $server_ip = sanitize_text_field($_POST['server_ip']);
    $episode_index = sanitize_text_field($_POST['episode_index']);
    $post_id = intval($_POST['post_id']);
    $post_type = get_post_type($post_id);
    $type = sanitize_text_field($_POST['type']);
    $deletion_successful = false;

    if (!empty($server_ip) && !empty($api_key)) {
        $url = "http://$server_ip/api/deleteVideo.php?id=" . urlencode($video_md5) . "&api=" . urlencode($api_key);
        custom_log("Sending delete request to URL: $url");
        $response = wp_remote_post($url, array('method' => 'POST', 'timeout' => 15));
        if (is_wp_error($response)) {
            custom_log("Failed to delete video on server: $server_ip. Error: " . $response->get_error_message());
        } else {
            $body = wp_remote_retrieve_body($response);
            custom_log("Response from server $server_ip: $body");
        }
    }
    
    $master_server_ip = get_option('vu_master_ip');
    if (!empty($master_server_ip) && $master_server_ip !== $server_ip && !empty($api_key)) {
        $url_master = "http://$master_server_ip/api/deleteVideo.php?id=" . urlencode($video_md5) . "&api=" . urlencode($api_key);
        custom_log("Sending delete request to Master URL: $url_master");
        $response_master = wp_remote_post($url_master, array('method' => 'POST', 'timeout' => 15));
        if (is_wp_error($response_master)) {
            custom_log("Failed to delete video on Master server: $master_server_ip. Error: " . $response_master->get_error_message());
        } else {
            $body_master = wp_remote_retrieve_body($response_master);
            custom_log("Response from Master server $master_server_ip: $body_master");
        }
    }
    
    remove_data_from_other_servers_new($video_md5);
    
    $deletion_successful = vu_delete_post_meta($post_id, $episode_index, $post_type, $type);
    
    if ($deletion_successful) {
        wp_send_json_success(['status' => 'success', 'message' => 'Video data removed successfully.']);
    } else {
        wp_send_json_error(['status' => 'error', 'message' => 'Failed to delete video metadata. The video might not exist.']);
    }
}
add_action('wp_ajax_vu_delete_video', 'vu_delete_video');

function vu_delete_post_meta($post_id, $episode_index, $post_type, $type) {
    $deleted_count = 0;
    if ($post_type === 'movie') {
        if ($type === 'dubbed') {
            $keys = ['video_status_dubbed', 'video_url_dubbed', 'm3u8_status_dubbed', 'm3u8_video_url_dubbed', 'm3u8_dubbed', 'server_ip_dubbed'];
            foreach($keys as $key) {
                if(delete_post_meta($post_id, $key)) $deleted_count++;
            }
        } elseif ($type === 'subbed') {
            $keys = ['video_status_subbed', 'video_url_subbed', 'm3u8_status_subbed', 'm3u8_video_url_subbed', 'm3u8_subbed', 'server_ip_subbed'];
            foreach($keys as $key) {
                if(delete_post_meta($post_id, $key)) $deleted_count++;
            }
        }
    } elseif ($post_type === 'serie' || $post_type === 'anime') {
        if ($type === 'dubbed') {
            $keys = [
                'video_url_episode_dubbed_' . $episode_index, 
                'video_status_episode_dubbed_' . $episode_index, 
                'm3u8_video_url_episode_dubbed_' . $episode_index, 
                'm3u8_status_episode_dubbed_' . $episode_index,
                'server_ip_episode_dubbed_' . $episode_index
            ];
            foreach($keys as $key) {
                if(delete_post_meta($post_id, $key)) $deleted_count++;
            }
        } elseif ($type === 'subbed') {
            $keys = [
                'video_url_episode_subbed_' . $episode_index, 
                'video_status_episode_subbed_' . $episode_index, 
                'm3u8_video_url_episode_subbed_' . $episode_index, 
                'm3u8_status_episode_subbed_' . $episode_index,
                'server_ip_episode_subbed_' . $episode_index
            ];
            foreach($keys as $key) {
                if(delete_post_meta($post_id, $key)) $deleted_count++;
            }
        }
    } elseif ($post_type === 'adult') {
        $keys = ['video_status_adult', 'video_url_adult', 'adult_m3u8_status', 'adult_m3u8_video_url', 'adult_m3u8_url', 'server_ip_adult'];
        foreach($keys as $key) {
            if(delete_post_meta($post_id, $key)) $deleted_count++;
        }
    }
    return $deleted_count > 0;
}

function vu_enqueue_video_list_scripts() {
    wp_enqueue_script('jquery');
    
    wp_localize_script('jquery', 'series_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('movie_scraping_nonce')
    ));
    
    $custom_js = '
    jQuery(document).ready(function ($) {
        console.log("Video List JavaScript loaded");
        
        $(".vu-nav-tab").on("click", function(e) {
            e.preventDefault();
            var url = $(this).attr("href");
            window.location.href = url;
        });
        
        $("#select-all-videos").on("change", function() {
            console.log("Select all clicked:", this.checked);
            $(".video-checkbox").prop("checked", this.checked);
            updateBulkDeleteButton();
        });
        
        $(document).on("change", ".video-checkbox", function() {
            console.log("Individual checkbox changed");
            updateBulkDeleteButton();
            var totalCheckboxes = $(".video-checkbox").length;
            var checkedCheckboxes = $(".video-checkbox:checked").length;
            var allChecked = checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0;
            $("#select-all-videos").prop("checked", allChecked);
            console.log("Checked:", checkedCheckboxes, "Total:", totalCheckboxes, "All checked:", allChecked);
        });
        
        function updateBulkDeleteButton() {
            var checkedCount = $(".video-checkbox:checked").length;
            console.log("Updating bulk delete button, checked count:", checkedCount);
            if (checkedCount > 0) {
                $("#bulk-delete-button").show().text("🗑️ ลบรายการที่เลือก (" + checkedCount + ")");
            } else {
                $("#bulk-delete-button").hide();
            }
        }
        
        $(document).on("click", "#bulk-delete-button", function() {
            console.log("Bulk delete button clicked");
            var checkedVideos = [];
            $(".video-checkbox:checked").each(function() {
                var row = $(this).closest("tr");
                checkedVideos.push({
                    video_md5: $(this).data("video-md5"),
                    server_ip: $(this).data("server-ip"),
                    post_id: $(this).data("post-id"),
                    episode_index: $(this).data("episode-index") || "",
                    type: $(this).data("type"),
                    row: row
                });
            });
            
            console.log("Videos to delete:", checkedVideos);
            
            if (checkedVideos.length === 0) {
                Swal.fire("⚠️ คำเตือน!", "กรุณาเลือกรายการที่ต้องการลบ", "warning");
                return;
            }
            
            Swal.fire({
                title: "⚠️ ยืนยันการลบจำนวนมาก",
                html: "คุณต้องการลบวิดีโอ <strong>" + checkedVideos.length + " รายการ</strong> หรือไม่?<br><small>การกระทำนี้ไม่สามารถยกเลิกได้</small>",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "🗑️ ลบทั้งหมด",
                cancelButtonText: "ยกเลิก",
                allowOutsideClick: false
            }).then((result) => {
                if (result.isConfirmed) {
                    bulkDeleteVideos(checkedVideos);
                }
            });
        });
        
        function bulkDeleteVideos(videos) {
            var totalVideos = videos.length;
            var deletedCount = 0;
            var failedCount = 0;
            
            Swal.fire({
                title: "🔄 กำลังลบวิดีโอ...",
                html: "กำลังลบ: <b>0</b> / " + totalVideos,
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            function deleteNext() {
                if (deletedCount + failedCount >= totalVideos) {
                    showBulkDeleteResults(deletedCount, failedCount);
                    return;
                }
                
                var video = videos[deletedCount + failedCount];
                
                Swal.update({
                    html: "กำลังลบ: <b>" + (deletedCount + failedCount + 1) + "</b> / " + totalVideos + "<br><small>Video ID: " + video.video_md5.substring(0, 8) + "...</small>"
                });
                
                $.ajax({
                    url: "' . admin_url('admin-ajax.php') . '",
                    type: "POST",
                    data: {
                        action: "vu_delete_video",
                        video_md5: video.video_md5,
                        server_ip: video.server_ip,
                        post_id: video.post_id,
                        episode_index: video.episode_index,
                        type: video.type
                    },
                    success: function (response) {
                        try {
                            var result = typeof response === "string" ? JSON.parse(response) : response;
                            if (result.success && result.data && result.data.status === "success") {
                                video.row.fadeOut(500, function() { $(this).remove(); });
                                deletedCount++;
                            } else if (result.status === "success") {
                                video.row.fadeOut(500, function() { $(this).remove(); });
                                deletedCount++;
                            } else {
                                console.error("Delete failed:", result);
                                failedCount++;
                            }
                        } catch (e) {
                            console.error("Response parse error:", e, response);
                            failedCount++;
                        }
                        setTimeout(deleteNext, 300);
                    },
                    error: function (xhr, status, error) {
                        console.error("AJAX Error:", status, error, xhr.responseText);
                        failedCount++;
                        setTimeout(deleteNext, 300);
                    }
                });
            }
            
            deleteNext();
        }
        
        function showBulkDeleteResults(deleted, failed) {
            var message = "ลบสำเร็จ: <strong>" + deleted + "</strong> รายการ";
            if (failed > 0) {
                message += "<br>ลบไม่สำเร็จ: <strong>" + failed + "</strong> รายการ";
            }
            
            Swal.fire({
                title: deleted === 0 ? "❌ ลบไม่สำเร็จ" : (failed === 0 ? "✅ ลบสำเร็จทั้งหมด!" : "⚠️ ลบเสร็จสิ้น"),
                html: message,
                icon: deleted === 0 ? "error" : (failed === 0 ? "success" : "warning"),
                confirmButtonText: "ตกลง"
            }).then(() => {
                $("#select-all-videos").prop("checked", false);
                $(".video-checkbox").prop("checked", false);
                updateBulkDeleteButton();
                location.reload();
            });
        }
        
        $(document).on("click", ".vu-delete-btn", function (e) {
            e.preventDefault();
            console.log("Individual delete button clicked");
            
            var video_md5 = $(this).data("video-md5");
            var server_ip = $(this).data("server-ip");
            var post_id = $(this).data("post-id");
            var episode_index = $(this).data("episode-index") || "";
            var type = $(this).data("type");
            var row = $(this).closest("tr");
            
            console.log("Delete data:", {video_md5, server_ip, post_id, episode_index, type});
            
            Swal.fire({
                title: "⚠️ ยืนยันการลบ",
                text: "คุณต้องการลบวิดีโอนี้หรือไม่? การกระทำนี้ไม่สามารถยกเลิกได้",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "🗑️ ลบ",
                cancelButtonText: "ยกเลิก"
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: "🔄 กำลังลบ...",
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                    
                    $.ajax({
                        url: "' . admin_url('admin-ajax.php') . '",
                        type: "POST",
                        data: {
                            action: "vu_delete_video",
                            video_md5: video_md5,
                            server_ip: server_ip,
                            post_id: post_id,
                            episode_index: episode_index,
                            type: type
                        },
                        success: function (response) {
                            console.log("Delete response:", response);
                            try {
                                var result = typeof response === "string" ? JSON.parse(response) : response;
                                if (result.success && result.data && result.data.status === "success") {
                                    Swal.fire("✅ สำเร็จ!", "ลบวิดีโอเรียบร้อยแล้ว", "success");
                                    row.fadeOut(500, function() { $(this).remove(); });
                                } else if (result.status === "success") {
                                    Swal.fire("✅ สำเร็จ!", "ลบวิดีโอเรียบร้อยแล้ว", "success");
                                    row.fadeOut(500, function() { $(this).remove(); });
                                } else {
                                    Swal.fire("❌ ผิดพลาด!", result.message || result.data?.message || "ไม่สามารถลบวิดีโอได้", "error");
                                }
                            } catch (e) {
                                console.error("JSON Parse Error:", e, "Response:", response);
                                Swal.fire("❌ ผิดพลาด!", "การตอบสนองจากเซิร์ฟเวอร์ไม่ถูกต้อง", "error");
                            }
                        },
                        error: function (xhr, status, error) {
                            console.error("AJAX Error:", status, error, xhr.responseText);
                            Swal.fire("❌ ผิดพลาด!", "เกิดข้อผิดพลาดในการลบวิดีโอ: " + error, "error");
                        }
                    });
                }
            });
        });
        
        updateBulkDeleteButton();
    });
    ';
    wp_add_inline_script('jquery', $custom_js);
}

function vu_video_list_admin_enqueue($hook) {
    if (isset($_GET['page']) && $_GET['page'] === 'video-list') {
        vu_enqueue_video_list_scripts();
    }
}
add_action('admin_enqueue_scripts', 'vu_video_list_admin_enqueue');

function vu_render_video_list_page() {
    $hls_mode = get_option('vu_hls_mode', true);
    $scraping_mode = get_option('vu_scraping_mode', false);
    $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'hls';
    
    if (!$hls_mode && !$scraping_mode) {
        echo '<div class="wrap"><h1>Video List</h1><div class="notice notice-warning"><p>กรุณาเปิดใช้งาน HLS Mode หรือ Scraping Mode ในหน้าการตั้งค่าก่อน</p></div></div>';
        return;
    }
    
    if (!$hls_mode && $active_tab === 'hls') {
        $active_tab = 'scraping';
    }
    if (!$scraping_mode && $active_tab === 'scraping') {
        $active_tab = 'hls';
    }
    
    $posts_per_page = isset($_GET['posts_per_page']) ? intval($_GET['posts_per_page']) : 50;
    $paged = isset($_GET['paged']) ? intval($_GET['paged']) : 1;
    $status_filter = isset($_GET['status_filter']) && $_GET['status_filter'] !== 'all' ? sanitize_text_field($_GET['status_filter']) : '';
    $server_ip_filter = isset($_GET['server_ip_filter']) && $_GET['server_ip_filter'] !== 'all' ? sanitize_text_field($_GET['server_ip_filter']) : '';
    $search_query = isset($_GET['search_query']) ? sanitize_text_field($_GET['search_query']) : '';
    $sort_order = isset($_GET['sort_order']) ? sanitize_text_field($_GET['sort_order']) : 'newest';

    echo '<div class="wrap">';
    echo '<h1>Video List</h1>';
    
    echo '<style>
    .vu-nav-tab-wrapper {
        border-bottom: 1px solid #ccd0d4;
        margin: 0 0 20px;
        line-height: inherit;
        overflow: hidden;
    }
    .vu-nav-tab {
        float: left;
        border: 1px solid #ccd0d4;
        border-bottom: none;
        margin-left: 0.5em;
        padding: 8px 16px;
        font-size: 14px;
        line-height: 20px;
        color: #646970;
        background: #dcdcde;
        text-decoration: none;
        white-space: nowrap;
        border-radius: 4px 4px 0 0;
        transition: all 0.3s ease;
    }
    .vu-nav-tab:hover {
        background-color: #fff;
        color: #000;
        text-decoration: none;
    }
    .vu-nav-tab-active,
    .vu-nav-tab-active:hover {
        border-bottom: 1px solid #f0f0f1;
        background: #f0f0f1;
        color: #000;
        font-weight: 600;
    }
    .vu-tab-content {
        background: #fff;
        border: 1px solid #c3c4c7;
        padding: 20px;
        border-radius: 0 4px 4px 4px;
    }
    .vu-nav-tab-wrapper::after {
        content: "";
        display: table;
        clear: both;
    }
    .notice-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-left: 4px solid #f39c12;
        padding: 12px;
        margin: 5px 0 15px;
        border-radius: 4px;
    }
    .notice-warning p {
        margin: 0;
        color: #856404;
    }
    .video-checkbox {
        cursor: pointer;
        transform: scale(1.2);
    }
    #select-all-videos {
        cursor: pointer;
        transform: scale(1.2);
    }
    #bulk-delete-button {
        font-weight: bold;
        border-radius: 4px;
        transition: all 0.3s ease;
    }
    #bulk-delete-button:hover {
        background-color: #c82333 !important;
        border-color: #bd2130 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    </style>';
    
    if ($hls_mode || $scraping_mode) {
        echo '<div class="vu-nav-tab-wrapper">';
        if ($hls_mode) {
            $hls_class = ($active_tab === 'hls') ? 'vu-nav-tab vu-nav-tab-active' : 'vu-nav-tab';
            echo '<a href="' . add_query_arg('tab', 'hls', remove_query_arg('paged')) . '" class="' . $hls_class . '">HLS Videos</a>';
        }
        if ($scraping_mode) {
            $scraping_class = ($active_tab === 'scraping') ? 'vu-nav-tab vu-nav-tab-active' : 'vu-nav-tab';
            echo '<a href="' . add_query_arg('tab', 'scraping', remove_query_arg('paged')) . '" class="' . $scraping_class . '">Scraping Videos</a>';
        }
        echo '</div>';
    }
    
    echo '<div class="vu-tab-content">';
    
    $counts = vu_get_total_video_count($status_filter, $server_ip_filter, $search_query, $active_tab);
    $total_pages = ceil($counts['total'] / $posts_per_page);
    
    $all_videos = vu_get_filtered_videos($status_filter, $server_ip_filter, $search_query, $sort_order, $active_tab, $posts_per_page, $paged);
    $paged_videos = $all_videos['videos'];
    
    if (count($paged_videos) !== $posts_per_page && $paged < $total_pages) {
        echo '<div style="background: #fff3cd; border: 1px solid #ffeaa7; border-left: 4px solid #f39c12; padding: 12px; margin: 10px 0; border-radius: 4px;">';
        echo '<strong>⚠️ หมายเหตุ:</strong> ';
        echo 'หน้านี้แสดง ' . count($paged_videos) . ' รายการ แทนที่จะเป็น ' . $posts_per_page . ' รายการตามที่ตั้งค่าไว้ ';
        echo 'เนื่องจากการกรองข้อมูลหรือสถานะของวิดีโอ';
        echo '</div>';
    }
    
    if (count($paged_videos) === $posts_per_page) {
        echo '<div style="background: #d1edff; border: 1px solid #0073aa; border-left: 4px solid #0073aa; padding: 12px; margin: 10px 0; border-radius: 4px;">';
        echo '<strong>✅ การแสดงผลสมบูรณ์:</strong> ';
        echo 'หน้านี้แสดงครบ ' . $posts_per_page . ' รายการตามที่ตั้งค่าไว้ 🎯';
        echo '<br><small style="color: #0073aa;">การเรียงลำดับทำงานถูกต้อง โพสต์ที่อัพเดทใหม่จะขึ้นด้านบนทันที</small>';
        echo '</div>';
    }

    $posts_count = count(array_unique(array_column($paged_videos, 'post_id')));
    $video_types_count = [
        'movie' => 0, 'serie' => 0, 'anime' => 0, 'adult' => 0
    ];
    $processed_posts = [];
    foreach ($paged_videos as $video) {
        $post_id = $video['post_id'];
        if (!isset($processed_posts[$post_id])) {
            $post_type = get_post_type($post_id);
            if (isset($video_types_count[$post_type])) {
                $video_types_count[$post_type]++;
            }
            $processed_posts[$post_id] = true;
        }
    }


    echo '<div style="background: #f0f8ff; border: 1px solid #0073aa; padding: 10px; margin: 10px 0; border-radius: 4px;">';
    echo '<strong>📊 สถิติการโหลดข้อมูล:</strong> ';
    echo 'แสดง <span style="color: #0073aa; font-weight: bold;">' . number_format(count($paged_videos)) . '</span> รายการ ';
    echo 'จากทั้งหมด <span style="color: #d63384; font-weight: bold;">' . number_format($counts['total']) . '</span> วิดีโอ ';
    echo '(หน้าที่ ' . $paged . ' จาก ' . $total_pages . ' หน้า) ';
    echo '<span style="color: #6c757d; font-size: 12px;">โหมด: ' . ucfirst($active_tab) . '</span>';
    
    $sort_labels = [
        'newest' => 'วันที่อัพเดทใหม่ล่าสุด',
        'oldest' => 'วันที่อัพเดทเก่าสุด',
        'id_desc' => 'ID ใหม่ล่าสุด',
        'id_asc' => 'ID เก่าสุด'
    ];
    $current_sort_label = $sort_labels[$sort_order] ?? 'วันที่อัพเดทใหม่ล่าสุด';
    echo '<br><small style="color: #28a745;">🔄 เรียงลำดับ: ' . $current_sort_label . '</small>';
    
    if (!empty($paged_videos)) {
        $first_video = reset($paged_videos);
        $last_video = end($paged_videos);
        $first_post_id = $first_video['post_id'];
        $last_post_id = $last_video['post_id'];
        
        if (in_array($sort_order, ['newest', 'oldest'])) {
            $first_modified = get_post_modified_time('Y-m-d H:i:s', false, $first_post_id);
            $last_modified = get_post_modified_time('Y-m-d H:i:s', false, $last_post_id);
            echo '<br><small style="color: #17a2b8;">📅 ช่วงเวลาอัพเดท: ' . $first_modified . ' ถึง ' . $last_modified . '</small>';
        } else {
            echo '<br><small style="color: #17a2b8;">🆔 ช่วง ID: ' . $first_post_id . ' ถึง ' . $last_post_id . '</small>';
        }
    }
    
    echo '<br><small style="color: #6c757d;">📈 รายละเอียด: ';
    $type_labels = ['movie' => 'หนัง', 'serie' => 'ซีรีย์', 'anime' => 'อนิเมะ', 'adult' => '18+'];
    $type_details = [];

    $post_type_counts = array_count_values(array_map(function($video) {
        return get_post_type($video['post_id']);
    }, $paged_videos));

    foreach ($type_labels as $type => $label) {
        if (isset($post_type_counts[$type]) && $post_type_counts[$type] > 0) {
            $type_details[] = $label . ': ' . number_format($post_type_counts[$type]);
        }
    }
    echo implode(' | ', $type_details);
    echo '</small></div>';
    
    $serie_anime_count = ($post_type_counts['serie'] ?? 0) + ($post_type_counts['anime'] ?? 0);
    if ($serie_anime_count > 0) {
        echo '<div style="margin: 20px 0; padding: 15px; background: #e7f3ff; border-left: 4px solid #2196f3;">';
        echo '<h3>🎬 Enhanced Series Scraper</h3>';
        echo '<p style="margin: 0 0 10px;">ทดสอบการตรวจจับตอนของซีรี่ย์จาก 22-hdd.com</p>';
        echo '<div style="display: flex; gap: 15px; align-items: center; margin-top: 10px;">';
        echo '<input type="url" id="series-test-url" placeholder="https://22-hdd.com/series-name/" style="min-width: 300px; padding: 8px;">';
        echo '<button type="button" id="test-series-bulk" class="button button-secondary">🔍 ทดสอบการตรวจจับ</button>';
        echo '</div>';
        echo '<div id="series-bulk-result" style="margin-top: 15px; display: none;"></div>';
        echo '</div>';
        
        echo '<script>
        jQuery(document).ready(function($) {
            $("#test-series-bulk").on("click", function() {
                var button = $(this);
                var originalText = button.text();
                var seriesUrl = $("#series-test-url").val();
                
                if (!seriesUrl) {
                    alert("กรุณาใส่ URL ซีรี่ย์");
                    return;
                }
                
                button.text("🔄 กำลังทดสอบ...").prop("disabled", true);
                $("#series-bulk-result").hide();
                
                $.ajax({
                    url: series_ajax.ajax_url,
                    type: "POST",
                    data: {
                        action: "test_series_detection",
                        series_url: seriesUrl,
                        nonce: series_ajax.nonce
                    },
                    success: function(response) {
                        button.text(originalText).prop("disabled", false);
                        
                        if (response.success) {
                            displayBulkTestResults(response.data);
                        } else {
                            $("#series-bulk-result").html("<p style=\"color: red;\">❌ " + response.data.message + "</p>").show();
                        }
                    },
                    error: function() {
                        button.text(originalText).prop("disabled", false);
                        $("#series-bulk-result").html("<p style=\"color: red;\">❌ เกิดข้อผิดพลาดในการเชื่อมต่อ</p>").show();
                    }
                });
            });
            
            function displayBulkTestResults(data) {
                var confidenceClass = "confidence-low";
                if (data.confidence_score >= 70) confidenceClass = "confidence-high";
                else if (data.confidence_score >= 50) confidenceClass = "confidence-medium";
                
                var html = "<div style=\"background: white; border: 1px solid #ddd; padding: 15px; border-radius: 4px;\">";
                html += "<h4>📊 ผลการทดสอบ</h4>";
                html += "<table style=\"width: 100%; border-collapse: collapse;\">";
                html += "<tr><th style=\"padding: 8px; border: 1px solid #ddd; background: #f0f0f0;\">รายการ</th><th style=\"padding: 8px; border: 1px solid #ddd; background: #f0f0f0;\">ผลลัพธ์</th></tr>";
                html += "<tr><td style=\"padding: 8px; border: 1px solid #ddd;\">🎬 ชื่อซีรี่ย์</td><td style=\"padding: 8px; border: 1px solid #ddd;\">" + data.title + "</td></tr>";
                html += "<tr><td style=\"padding: 8px; border: 1px solid #ddd;\">📺 ตอนที่พบ</td><td style=\"padding: 8px; border: 1px solid #ddd;\">" + data.episodes_found + " ตอน [" + data.episodes.join(", ") + "]</td></tr>";
                html += "<tr><td style=\"padding: 8px; border: 1px solid #ddd;\">🎭 ซีซันที่พบ</td><td style=\"padding: 8px; border: 1px solid #ddd;\">" + data.seasons_found + " ซีซัน [" + data.seasons.join(", ") + "]</td></tr>";
                html += "<tr><td style=\"padding: 8px; border: 1px solid #ddd;\">🎮 Player ที่พบ</td><td style=\"padding: 8px; border: 1px solid #ddd;\">" + data.players_found + " players</td></tr>";
                html += "<tr><td style=\"padding: 8px; border: 1px solid #ddd;\">📈 ความมั่นใจ</td><td style=\"padding: 8px; border: 1px solid #ddd;\"><span class=\"" + confidenceClass + "\">" + data.confidence_score + "%</span></td></tr>";
                html += "<tr><td style=\"padding: 8px; border: 1px solid #ddd;\">✅ สถานะ</td><td style=\"padding: 8px; border: 1px solid #ddd;\">" + data.completeness.message + "</td></tr>";
                html += "</table>";
                
                if (data.analysis && data.analysis.length > 0) {
                    html += "<h4>🔍 การวิเคราะห์</h4>";
                    html += "<ul>";
                    data.analysis.forEach(function(item) {
                        html += "<li>" + item + "</li>";
                    });
                    html += "</ul>";
                }
                
                html += "<div style=\"margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px;\">";
                html += "<strong>💡 คำแนะนำ:</strong> หากผลลัพธ์ดี สามารถเพิ่มซีรี่ย์ใหม่และใช้ URL นี้ในการ Scrape";
                html += "</div>";
                html += "</div>";
                
                $("#series-bulk-result").html(html).show();
            }
        });
        </script>';
        
        echo '<style>
        .confidence-high { color: #008000; font-weight: bold; }
        .confidence-medium { color: #ff8800; font-weight: bold; }
        .confidence-low { color: #cc0000; font-weight: bold; }
        </style>';
    }

    vu_render_filters($posts_per_page, $status_filter, $server_ip_filter, $search_query, $sort_order, $counts, $active_tab);
    vu_render_table($paged_videos, $active_tab);
    if ($total_pages > 1) {
        vu_render_pagination($total_pages, $paged);
    }
    
    echo '</div>';
    echo '</div>';
}

function vu_get_filtered_videos($status_filter, $server_ip_filter, $search_query, $sort_order, $active_tab, $posts_per_page = 50, $paged = 1) {
    global $wpdb;
    
    $offset = ($paged - 1) * $posts_per_page;
    
    $orderby = 'modified';
    $order = 'DESC';
    
    if ($sort_order === 'newest') {
        $orderby = 'modified';
        $order = 'DESC';
    } elseif ($sort_order === 'oldest') {
        $orderby = 'modified';
        $order = 'ASC';
    } elseif ($sort_order === 'id_desc') {
        $orderby = 'ID';
        $order = 'DESC';
    } elseif ($sort_order === 'id_asc') {
        $orderby = 'ID';
        $order = 'ASC';
    }
    
    $args = array(
        'post_type' => ['movie', 'serie', 'anime', 'adult'],
        'posts_per_page' => $posts_per_page * 5,
        'offset' => $offset,
        'post_status' => 'publish',
        'tax_query' => array(
            'relation' => 'OR',
            array(
                'taxonomy' => 'adult_category',
                'field'    => 'slug',
                'terms'    => 'picpost-ai',
                'operator' => 'NOT IN',
            ),
            array(
                'taxonomy' => 'adult_category',
                'operator' => 'NOT EXISTS',
            ),
        ),
        'orderby' => $orderby,
        'order' => $order,
        'fields' => 'ids',
    );

    if (!empty($search_query)) {
        $args['s'] = $search_query;
    }

    $query = new WP_Query($args);
    $post_ids = $query->posts;
    
    if (empty($post_ids)) {
        return ['videos' => [], 'total_found' => 0];
    }

    $base_meta_keys = [];
    if ($active_tab === 'hls') {
        $base_meta_keys = [
            'serie_start_episode_number', 'serie_dubbed_episodes', 'serie_subbed_episodes',
            'gdrivedubbed', 'gdrivesubbed', 'adult_drive_link',
            'video_status_dubbed', 'video_status_subbed', 'video_status_adult',
            'server_ip_dubbed', 'server_ip_subbed', 'server_ip_adult'
        ];
    } elseif ($active_tab === 'scraping') {
        $base_meta_keys = [
            'scraping_status', 'scraping_message', 'scraping_updated',
            'scraping_dubbed_status', 'scraping_subbed_status',
            'm3u8_dubbed', 'm3u8_subbed', 'adult_m3u8_url',
            'series_episodes', 'serie_start_episode_number',
            'scraping_url', 'series_url',
            'scraper_dubbed_episodes', 'scraper_subbed_episodes', 'scraper_series_info'
        ];
    }

    $all_meta = vu_get_all_meta_for_posts($post_ids, $base_meta_keys, $active_tab);
    $post_titles = vu_get_bulk_post_titles($post_ids);
    $post_types = vu_get_bulk_post_types($post_ids);
    
    $all_videos = [];
    $processed_count = 0;
    
    foreach ($post_ids as $post_id) {
        $post_type = $post_types[$post_id] ?? '';
        $post_title = $post_titles[$post_id] ?? '';
        
        $video_data = [];

        if ($post_type === 'serie' || $post_type === 'anime') {
            $video_data = vu_process_serie_episodes($post_id, $post_title, $post_type, $all_meta[$post_id], $active_tab, $status_filter, $server_ip_filter, 0);
        } else {
            $video_data = vu_process_single_video($post_id, $post_title, $post_type, $all_meta[$post_id], $active_tab, $status_filter, $server_ip_filter, 0);
        }
        
        if (!empty($video_data)) {
            foreach ($video_data as $video) {
                if ($processed_count >= $posts_per_page) {
                    break 2;
                }
                $all_videos[] = $video;
                $processed_count++;
            }
        }
    }
    

    
    $final_videos = $all_videos;
    
    $result = [
        'videos' => $final_videos,
        'total_found' => count($final_videos)
    ];
    
    return $result;
}

function vu_get_all_meta_for_posts($post_ids, $base_meta_keys, $active_tab) {
    global $wpdb;
    
    if (empty($post_ids)) {
        return [];
    }
    
    $post_ids_str = implode(',', array_map('intval', $post_ids));
    
    $all_meta_keys = $base_meta_keys;
    
    if ($active_tab === 'hls') {
        for ($i = 0; $i < 100; $i++) {
            $all_meta_keys[] = "server_ip_episode_dubbed_$i";
            $all_meta_keys[] = "video_status_episode_dubbed_$i";
            $all_meta_keys[] = "server_ip_episode_subbed_$i";
            $all_meta_keys[] = "video_status_episode_subbed_$i";
        }
    } elseif ($active_tab === 'scraping') {
        for ($i = 0; $i < 100; $i++) {
            $all_meta_keys[] = "m3u8_server_ip_episode_dubbed_$i";
            $all_meta_keys[] = "m3u8_status_episode_dubbed_$i";
            $all_meta_keys[] = "m3u8_video_url_episode_dubbed_$i";
            $all_meta_keys[] = "m3u8_server_ip_episode_subbed_$i";
            $all_meta_keys[] = "m3u8_status_episode_subbed_$i";
            $all_meta_keys[] = "m3u8_video_url_episode_subbed_$i";
        }
    }
    
    $meta_keys_str = "'" . implode("','", array_map('esc_sql', $all_meta_keys)) . "'";
    
    $query = "
        SELECT post_id, meta_key, meta_value 
        FROM {$wpdb->postmeta} 
        WHERE post_id IN ($post_ids_str) 
        AND meta_key IN ($meta_keys_str)
    ";
    
    $results = $wpdb->get_results($query);
    $meta_data = [];
    
    foreach ($results as $row) {
        $meta_data[$row->post_id][$row->meta_key] = maybe_unserialize($row->meta_value);
    }
    
    foreach ($post_ids as $post_id) {
        if (!isset($meta_data[$post_id])) {
            $meta_data[$post_id] = [];
        }
    }
    
    return $meta_data;
}

function vu_get_bulk_post_meta($post_ids, $meta_keys) {
    global $wpdb;
    
    if (empty($post_ids) || empty($meta_keys)) {
        return [];
    }
    
    $post_ids_str = implode(',', array_map('intval', $post_ids));
    $meta_keys_str = "'" . implode("','", array_map('esc_sql', $meta_keys)) . "'";
    
    $query = "
        SELECT post_id, meta_key, meta_value 
        FROM {$wpdb->postmeta} 
        WHERE post_id IN ($post_ids_str) 
        AND meta_key IN ($meta_keys_str)
    ";
    
    $results = $wpdb->get_results($query);
    $meta_data = [];
    
    foreach ($results as $row) {
        $meta_data[$row->post_id][$row->meta_key] = maybe_unserialize($row->meta_value);
    }
    
    foreach ($post_ids as $post_id) {
        if (!isset($meta_data[$post_id])) {
            $meta_data[$post_id] = [];
        }
    }
    
    return $meta_data;
}

function vu_get_bulk_post_titles($post_ids) {
    global $wpdb;
    
    if (empty($post_ids)) {
        return [];
    }
    
    $post_ids_str = implode(',', array_map('intval', $post_ids));
    
    $query = "
        SELECT ID, post_title 
        FROM {$wpdb->posts} 
        WHERE ID IN ($post_ids_str)
    ";
    
    $results = $wpdb->get_results($query);
    $titles = [];
    
    foreach ($results as $row) {
        $titles[$row->ID] = $row->post_title;
    }
    
    return $titles;
}

function vu_get_bulk_post_types($post_ids) {
    global $wpdb;
    
    if (empty($post_ids)) {
        return [];
    }
    
    $post_ids_str = implode(',', array_map('intval', $post_ids));
    
    $query = "
        SELECT ID, post_type 
        FROM {$wpdb->posts} 
        WHERE ID IN ($post_ids_str)
    ";
    
    $results = $wpdb->get_results($query);
    $types = [];
    
    foreach ($results as $row) {
        $types[$row->ID] = $row->post_type;
    }
    
    return $types;
}

function vu_process_serie_episodes($post_id, $post_title, $post_type, $post_meta, $active_tab, $status_filter, $server_ip_filter, $views) {
    $video_data = [];
    $start_episode_number = 0;
    if (isset($post_meta['serie_start_episode_number']) && is_numeric($post_meta['serie_start_episode_number'])) {
        $start_episode_number = intval($post_meta['serie_start_episode_number']);
    }
    
    if ($active_tab === 'hls') {
        $dubbed_episodes = $post_meta['serie_dubbed_episodes'] ?? [];
        $subbed_episodes = $post_meta['serie_subbed_episodes'] ?? [];
        
        if (!empty($dubbed_episodes) && is_array($dubbed_episodes)) {
            foreach ($dubbed_episodes as $index => $episode_gdrive) {
                if (empty($episode_gdrive)) continue;
                
                        $current_episode_number = $start_episode_number + ($index + 1);
                $server_ip_key = "server_ip_episode_dubbed_$index";
                $status_key = "video_status_episode_dubbed_$index";
                
                $server_ip = $post_meta[$server_ip_key] ?? '';
                $status = $post_meta[$status_key] ?? '';
                
                if ((!$server_ip_filter || $server_ip_filter == $server_ip) &&
                    (!$status_filter || $status_filter == $status)) {
                    
                    $drive_id = vu_extract_drive_id($episode_gdrive);
                    $video_id = md5($drive_id);
                    
                    $video_data[] = [
                                'post_id' => $post_id,
                        'title' => $post_title . ' - Episode ' . $current_episode_number . ' (พากย์ไทย)',
                                'gdrive' => '<a href="' . esc_url($episode_gdrive) . '" target="_blank">' . esc_html($episode_gdrive) . '</a>',
                        'server_ip' => $server_ip,
                        'video_status' => $status,
                        'video_id' => $video_id,
                        'video_url' => 'https://player.movie2free.tv/embed/' . $video_id . '/',
                                'episode_index' => $index,
                                'type' => 'dubbed',
                                'views' => $views,
                    ];
                }
            }
        }
        
        if (!empty($subbed_episodes) && is_array($subbed_episodes)) {
            foreach ($subbed_episodes as $index => $episode_gdrive) {
                if (empty($episode_gdrive)) continue;
                
                        $current_episode_number = $start_episode_number + ($index + 1);
                $server_ip_key = "server_ip_episode_subbed_$index";
                $status_key = "video_status_episode_subbed_$index";
                
                $server_ip = $post_meta[$server_ip_key] ?? '';
                $status = $post_meta[$status_key] ?? '';
                
                if ((!$server_ip_filter || $server_ip_filter == $server_ip) &&
                    (!$status_filter || $status_filter == $status)) {
                    
                    $drive_id = vu_extract_drive_id($episode_gdrive);
                    $video_id = md5($drive_id);
                    
                    $video_data[] = [
                                'post_id' => $post_id,
                        'title' => $post_title . ' - Episode ' . $current_episode_number . ' (ซับไทย)',
                                'gdrive' => '<a href="' . esc_url($episode_gdrive) . '" target="_blank">' . esc_html($episode_gdrive) . '</a>',
                        'server_ip' => $server_ip,
                        'video_status' => $status,
                        'video_id' => $video_id,
                        'video_url' => 'https://player.movie2free.tv/embed/' . $video_id . '/',
                                'episode_index' => $index,
                                'type' => 'subbed',
                                'views' => $views,
                    ];
                }
            }
        }
    } elseif ($active_tab === 'scraping') {
        $dubbed_episodes = $post_meta['scraper_dubbed_episodes'] ?? [];
        $subbed_episodes = $post_meta['scraper_subbed_episodes'] ?? [];
        $series_info = $post_meta['scraper_series_info'] ?? [];
        $main_status = $post_meta['scraping_status'] ?? '';
        $main_message = $post_meta['scraping_message'] ?? '';
        $series_url = $post_meta['series_url'] ?? '';
        
        if (!empty($dubbed_episodes) && is_array($dubbed_episodes)) {
            foreach ($dubbed_episodes as $episode_key => $episode_data) {
                if (empty($episode_data) || !is_array($episode_data)) continue;
                
                $season = $episode_data['season'] ?? 1;
                $episode = $episode_data['episode'] ?? 1;
                $episode_title = $episode_data['title'] ?? "Episode $episode";
                $episode_status = $episode_data['status'] ?? 'available';
                $episode_quality = $episode_data['quality'] ?? 'Standard';
                
                if (!empty($main_status) && 
                    (!$status_filter || $status_filter == '' || $status_filter == 'all' || $status_filter == $main_status)) {
                    
                    $video_data[] = [
                        'post_id' => $post_id,
                        'title' => $post_title . " - S{$season}E{$episode}: " . $episode_title . ' (พากย์ไทย) [Scraping]',
                        'scraping_url' => $series_url,
                        'scraping_status' => $main_status,
                        'scraping_message' => $main_message,
                        'episode_key' => $episode_key,
                        'episode_data' => $episode_data,
                        'episode_quality' => $episode_quality,
                        'episode_status' => $episode_status,
                        'master_url' => $episode_data['master_url'] ?? '',
                        'type' => 'scraping_dubbed',
                    ];
                }
            }
        }
        
        if (!empty($subbed_episodes) && is_array($subbed_episodes)) {
            foreach ($subbed_episodes as $episode_key => $episode_data) {
                if (empty($episode_data) || !is_array($episode_data)) continue;
                
                $season = $episode_data['season'] ?? 1;
                $episode = $episode_data['episode'] ?? 1;
                $episode_title = $episode_data['title'] ?? "Episode $episode";
                $episode_status = $episode_data['status'] ?? 'available';
                $episode_quality = $episode_data['quality'] ?? 'Standard';
                
                if (!empty($main_status) && 
                    (!$status_filter || $status_filter == '' || $status_filter == 'all' || $status_filter == $main_status)) {
                    
                    $video_data[] = [
                        'post_id' => $post_id,
                        'title' => $post_title . " - S{$season}E{$episode}: " . $episode_title . ' (ซับไทย) [Scraping]',
                        'scraping_url' => $series_url,
                        'scraping_status' => $main_status,
                        'scraping_message' => $main_message,
                        'episode_key' => $episode_key,
                        'episode_data' => $episode_data,
                        'episode_quality' => $episode_quality,
                        'episode_status' => $episode_status,
                        'master_url' => $episode_data['master_url'] ?? '',
                        'type' => 'scraping_subbed',
                    ];
                }
            }
        }
        
        if (empty($dubbed_episodes) && empty($subbed_episodes) && !empty($main_status) && 
            (!$status_filter || $status_filter == '' || $status_filter == 'all' || $status_filter == $main_status)) {
            $video_data[] = [
                'post_id' => $post_id,
                'title' => $post_title . ' [Series Scraping]',
                'scraping_url' => $series_url,
                'scraping_status' => $main_status,
                'scraping_message' => $main_message,
                'episode_key' => null,
                'type' => 'scraping',
            ];
        }
    }
    
    return $video_data;
}

function vu_process_single_video($post_id, $post_title, $post_type, $post_meta, $active_tab, $status_filter, $server_ip_filter, $views) {
    $video_data = [];
    
    if ($post_type === 'movie') {
        if ($active_tab === 'hls') {
            $dubbed_link = $post_meta['gdrivedubbed'] ?? '';
            $subbed_link = $post_meta['gdrivesubbed'] ?? '';
            $dubbed_status = $post_meta['video_status_dubbed'] ?? '';
            $subbed_status = $post_meta['video_status_subbed'] ?? '';
            $dubbed_server = $post_meta['server_ip_dubbed'] ?? '';
            $subbed_server = $post_meta['server_ip_subbed'] ?? '';
            
            if (!empty($dubbed_link) && 
                (!$status_filter || $status_filter == $dubbed_status) &&
                (!$server_ip_filter || $server_ip_filter == $dubbed_server)) {
                
                $drive_id = vu_extract_drive_id($dubbed_link);
                $video_id = md5($drive_id);
                
                $video_data[] = [
                    'post_id' => $post_id,
                    'title' => $post_title . ' (พากย์ไทย)',
                    'gdrive' => '<a href="' . esc_url($dubbed_link) . '" target="_blank">' . esc_html($dubbed_link) . '</a>',
                    'server_ip' => $dubbed_server,
                    'video_status' => $dubbed_status,
                    'video_id' => $video_id,
                    'video_url' => 'https://player.movie2free.tv/embed/' . $video_id . '/',
                    'episode_index' => null,
                    'type' => 'dubbed',
                    'views' => $views,
                ];
            }
            
            if (!empty($subbed_link) && 
                (!$status_filter || $status_filter == $subbed_status) &&
                (!$server_ip_filter || $server_ip_filter == $subbed_server)) {
                
                $drive_id = vu_extract_drive_id($subbed_link);
                $video_id = md5($drive_id);
                
                $video_data[] = [
                    'post_id' => $post_id,
                    'title' => $post_title . ' (ซับไทย)',
                    'gdrive' => '<a href="' . esc_url($subbed_link) . '" target="_blank">' . esc_html($subbed_link) . '</a>',
                    'server_ip' => $subbed_server,
                    'video_status' => $subbed_status,
                    'video_id' => $video_id,
                    'video_url' => 'https://player.movie2free.tv/embed/' . $video_id . '/',
                    'episode_index' => null,
                    'type' => 'subbed',
                    'views' => $views,
                ];
            }
        } elseif ($active_tab === 'scraping') {
            $main_status = $post_meta['scraping_status'] ?? '';
            $main_message = $post_meta['scraping_message'] ?? '';
            $dubbed_status = $post_meta['scraping_dubbed_status'] ?? '';
            $subbed_status = $post_meta['scraping_subbed_status'] ?? '';
            $scraping_url = $post_meta['scraping_url'] ?? '';
            
            if (!empty($main_status) && 
                (!$status_filter || $status_filter == '' || $status_filter == 'all' || $status_filter == $main_status)) {
                
                $video_data[] = [
                    'post_id' => $post_id,
                    'title' => $post_title . ' [Scraping]',
                    'scraping_url' => $scraping_url,
                    'scraping_status' => $main_status,
                    'scraping_message' => $main_message,
                    'dubbed_status' => $dubbed_status,
                    'subbed_status' => $subbed_status,
                    'episode_index' => null,
                    'type' => 'scraping',
                ];
            }
            

        }
    } elseif ($post_type === 'adult') {
        if ($active_tab === 'hls') {
            $adult_link = $post_meta['adult_drive_link'] ?? '';
            $adult_status = $post_meta['video_status_adult'] ?? '';
            $adult_server = $post_meta['server_ip_adult'] ?? '';
            
            if (!empty($adult_link) && 
                (!$status_filter || $status_filter == $adult_status) &&
                (!$server_ip_filter || $server_ip_filter == $adult_server)) {
                
                $drive_id = vu_extract_drive_id($adult_link);
                $video_id = md5($drive_id);
                
            $video_data[] = [
                'post_id' => $post_id,
                    'title' => $post_title,
                    'gdrive' => '<a href="' . esc_url($adult_link) . '" target="_blank">' . esc_html($adult_link) . '</a>',
                    'server_ip' => $adult_server,
                    'video_status' => $adult_status,
                    'video_id' => $video_id,
                    'video_url' => 'https://player.movie2free.tv/embed/' . $video_id . '/',
                    'episode_index' => null,
                    'type' => 'adult',
                    'views' => $views,
                ];
            }
        } elseif ($active_tab === 'scraping') {
            $main_status = $post_meta['scraping_status'] ?? '';
            $main_message = $post_meta['scraping_message'] ?? '';
            $scraping_url = $post_meta['scraping_url'] ?? '';
            
            if (!empty($main_status) && 
                (!$status_filter || $status_filter == '' || $status_filter == 'all' || $status_filter == $main_status)) {
                
            $video_data[] = [
                'post_id' => $post_id,
                    'title' => $post_title . ' [Adult Scraping]',
                    'scraping_url' => $scraping_url,
                    'scraping_status' => $main_status,
                    'scraping_message' => $main_message,
                    'episode_index' => null,
                    'type' => 'scraping',
                ];
            }
        }
    }
    
    return $video_data;
}

function vu_render_table($videos, $active_tab) {
    echo '<style>
    .vu-modern-container {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        margin: 20px 0;
    }
    .vu-bulk-actions {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        border-radius: 12px 12px 0 0;
        margin-bottom: 0;
    }
    .vu-bulk-delete-btn {
        background: #ff4757;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(255,71,87,0.3);
    }
    .vu-bulk-delete-btn:hover {
        background: #ff3742;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(255,71,87,0.4);
    }
    .vu-bulk-info {
        color: rgba(255,255,255,0.9);
        font-size: 14px;
        margin-left: 15px;
    }
    .vu-modern-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        margin: 0;
    }
    .vu-modern-table thead {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    .vu-modern-table th {
        padding: 18px 16px;
        text-align: left;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .vu-modern-table th.center {
        text-align: center;
    }
    .vu-modern-table td {
        padding: 16px;
        border-bottom: 1px solid #f1f3f4;
        vertical-align: middle;
        font-size: 14px;
    }
    .vu-modern-table tr:hover {
        background: linear-gradient(90deg, #f8f9ff 0%, #fff 100%);
        transform: scale(1.001);
        transition: all 0.2s ease;
    }
    .vu-title-link {
        color: #2c3e50;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }
    .vu-title-link:hover {
        color: #3498db;
    }
    .vu-original-link {
        color: #6c757d;
        font-size: 11px;
        text-decoration: none;
        background: #f8f9fa;
        padding: 6px 10px;
        border-radius: 4px;
        display: block;
        max-width: 100%;
        word-break: break-all;
        line-height: 1.3;
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;
    }
    .vu-original-link:hover {
        background: #e9ecef;
        color: #495057;
    }
    .vu-status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
        min-width: 80px;
        text-align: center;
    }
    .vu-status-completed {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .vu-status-processing {
        background: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    .vu-status-waiting {
        background: #cce7ff;
        color: #004085;
        border: 1px solid #74c0fc;
    }
    .vu-status-failed {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .vu-status-empty {
        background: #f1f3f4;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }
    .vu-delete-btn {
        background: #ff4757;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
    }
    .vu-delete-btn:hover {
        background: #ff3742;
        transform: scale(1.05);
    }
    .vu-checkbox {
        width: 18px;
        height: 18px;
        cursor: pointer;
        accent-color: #667eea;
    }
    .vu-id-badge {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 12px;
        display: inline-block;
        min-width: 40px;
        text-align: center;
    }
    .vu-no-data {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
        font-size: 16px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    </style>';
    
    echo '<div class="vu-modern-container">';
    echo '<div class="vu-bulk-actions">';
    echo '<button id="bulk-delete-button" class="vu-bulk-delete-btn" style="display: none;">🗑️ ลบรายการที่เลือก (0)</button>';
    echo '<span class="vu-bulk-info">💡 เลือกรายการที่ต้องการลบพร้อมกัน</span>';
    echo '</div>';
    
    echo '<table class="vu-modern-table">';
    
    if ($active_tab === 'hls') {
        echo '<thead>
                <tr>
                    <th style="width: 50px;" class="center">
                        <input type="checkbox" id="select-all-videos" class="vu-checkbox" title="เลือกทั้งหมด">
                    </th>
                    <th style="width: 60px;" class="center">ID</th>
                    <th style="width: 30%;">ชื่อเรื่อง</th>
                    <th style="width: 25%;">Google Drive Link</th>
                    <th style="width: 100px;" class="center">Server IP</th>
                    <th style="width: 100px;" class="center">สถานะ</th>
                    <th style="width: 80px;" class="center">Views</th>
                    <th style="width: 80px;" class="center">จัดการ</th>
                </tr>
              </thead>';
    } else {
        echo '<thead>
                <tr>
                    <th style="width: 50px;" class="center">
                        <input type="checkbox" id="select-all-videos" class="vu-checkbox" title="เลือกทั้งหมด">
                    </th>
                    <th style="width: 60px;" class="center">ID</th>
                    <th style="width: 30%;">ชื่อเรื่อง</th>
                    <th style="width: 40%;">Scraping URL</th>
                    <th style="width: 120px;" class="center">สถานะ</th>
                    <th style="width: 80px;" class="center">จัดการ</th>
                </tr>
              </thead>';
    }
    
    echo '<tbody>';

    if (!empty($videos)) {
        foreach ($videos as $video) {
            if ($active_tab === 'hls') {
                vu_render_row(
                    $video['post_id'], 
                    $video['title'], 
                    $video['gdrive'], 
                    $video['server_ip'], 
                    $video['video_status'], 
                    $video['video_url'], 
                    isset($video['episode_index']) ? $video['episode_index'] : null,
                    isset($video['type']) ? $video['type'] : null,
                    isset($video['views']) ? $video['views'] : 0,
                    isset($video['video_id']) ? $video['video_id'] : md5(vu_extract_drive_id($video['gdrive']))
                );
            } else {
                vu_render_scraping_row($video);
            }
        }
    } else {
        $colspan = ($active_tab === 'hls') ? '8' : '6';
        echo '<tr><td colspan="' . $colspan . '" class="vu-no-data">
                <div>
                    <div style="font-size: 48px; margin-bottom: 16px;">📹</div>
                    <div style="font-weight: 600; margin-bottom: 8px;">ไม่พบวิดีโอ</div>
                    <div>ลองเปลี่ยนเงื่อนไขการค้นหาหรือเพิ่มวิดีโอใหม่</div>
                </div>
              </td></tr>';
    }

    echo '</tbody></table>';
    echo '</div>';
}

function vu_render_row($post_id, $title, $drive_link, $server_ip, $status, $video_url, $episode_index = null, $type = null, $views = 0, $video_id = null) {
    $video_id = $video_id ?? md5(vu_extract_drive_id($drive_link));
    
    $status_class = 'vu-status-empty';
    switch(strtolower($status)) {
        case 'completed':
            $status_class = 'vu-status-completed';
            $status = 'เสร็จสิ้น';
            break;
        case 'processing':
            $status_class = 'vu-status-processing';
            $status = 'กำลังประมวลผล';
            break;
        case 'waiting':
            $status_class = 'vu-status-waiting';
            $status = 'รอดำเนินการ';
            break;
        case 'failed':
            $status_class = 'vu-status-failed';
            $status = 'ล้มเหลว';
            break;
        default:
            $status = 'ไม่ระบุ';
    }
    
    $scraping_url = get_post_meta($post_id, 'scraping_url', true);
    $original_link = $scraping_url ?: $drive_link;

    echo '<tr>
            <td style="text-align: center;">
                <input type="checkbox" class="video-checkbox vu-checkbox" 
                    data-video-md5="' . esc_attr($video_id) . '" 
                    data-server-ip="' . esc_attr($server_ip) . '" 
                    data-post-id="' . esc_attr($post_id) . '" 
                    data-episode-index="' . esc_attr($episode_index ?: '') . '" 
                    data-type="' . esc_attr($type) . '"
                    title="เลือกรายการนี้">
            </td>
            <td style="text-align: center;">
                <span class="vu-id-badge">' . esc_html($post_id) . '</span>
            </td>
            <td>
                <a href="' . get_edit_post_link($post_id) . '" class="vu-title-link">' . esc_html($title) . '</a>';
    
    if ($episode_index) {
        echo '<br><small style="color: #6c757d;">ตอนที่ ' . esc_html($episode_index) . ' (' . esc_html($type) . ')</small>';
    }
    
    echo '</td>
            <td>';
    
    if ($original_link) {
        echo '<a href="' . esc_url($original_link) . '" target="_blank" class="vu-original-link" title="' . esc_attr($original_link) . '">' . esc_html(parse_url($original_link, PHP_URL_HOST) ?: $original_link) . '</a>';
    } else {
        echo '<span style="color: #6c757d; font-style: italic;">ไม่มีข้อมูล</span>';
    }
    
    echo '</td>
            <td style="text-align: center;">
                <span class="vu-status-badge ' . $status_class . '">' . esc_html($status) . '</span>
            </td>
            <td style="text-align: center;">';
    
    if (!is_null($episode_index)) {
        echo '<button class="button vu-delete-btn" data-video-md5="' . esc_attr($video_id) . '" data-server-ip="' . esc_attr($server_ip) . '" data-post-id="' . esc_attr($post_id) . '" data-episode-index="' . esc_attr($episode_index) . '" data-type="' . esc_attr($type) . '" title="ลบรายการนี้">🗑️</button>';
    } else {
        echo '<button class="button vu-delete-btn" data-video-md5="' . esc_attr($video_id) . '" data-server-ip="' . esc_attr($server_ip) . '" data-post-id="' . esc_attr($post_id) . '" data-episode-index="" data-type="' . esc_attr($type) . '" title="ลบรายการนี้">🗑️</button>';
    }
    echo '</td></tr>';
}

function vu_render_scraping_row($video) {
    $post_id = $video['post_id'];
    $title = $video['title'];
    $scraping_status = $video['scraping_status'] ?? '';
    $scraping_message = $video['scraping_message'] ?? '';
    $scraping_url = $video['scraping_url'] ?? '';
    $episode_index = $video['episode_index'] ?? null;
    $type = $video['type'] ?? 'scraping';
    
    $status_class = 'vu-status-empty';
    $status_text = 'ไม่ระบุ';
    
    switch(strtolower($scraping_status)) {
        case 'completed':
            $status_class = 'vu-status-completed';
            $status_text = 'เสร็จสิ้น';
            break;
        case 'processing':
            $status_class = 'vu-status-processing';
            $status_text = 'กำลังประมวลผล';
            break;
        case 'failed':
            $status_class = 'vu-status-failed';
            $status_text = 'ล้มเหลว';
            break;
        case 'video_unavailable':
            $status_class = 'vu-status-failed';
            $status_text = 'วิดีโอไม่พร้อมใช้งาน';
            break;
        default:
            if (!empty($scraping_status)) {
                $status_text = $scraping_status;
            }
    }
    
    $video_id = md5($post_id . '_scraping_' . ($episode_index ?? '0'));
    
    echo '<tr>
            <td style="text-align: center;">
                <input type="checkbox" class="video-checkbox vu-checkbox" 
                    data-video-md5="' . esc_attr($video_id) . '" 
                    data-server-ip="" 
                    data-post-id="' . esc_attr($post_id) . '" 
                    data-episode-index="' . esc_attr($episode_index ?: '') . '" 
                    data-type="' . esc_attr($type) . '"
                    title="เลือกรายการนี้">
            </td>
            <td style="text-align: center;">
                <span class="vu-id-badge">' . esc_html($post_id) . '</span>
            </td>
            <td>
                <a href="' . get_edit_post_link($post_id) . '" class="vu-title-link">' . esc_html($title) . '</a>';
    
    if (!empty($scraping_message)) {
        echo '<br><small style="color: #6c757d;" title="' . esc_attr($scraping_message) . '">' . esc_html(wp_trim_words($scraping_message, 8)) . '</small>';
    }
    
    echo '</td>
            <td>';
    
    if (!empty($scraping_url)) {
        echo '<a href="' . esc_url($scraping_url) . '" target="_blank" class="vu-original-link" title="' . esc_attr($scraping_url) . '" style="word-break: break-all; display: block; max-width: 400px; font-size: 11px;">' . esc_html($scraping_url) . '</a>';
    } else {
        echo '<span style="color: #6c757d; font-style: italic;">ไม่มี URL</span>';
    }
    
    echo '</td>
            <td style="text-align: center;">
                <span class="vu-status-badge ' . $status_class . '" title="' . esc_attr($scraping_message) . '">' . esc_html($status_text) . '</span>
            </td>
            <td style="text-align: center;">
                <button class="button vu-delete-btn" 
                    data-video-md5="' . esc_attr($video_id) . '" 
                    data-server-ip="" 
                    data-post-id="' . esc_attr($post_id) . '" 
                    data-episode-index="' . esc_attr($episode_index ?: '') . '" 
                    data-type="' . esc_attr($type) . '" 
                    title="ลบข้อมูล Scraping">🗑️</button>
            </td>
          </tr>';
}

function vu_render_filters($posts_per_page, $status_filter, $server_ip_filter, $search_query, $sort_order, $counts, $active_tab) {
    $servers = get_option('vu_servers', []);
    echo '<form method="get" class="vu-form">
        <input type="hidden" name="page" value="video-list">
        <input type="hidden" name="tab" value="' . esc_attr($active_tab) . '">
            <label for="posts_per_page">📊 แสดงจำนวนรายการต่อหน้า:</label>
            <select name="posts_per_page" id="posts_per_page" onchange="this.form.submit();">
                <option value="5"' . selected($posts_per_page, 5, false) . '>5 รายการ</option>
                <option value="10"' . selected($posts_per_page, 10, false) . '>10 รายการ</option>
                <option value="20"' . selected($posts_per_page, 20, false) . '>20 รายการ</option>
                <option value="50"' . selected($posts_per_page, 50, false) . '>50 รายการ ⭐</option>
                <option value="100"' . selected($posts_per_page, 100, false) . '>100 รายการ</option>
                <option value="200"' . selected($posts_per_page, 200, false) . '>200 รายการ</option>
                <option value="500"' . selected($posts_per_page, 500, false) . '>500 รายการ</option>
                <option value="1000"' . selected($posts_per_page, 1000, false) . '>1000 รายการ</option>
            </select>

            <label for="sort_order">🔄 เรียงลำดับตาม:</label>
            <select name="sort_order" id="sort_order" onchange="this.form.submit();">
                <option value="newest"' . selected($sort_order, 'newest', false) . '>📅 วันที่อัพเดทใหม่ล่าสุด</option>
                <option value="oldest"' . selected($sort_order, 'oldest', false) . '>📅 วันที่อัพเดทเก่าสุด</option>
                <option value="id_desc"' . selected($sort_order, 'id_desc', false) . '>🆔 ID ใหม่ล่าสุด</option>
                <option value="id_asc"' . selected($sort_order, 'id_asc', false) . '>🆔 ID เก่าสุด</option>
            </select>

            <label for="status_filter">Filter by status:</label>
            <select name="status_filter" id="status_filter" onchange="this.form.submit();">
                <option value=""' . selected($status_filter, '', false) . '>All (' . $counts['total'] . ')</option>
                <option value="waiting"' . selected($status_filter, 'waiting', false) . '>Waiting (' . $counts['waiting'] . ')</option>
                <option value="processing"' . selected($status_filter, 'processing', false) . '>Processing (' . $counts['processing'] . ')</option>
                <option value="completed"' . selected($status_filter, 'completed', false) . '>Completed (' . $counts['completed'] . ')</option>
                <option value="failed"' . selected($status_filter, 'failed', false) . '>Failed (' . $counts['failed'] . ')</option>
                <option value="empty"' . selected($status_filter, 'empty', false) . '>Empty Status (' . $counts['empty'] . ')</option>
            </select>

            <label for="server_ip_filter">Filter by Original Source:</label>
            <select name="server_ip_filter" id="server_ip_filter" onchange="this.form.submit();">
                <option value="all">All Sources</option>
                <option value="empty"' . selected($server_ip_filter, 'empty', false) . '>No Source</option>';
                foreach ($servers as $server) {
                    $server_ip = isset($server['ip']) ? $server['ip'] : '';
                    if (!empty($server_ip)) {
                        echo '<option value="' . esc_attr($server_ip) . '"' . selected($server_ip_filter, $server_ip, false) . '>' . esc_html($server_ip) . '</option>';
                    }
                }
    echo '</select>

            <label for="search_query">Search by title or video ID (comma separated):</label> 
            <input type="text" name="search_query" value="' . esc_attr($search_query) . '" placeholder="Enter title or video IDs, separated by commas..." onkeyup="this.form.submit();" /> 

            <input type="hidden" name="page" value="video-list">
            <button type="submit">Filter</button>
          </form>';
}

function vu_render_pagination($total_pages, $paged) {
    echo '<div class="vu-pagination">';
    echo paginate_links(array(
        'base' => add_query_arg('paged', '%#%'),
        'format' => '',
        'current' => $paged,
        'total' => $total_pages,
        'prev_text' => '&laquo; Previous',
        'next_text' => 'Next &raquo;',
    ));
    echo '</div>';
}

function vu_extract_drive_id($url) {
    preg_match('/[-\w]{25,}/', $url, $matches);
    return $matches[0] ?? null;
}

function remove_data_from_other_servers_new($video_md5) {
    $api_key = get_option('vu_api_key');
    $all_servers = get_option('vu_servers', []);
    if (!is_array($all_servers) || empty($api_key)) {
        return;
    }
    foreach ($all_servers as $server) {
        $server_ip = isset($server['ip']) ? $server['ip'] : null;
        if (!$server_ip) {
            continue;
        }
        $url = "http://$server_ip/api/deleteVideo.php?id=$video_md5&api=$api_key";
        $response = wp_remote_post($url, array(
            'method' => 'POST',
            'timeout' => 30,
        ));
        if (is_wp_error($response)) {
            custom_log("Error deleting video ID $video_md5 on server $server_ip: " . $response->get_error_message());
        } else {
            $body = wp_remote_retrieve_body($response);
            $result = json_decode($body, true);
            if ($result['status'] !== 'success') {
                custom_log("Failed to delete video ID $video_md5 on server $server_ip: " . $result['message']);
            } else {
                custom_log("Successfully deleted video ID $video_md5 on server $server_ip.");
            }
        }
    }
}

function vu_get_total_video_count($status_filter, $server_ip_filter, $search_query, $active_tab) {
    $args = array(
        'post_type' => ['movie', 'serie', 'anime', 'adult'],
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'tax_query' => array(
            'relation' => 'OR',
            array(
                'taxonomy' => 'adult_category',
                'field'    => 'slug',
                'terms'    => 'picpost-ai',
                'operator' => 'NOT IN',
            ),
            array(
                'taxonomy' => 'adult_category',
                'operator' => 'NOT EXISTS',
            ),
        ),
        'orderby' => 'modified',
        'order' => 'DESC',
        'fields' => 'ids',
    );

    if (!empty($search_query)) {
        $args['s'] = $search_query;
    }

    $query = new WP_Query($args);
    $post_ids = $query->posts;
    
    if (empty($post_ids)) {
        return ['total' => 0, 'waiting' => 0, 'processing' => 0, 'completed' => 0, 'failed' => 0, 'empty' => 0];
    }

    $base_meta_keys = [];
    if ($active_tab === 'hls') {
        $base_meta_keys = [
            'serie_start_episode_number', 'serie_dubbed_episodes', 'serie_subbed_episodes',
            'gdrivedubbed', 'gdrivesubbed', 'adult_drive_link',
            'video_status_dubbed', 'video_status_subbed', 'video_status_adult',
            'server_ip_dubbed', 'server_ip_subbed', 'server_ip_adult'
        ];
    } elseif ($active_tab === 'scraping') {
        $base_meta_keys = [
            'serie_start_episode_number', 'serie_dubbed_m3u8_episodes', 'serie_subbed_m3u8_episodes',
            'm3u8_dubbed', 'm3u8_subbed', 'adult_m3u8_url',
            'm3u8_status_dubbed', 'm3u8_status_subbed', 'adult_m3u8_status',
            'm3u8_server_ip_dubbed', 'm3u8_server_ip_subbed', 'adult_m3u8_server_ip',
            'scraping_dubbed_status', 'scraping_subtitled_status', 
            'scraping_dubbed_files', 'scraping_subtitled_files'
        ];
    }
    
    $all_meta = vu_get_all_meta_for_posts($post_ids, $base_meta_keys, $active_tab);
    $post_types = vu_get_bulk_post_types($post_ids);
    
    $total_count = 0;
    $counts = ['total' => 0, 'waiting' => 0, 'processing' => 0, 'completed' => 0, 'failed' => 0, 'empty' => 0];
    
    foreach ($post_ids as $post_id) {
        $post_type = $post_types[$post_id] ?? '';
        $post_meta = $all_meta[$post_id] ?? [];
        
        if ($post_type === 'serie' || $post_type === 'anime') {
            $video_count = vu_count_all_serie_episodes($post_id, $post_meta, $active_tab, $counts);
            $total_count += $video_count;
        } else {
            $video_count = vu_count_all_single_video($post_id, $post_meta, $post_type, $active_tab, $counts);
            $total_count += $video_count;
        }
    }
    
    $counts['total'] = $total_count;
    
    return $counts;
}

function vu_count_all_serie_episodes($post_id, $post_meta, $active_tab, &$counts) {
    $video_count = 0;
    
    if ($active_tab === 'hls') {
        $dubbed_episodes = $post_meta['serie_dubbed_episodes'] ?? [];
        $subbed_episodes = $post_meta['serie_subbed_episodes'] ?? [];
        
        if (!empty($dubbed_episodes) && is_array($dubbed_episodes)) {
            foreach ($dubbed_episodes as $index => $episode_gdrive) {
                if (empty($episode_gdrive)) continue;
                
                $status_key = "video_status_episode_dubbed_$index";
                $status = $post_meta[$status_key] ?? 'empty';
                
                $video_count++;
                if (isset($counts[$status])) {
                    $counts[$status]++;
                } else {
                    $counts['empty']++;
                }
            }
        }
        
        if (!empty($subbed_episodes) && is_array($subbed_episodes)) {
            foreach ($subbed_episodes as $index => $episode_gdrive) {
                if (empty($episode_gdrive)) continue;
                
                $status_key = "video_status_episode_subbed_$index";
                $status = $post_meta[$status_key] ?? 'empty';
                
                $video_count++;
                if (isset($counts[$status])) {
                    $counts[$status]++;
                } else {
                    $counts['empty']++;
                }
            }
        }
    } elseif ($active_tab === 'scraping') {
        $dubbed_episodes = $post_meta['scraper_dubbed_episodes'] ?? [];
        $subbed_episodes = $post_meta['scraper_subbed_episodes'] ?? [];
        $main_status = $post_meta['scraping_status'] ?? '';
        
        if (!empty($dubbed_episodes) && is_array($dubbed_episodes)) {
            foreach ($dubbed_episodes as $episode_key => $episode_data) {
                if (empty($episode_data) || !is_array($episode_data)) continue;
                
                if (!empty($main_status)) {
                    $video_count++;
                    if (isset($counts[$main_status])) {
                        $counts[$main_status]++;
                    } else {
                        $counts['completed']++;
                    }
                }
            }
        }
        
        if (!empty($subbed_episodes) && is_array($subbed_episodes)) {
            foreach ($subbed_episodes as $episode_key => $episode_data) {
                if (empty($episode_data) || !is_array($episode_data)) continue;
                
                if (!empty($main_status)) {
                    $video_count++;
                    if (isset($counts[$main_status])) {
                        $counts[$main_status]++;
                    } else {
                        $counts['completed']++;
                    }
                }
            }
        }
        
        if (empty($dubbed_episodes) && empty($subbed_episodes) && !empty($main_status)) {
            $video_count++;
            if (isset($counts[$main_status])) {
                $counts[$main_status]++;
            } else {
                $counts['completed']++;
            }
        }
    }
    
    return $video_count;
}

function vu_count_all_single_video($post_id, $post_meta, $post_type, $active_tab, &$counts) {
    $video_count = 0;
    
    if ($post_type === 'movie') {
        if ($active_tab === 'hls') {
            $dubbed_link = $post_meta['gdrivedubbed'] ?? '';
            $subbed_link = $post_meta['gdrivesubbed'] ?? '';
            $dubbed_status = $post_meta['video_status_dubbed'] ?? 'empty';
            $subbed_status = $post_meta['video_status_subbed'] ?? 'empty';
            
            if (!empty($dubbed_link)) {
                $video_count++;
                if (isset($counts[$dubbed_status])) {
                    $counts[$dubbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
            
            if (!empty($subbed_link)) {
                $video_count++;
                if (isset($counts[$subbed_status])) {
                    $counts[$subbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        } elseif ($active_tab === 'scraping') {
            $dubbed_m3u8 = $post_meta['m3u8_dubbed'] ?? '';
            $subbed_m3u8 = $post_meta['m3u8_subbed'] ?? '';
            $dubbed_status = $post_meta['scraping_dubbed_status'] ?? 'empty';
            $subbed_status = $post_meta['scraping_subtitled_status'] ?? 'empty';
            
            if (!empty($dubbed_m3u8)) {
                $video_count++;
                if (isset($counts[$dubbed_status])) {
                    $counts[$dubbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
            
            if (!empty($subbed_m3u8)) {
                $video_count++;
                if (isset($counts[$subbed_status])) {
                    $counts[$subbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        }
    } elseif ($post_type === 'adult') {
        if ($active_tab === 'hls') {
            $adult_link = $post_meta['adult_drive_link'] ?? '';
            $adult_status = $post_meta['video_status_adult'] ?? 'empty';
            
            if (!empty($adult_link)) {
                $video_count++;
                if (isset($counts[$adult_status])) {
                    $counts[$adult_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        } elseif ($active_tab === 'scraping') {
            $adult_m3u8 = $post_meta['adult_m3u8_url'] ?? '';
            $adult_status = $post_meta['adult_m3u8_status'] ?? 'empty';
            
            if (!empty($adult_m3u8)) {
                $video_count++;
                if (isset($counts[$adult_status])) {
                    $counts[$adult_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        }
    }
    
    return $video_count;
}

function vu_count_serie_episodes($post_id, $post_meta, $active_tab, $status_filter, $server_ip_filter, &$counts) {
    $video_count = 0;
    
    if ($active_tab === 'hls') {
        $dubbed_episodes = $post_meta['serie_dubbed_episodes'] ?? [];
        $subbed_episodes = $post_meta['serie_subbed_episodes'] ?? [];
        
        if (!empty($dubbed_episodes) && is_array($dubbed_episodes)) {
            foreach ($dubbed_episodes as $index => $episode_gdrive) {
                if (empty($episode_gdrive)) continue;
                
                $server_ip_key = "server_ip_episode_dubbed_$index";
                $status_key = "video_status_episode_dubbed_$index";
                
                $server_ip = $post_meta[$server_ip_key] ?? '';
                $status = $post_meta[$status_key] ?? 'empty';
                
                if ((!$server_ip_filter || $server_ip_filter == $server_ip) &&
                    (!$status_filter || $status_filter == $status)) {
                    
                    $video_count++;
                    if (isset($counts[$status])) {
                        $counts[$status]++;
                    } else {
                        $counts['empty']++;
                    }
                }
            }
        }
        
        if (!empty($subbed_episodes) && is_array($subbed_episodes)) {
            foreach ($subbed_episodes as $index => $episode_gdrive) {
                if (empty($episode_gdrive)) continue;
                
                $server_ip_key = "server_ip_episode_subbed_$index";
                $status_key = "video_status_episode_subbed_$index";
                
                $server_ip = $post_meta[$server_ip_key] ?? '';
                $status = $post_meta[$status_key] ?? 'empty';
                
                if ((!$server_ip_filter || $server_ip_filter == $server_ip) &&
                    (!$status_filter || $status_filter == $status)) {
                    
                    $video_count++;
                    if (isset($counts[$status])) {
                        $counts[$status]++;
                    } else {
                        $counts['empty']++;
                    }
                }
            }
        }
    } elseif ($active_tab === 'scraping') {
        $dubbed_episodes = $post_meta['scraper_dubbed_episodes'] ?? [];
        $subbed_episodes = $post_meta['scraper_subbed_episodes'] ?? [];
        $main_status = $post_meta['scraping_status'] ?? '';
        
        if (!empty($dubbed_episodes) && is_array($dubbed_episodes)) {
            foreach ($dubbed_episodes as $episode_key => $episode_data) {
                if (empty($episode_data) || !is_array($episode_data)) continue;
                
                if (!empty($main_status) && 
                    (!$status_filter || $status_filter == $main_status)) {
                    
                    $video_count++;
                    if (isset($counts[$main_status])) {
                        $counts[$main_status]++;
                    } else {
                        $counts['completed']++;
                    }
                }
            }
        }
        
        if (!empty($subbed_episodes) && is_array($subbed_episodes)) {
            foreach ($subbed_episodes as $episode_key => $episode_data) {
                if (empty($episode_data) || !is_array($episode_data)) continue;
                
                if (!empty($main_status) && 
                    (!$status_filter || $status_filter == $main_status)) {
                    
                    $video_count++;
                    if (isset($counts[$main_status])) {
                        $counts[$main_status]++;
                    } else {
                        $counts['completed']++;
                    }
                }
            }
        }
        
        if (empty($dubbed_episodes) && empty($subbed_episodes) && !empty($main_status)) {
            if (!$status_filter || $status_filter == $main_status) {
                $video_count++;
                if (isset($counts[$main_status])) {
                    $counts[$main_status]++;
                } else {
                    $counts['completed']++;
                }
            }
        }
    }
    
    return $video_count;
}

function vu_count_single_video($post_id, $post_meta, $post_type, $active_tab, $status_filter, $server_ip_filter, &$counts) {
    $video_count = 0;
    
    if ($post_type === 'movie') {
        if ($active_tab === 'hls') {
            $dubbed_link = $post_meta['gdrivedubbed'] ?? '';
            $subbed_link = $post_meta['gdrivesubbed'] ?? '';
            $dubbed_status = $post_meta['video_status_dubbed'] ?? 'empty';
            $subbed_status = $post_meta['video_status_subbed'] ?? 'empty';
            $dubbed_server = $post_meta['server_ip_dubbed'] ?? '';
            $subbed_server = $post_meta['server_ip_subbed'] ?? '';
            
            if (!empty($dubbed_link) && 
                (!$status_filter || $status_filter == $dubbed_status) &&
                (!$server_ip_filter || $server_ip_filter == $dubbed_server)) {
                
                $video_count++;
                if (isset($counts[$dubbed_status])) {
                    $counts[$dubbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
            
            if (!empty($subbed_link) && 
                (!$status_filter || $status_filter == $subbed_status) &&
                (!$server_ip_filter || $server_ip_filter == $subbed_server)) {
                
                $video_count++;
                if (isset($counts[$subbed_status])) {
                    $counts[$subbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        } elseif ($active_tab === 'scraping') {
            $dubbed_m3u8 = $post_meta['m3u8_dubbed'] ?? '';
            $subbed_m3u8 = $post_meta['m3u8_subbed'] ?? '';
            $dubbed_status = $post_meta['scraping_dubbed_status'] ?? 'empty';
            $subbed_status = $post_meta['scraping_subtitled_status'] ?? 'empty';
            $dubbed_server = $post_meta['server_ip_dubbed'] ?? '';
            $subbed_server = $post_meta['server_ip_subbed'] ?? '';
            
            if (!empty($dubbed_m3u8) && 
                (!$status_filter || $status_filter == $dubbed_status) &&
                (!$server_ip_filter || $server_ip_filter == $dubbed_server)) {
                
                $video_count++;
                if (isset($counts[$dubbed_status])) {
                    $counts[$dubbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
            
            if (!empty($subbed_m3u8) && 
                (!$status_filter || $status_filter == $subbed_status) &&
                (!$server_ip_filter || $server_ip_filter == $subbed_server)) {
                
                $video_count++;
                if (isset($counts[$subbed_status])) {
                    $counts[$subbed_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        }
    } elseif ($post_type === 'adult') {
        if ($active_tab === 'hls') {
            $adult_link = $post_meta['adult_drive_link'] ?? '';
            $adult_status = $post_meta['video_status_adult'] ?? 'empty';
            $adult_server = $post_meta['server_ip_adult'] ?? '';
            
            if (!empty($adult_link) && 
                (!$status_filter || $status_filter == $adult_status) &&
                (!$server_ip_filter || $server_ip_filter == $adult_server)) {
                
                $video_count++;
                if (isset($counts[$adult_status])) {
                    $counts[$adult_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        } elseif ($active_tab === 'scraping') {
            $adult_m3u8 = $post_meta['adult_m3u8_url'] ?? '';
            $adult_status = $post_meta['scraping_adult_status'] ?? 'empty';
            $adult_server = $post_meta['server_ip_adult'] ?? '';
            
            if (!empty($adult_m3u8) && 
                (!$status_filter || $status_filter == $adult_status) &&
                (!$server_ip_filter || $server_ip_filter == $adult_server)) {
                
                $video_count++;
                if (isset($counts[$adult_status])) {
                    $counts[$adult_status]++;
                } else {
                    $counts['empty']++;
                }
            }
        }
    }
    
    return $video_count;
}