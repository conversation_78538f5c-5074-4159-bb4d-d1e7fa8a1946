<?php

if (!defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/BaseScraper.php';

class MovieScraper extends BaseScraper {

    public function __construct() {
        parent::__construct();
        $this->upload_dir .= 'movies/';
        $this->base_url .= 'movies/';

        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }

    public function scrape($post_id, $movie_url, $options = []) {
        try {
            $progress = ScrapingProgressManager::getInstance()->initializeForPost($post_id, 'movie');

            $this->log("Starting movie scraping for post ID: $post_id, URL: $movie_url");
            $progress->updateProgress('init', 0);

            $change_type = $progress->checkDomainChange($movie_url);
            $progress->updateProgress('domain_check', 50);

            $should_clear = $this->handle_source_change($post_id, $change_type, $movie_url);
            update_post_meta($post_id, 'scraper_last_change_type', $change_type);

            if ($should_clear) {
                $this->log("Cleared old data due to source change");
            }
            $progress->completeStep('domain_check');

            $website = $this->detect_website($movie_url);
            $this->log("Detected website: $website");

            switch ($website) {
                case '22-hdd':
                    $progress->updateProgress('fetch_html', 0, 'เชื่อมต่อ 22HDD');
                    $scraper = new HD22MovieScraper();
                    return $scraper->scrape($post_id, $movie_url, $options);

                case '24-hd':
                    $progress->updateProgress('fetch_html', 0, 'เชื่อมต่อ 24HD');
                    $scraper = new HD24MovieScraper();
                    return $scraper->scrape($post_id, $movie_url, $options);

                default:
                    $error_msg = "Unsupported website: $website. Please add scraper support for this website.";
                    $this->log("ERROR: $error_msg");
                    $progress->setError($error_msg, 'detect_website');
                    return false;
            }

        } catch (Exception $e) {
            $error_msg = 'Exception occurred: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }

    public function get_scraping_status($post_id) {
        $base_status = [
            'status' => get_post_meta($post_id, 'scraping_status', true),
            'message' => get_post_meta($post_id, 'scraping_message', true),
            'updated' => get_post_meta($post_id, 'scraping_updated', true)
        ];

        foreach (['dubbed', 'subbed'] as $language) {
            $base_status[$language] = [
                'status' => get_post_meta($post_id, "scraping_{$language}_status", true),
                'message' => get_post_meta($post_id, "scraping_{$language}_message", true),
                'files' => get_post_meta($post_id, "scraping_{$language}_files", true),
                'url' => get_post_meta($post_id, "scraping_{$language}_url", true),
                'updated' => get_post_meta($post_id, "scraping_{$language}_updated", true)
            ];
        }

        return $base_status;
    }

    public function clear_scraping_data($post_id) {
        parent::clear_scraping_data($post_id);

        $additional_keys = [
            'dubbed_master_original_url', 'subbed_master_original_url',
            'movie_url_dubbed', 'movie_url_subbed', 'scraping_video_unavailable'
        ];

        foreach (['dubbed', 'subbed'] as $language) {
            $additional_keys[] = "scraping_{$language}_status";
            $additional_keys[] = "scraping_{$language}_message";
            $additional_keys[] = "scraping_{$language}_files";
            $additional_keys[] = "scraping_{$language}_url";
            $additional_keys[] = "scraping_{$language}_updated";
        }

        foreach ($additional_keys as $key) {
            delete_post_meta($post_id, $key);
        }

        $this->log("Movie scraping data cleared for post ID: $post_id");
    }

    protected function process_content($post_id, $config, $options = []) {
        $this->log("MovieScraper is a dispatcher - use specific scrapers (HD22MovieScraper, HD24MovieScraper)");
        return false;
    }
}

class HD22MovieScraper extends BaseScraper {

    public function __construct() {
        parent::__construct();
        $this->upload_dir .= 'movies/';
        $this->base_url .= 'movies/';

        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }

    public function scrape($post_id, $movie_url, $options = []) {
        try {
            $progress = ScrapingProgressManager::getInstance();

            $progress->updateProgress('fetch_html', 0);
            $html = $this->fetch_html($movie_url);
            if (!$html) {
                $error_msg = 'Could not fetch HTML from URL: ' . $movie_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_html');
                return false;
            }
            $progress->completeStep('fetch_html');

            $this->log("Successfully fetched HTML, extracting movie information...");
            $progress->updateProgress('extract_metadata', 0);
            $this->extract_metadata($post_id, $html);
            $progress->completeStep('extract_metadata');

            $progress->updateProgress('find_player', 0);
            $main_player_url = $this->extract_main_player($html);
            if (!$main_player_url) {
                $error_msg = 'No main player iframe found in HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'find_player');
                return false;
            }
            $progress->completeStep('find_player');

            $this->log("Found main player URL: $main_player_url");

            $progress->updateProgress('fetch_player', 0);
            $player_html = $this->fetch_html($main_player_url);
            if (!$player_html) {
                $error_msg = 'Could not fetch player HTML from URL: ' . $main_player_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_player');
                return false;
            }
            $progress->completeStep('fetch_player');

            $this->log("Successfully fetched player HTML, extracting movie config...");
            $progress->updateProgress('extract_config', 0);

            $movie_config = $this->extract_config($player_html);
            if (!$movie_config) {
                $error_msg = 'No movieList configuration found in player HTML';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'extract_config');
                return false;
            }
            $progress->completeStep('extract_config');

            return $this->process_content($post_id, $movie_config, $options);

        } catch (Exception $e) {
            $error_msg = 'Exception occurred: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }

    protected function process_content($post_id, $config, $options = []) {
        $progress = ScrapingProgressManager::getInstance();

        $this->log("Processing movie content for post ID: $post_id");
        $progress->updateProgress('process_dubbed', 0);

        $language_players = $this->detect_language_players($config);

        $results = [];
        $video_unavailable = false;

        $progress->updateProgress('process_dubbed', 10);
        $lang_display = 'พากย์ไทย';
        if (empty($language_players['dubbed'])) {
            $this->log("No dubbed players found");
            $progress->updateProgress('process_dubbed', 50, "ไม่พบไฟล์ $lang_display");
            $results['dubbed'] = false;
        } else {
            $progress->updateProgress('process_dubbed', 20, "กำลังดึงไฟล์ $lang_display");
            $result = $this->process_language_video($post_id, 'dubbed', $language_players['dubbed']);

            if ($result === 'HTTP_500_ERROR' || $result === 'HTTP_404_ERROR') {
                $video_unavailable = true;
                $results['dubbed'] = 'unavailable';
                $progress->updateProgress('process_dubbed', 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
            } else {
                $results['dubbed'] = $result;
                $progress->updateProgress('process_dubbed', 95, "ดึงไฟล์ $lang_display สำเร็จ");
            }
        }
        $progress->completeStep('process_dubbed');

        $progress->updateProgress('process_subbed', 10);
        $lang_display = 'ซับไทย';
        if (empty($language_players['subbed'])) {
            $this->log("No subbed players found");
            $progress->updateProgress('process_subbed', 50, "ไม่พบไฟล์ $lang_display");
            $results['subbed'] = false;
        } else {
            $progress->updateProgress('process_subbed', 20, "กำลังดึงไฟล์ $lang_display");
            $result = $this->process_language_video($post_id, 'subbed', $language_players['subbed']);

            if ($result === 'HTTP_500_ERROR' || $result === 'HTTP_404_ERROR') {
                $video_unavailable = true;
                $results['subbed'] = 'unavailable';
                $progress->updateProgress('process_subbed', 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
            } else {
                $results['subbed'] = $result;
                $progress->updateProgress('process_subbed', 95, "ดึงไฟล์ $lang_display สำเร็จ");
            }
        }
        $progress->completeStep('process_subbed');

        $progress->updateProgress('finalize', 0);

        if ($video_unavailable && !array_filter($results, function($r) { return $r !== false && $r !== 'unavailable'; })) {
            $this->log("All video sources are unavailable");
            $progress->setVideoUnavailable('ไฟล์วิดีโอไม่พร้อมใช้งาน');
            update_post_meta($post_id, 'scraping_video_unavailable', true);
            return 'video_unavailable';
        }

        $success_count = count(array_filter($results, function($r) { return $r !== false && $r !== 'unavailable'; }));

        if ($success_count > 0) {
            $this->auto_fill_m3u8_urls($post_id, $results);
            $progress->updateProgress('finalize', 80);
            $progress->setCompleted("ดึงข้อมูลสำเร็จ $success_count ภาษา");
            $this->log("Successfully processed $success_count language(s)");
            return true;
        }

        $progress->setError('ไม่พบไฟล์วิดีโอที่ใช้งานได้', 'finalize');
        return false;
    }

    protected function process_language_video($post_id, $language, $players) {
        $this->log("Processing $language video for post ID: $post_id with " . count($players) . " players");

        $total_players = count($players);
        $failed_players = 0;
        $http_errors = [];

        foreach ($players as $index => $player_data) {
            $player_url = $player_data['url'];
            $player_group = $player_data['group'];

            $this->log("Trying $language player " . ($index + 1) . "/$total_players: $player_url (Group: $player_group)");

            $m3u8_files = $this->extract_m3u8_files($player_url);

            if ($m3u8_files === 'HTTP_500_ERROR') {
                $this->log("HTTP 500 Error from $language player " . ($index + 1));
                $http_errors[] = "Player " . ($index + 1) . ": HTTP 500 Error";
                $failed_players++;
                continue;
            }

            if ($m3u8_files === 'HTTP_404_ERROR') {
                $this->log("HTTP 404 Error from $language player " . ($index + 1));
                $http_errors[] = "Player " . ($index + 1) . ": HTTP 404 Error";
                $failed_players++;
                continue;
            }

            if (empty($m3u8_files)) {
                $this->log("No M3U8 files found for $language player " . ($index + 1));
                $failed_players++;
                continue;
            }

            $this->log("Found " . count($m3u8_files) . " M3U8 files for $language player " . ($index + 1));

            $saved_files = $this->save_m3u8_files($post_id, 'movie', $language, $m3u8_files);

            if ($saved_files) {
                $this->log("Successfully saved $language M3U8 files from player " . ($index + 1));

                update_post_meta($post_id, "scraping_{$language}_status", 'completed');
                update_post_meta($post_id, "scraping_{$language}_message", 'Successfully processed');
                update_post_meta($post_id, "scraping_{$language}_files", $saved_files);
                update_post_meta($post_id, "scraping_{$language}_url", $player_url);
                update_post_meta($post_id, "scraping_{$language}_updated", current_time('mysql'));

                if (isset($m3u8_files['master_original']['url'])) {
                    update_post_meta($post_id, "{$language}_master_original_url", $m3u8_files['master_original']['url']);
                    $this->log("$language original master URL saved: " . $m3u8_files['master_original']['url']);
                }

                return $saved_files;
            } else {
                $this->log("Failed to save $language M3U8 files from player " . ($index + 1));
                $failed_players++;
            }
        }

        $this->log("All $language players failed. Total: $total_players, Failed: $failed_players");

        if (!empty($http_errors)) {
            $error_message = "Video sources unavailable: " . implode(", ", $http_errors);
            $this->log("HTTP errors detected for $language: $error_message");
            update_post_meta($post_id, "scraping_{$language}_status", 'http_error');
            update_post_meta($post_id, "scraping_{$language}_message", $error_message);
            return 'HTTP_500_ERROR';
        } else {
            update_post_meta($post_id, "scraping_{$language}_status", 'failed');
            update_post_meta($post_id, "scraping_{$language}_message", 'No valid M3U8 files found from any player');
        }

        return false;
    }

    protected function auto_fill_m3u8_urls($post_id, $results) {
        $this->log("Auto-filling M3U8 URLs for post ID: $post_id");
        
        $scraper_settings = get_option('movie_scraper_settings', []);
        $auto_fill_m3u8 = $scraper_settings['auto_fill_m3u8'] ?? true;
        
        if (!$auto_fill_m3u8) {
            $this->log("Auto-fill M3U8 is disabled");
            return;
        }
        
        $use_downloaded_files = $scraper_settings['use_downloaded_files'] ?? true;
        
        if ($use_downloaded_files) {
            if (isset($results['dubbed']) && $results['dubbed'] && isset($results['dubbed']['master'])) {
                if (!get_post_meta($post_id, 'm3u8_dubbed', true)) {
                    update_post_meta($post_id, 'm3u8_dubbed', $results['dubbed']['master']);
                    $this->log("Auto-filled M3U8 dubbed URL (downloaded): " . $results['dubbed']['master']);
                }
            }
            
            if (isset($results['subbed']) && $results['subbed'] && isset($results['subbed']['master'])) {
                if (!get_post_meta($post_id, 'm3u8_subbed', true)) {
                    update_post_meta($post_id, 'm3u8_subbed', $results['subbed']['master']);
                    $this->log("Auto-filled M3U8 subbed URL (downloaded): " . $results['subbed']['master']);
                }
            }
        } else {
            $dubbed_original_url = get_post_meta($post_id, 'dubbed_master_original_url', true);
            if ($dubbed_original_url) {
                update_post_meta($post_id, 'movie_url_dubbed', $dubbed_original_url);
                $this->log("Auto-filled dubbed URL (original): " . $dubbed_original_url);
            }
            
            $subbed_original_url = get_post_meta($post_id, 'subbed_master_original_url', true);
            if ($subbed_original_url) {
                update_post_meta($post_id, 'movie_url_subbed', $subbed_original_url);
                $this->log("Auto-filled subbed URL (original): " . $subbed_original_url);
            }
        }
    }
}

class HD24MovieScraper extends BaseScraper {

    public function __construct() {
        parent::__construct();
        $this->upload_dir .= 'movies/';
        $this->base_url .= 'movies/';

        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }

    public function scrape($post_id, $movie_url, $options = []) {
        try {
            $progress = ScrapingProgressManager::getInstance();

            $progress->updateProgress('fetch_html', 0);
            $html = $this->fetch_html($movie_url);
            if (!$html) {
                $error_msg = 'Could not fetch HTML from URL: ' . $movie_url;
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'fetch_html');
                return false;
            }
            $progress->completeStep('fetch_html');

            $this->log("Successfully fetched HTML, extracting movie information...");
            $progress->updateProgress('extract_metadata', 0);
            $this->extract_24hd_metadata($post_id, $html);
            $progress->completeStep('extract_metadata');

            $this->log("Extracting movie data using AJAX API...");
            $progress->updateProgress('extract_config', 0);

            $movie_data = $this->extract_24hd_movie_data($post_id, $html);
            if (!$movie_data) {
                $error_msg = 'Failed to extract 24-HD movie data';
                $this->log("ERROR: $error_msg");
                $progress->setError($error_msg, 'extract_config');
                return false;
            }
            $progress->completeStep('extract_config');

            $this->extract_24hd_duration($post_id, $movie_data);

            return $this->process_24hd_movie_content($post_id, $movie_data, $options);

        } catch (Exception $e) {
            $error_msg = 'Exception occurred: ' . $e->getMessage();
            $this->log("ERROR: $error_msg");
            $progress->setError($error_msg);
            return false;
        }
    }

    protected function extract_24hd_movie_data($post_id, $html) {
        $this->log("Extracting 24-HD movie data for post: $post_id");

        $post_id_match = null;
        if (preg_match('/"post_id"\s*:\s*(\d+)/', $html, $matches)) {
            $post_id_match = $matches[1];
            $this->log("Found post_id: $post_id_match");
        }

        if (!$post_id_match && preg_match('/data-post-id=["\']*([^"\']+)["\']*/', $html, $matches)) {
            $post_id_match = $matches[1];
            $this->log("Found post_id from data attribute: $post_id_match");
        }

        if (!$post_id_match) {
            $this->log("No post_id found in HTML");
            return false;
        }

        $movie_data = [
            'dubbed' => [],
            'subbed' => []
        ];

        $languages = ['Thai', 'Sound Track'];

        foreach ($languages as $language) {
            $this->log("Processing language: $language");

            $episode_data = $this->get_24hd_movie_episode_data($post_id_match, $language);

            if ($episode_data && $episode_data['success']) {
                $language_type = ($language === 'Thai') ? 'dubbed' : 'subbed';

                $movie_data[$language_type] = [
                    'iframe_url' => $episode_data['iframe_url'],
                    'm3u8_url' => $episode_data['m3u8_url'],
                    'original_url' => $episode_data['m3u8_url']
                ];

                $lang_desc = ($language === 'Thai') ? 'Thai dubbed' : 'Thai subbed';
                $this->log("Successfully extracted movie data ($lang_desc)");
            } else {
                $lang_desc = ($language === 'Thai') ? 'Thai dubbed' : 'Thai subbed';
                $this->log("Failed to extract movie data ($lang_desc)");
            }
        }

        return $movie_data;
    }

    protected function get_24hd_movie_episode_data($post_id, $language) {
        $post_data = "action=halim_ajax_player&nonce=&episode=1&postid={$post_id}&lang=" . urlencode($language) . "&server=1";

        $max_retries = 3;
        $ajax_response = null;

        for ($retry = 1; $retry <= $max_retries; $retry++) {
            $ajax_response = $this->make_24hd_ajax_request('https://api.24-hd.com/get.php', $post_data);

            if ($ajax_response && $ajax_response['success']) {
                break;
            }

            if ($retry < $max_retries) {
                $this->log("AJAX request failed for language: $language (attempt $retry/$max_retries), retrying...");
                sleep(1);
            }
        }

        if (!$ajax_response || !$ajax_response['success']) {
            $this->log("AJAX request failed for language: $language after $max_retries attempts");
            return ['success' => false, 'error' => 'AJAX request failed after retries'];
        }

        $iframe_match = preg_match('/<iframe[^>]*src=["\'](https:\/\/main\.24playerhd\.com\/[^"\']*?)["\']/i', $ajax_response['content'], $matches);
        if (!$iframe_match) {
            $this->log("No iframe found in AJAX response for language: $language");
            return ['success' => false, 'error' => 'No iframe found'];
        }

        $iframe_url = $matches[1];

        if (strpos($iframe_url, 'Failed to connect to MySQL') !== false) {
            $this->log("MySQL connection error detected in iframe URL for $language");
            return ['success' => false, 'error' => 'Server database connection error'];
        }

        $this->log("Found iframe URL for $language: $iframe_url");

        $m3u8_url = $this->extract_24hd_m3u8_from_iframe($iframe_url);
        if (!$m3u8_url) {
            $this->log("No M3U8 URL found for language: $language");
            return ['success' => false, 'error' => 'No M3U8 URL found'];
        }

        return [
            'success' => true,
            'iframe_url' => $iframe_url,
            'm3u8_url' => $m3u8_url
        ];
    }

    protected function extract_24hd_m3u8_from_iframe($iframe_url) {
        $this->log("Extracting M3U8 from iframe: $iframe_url");

        $url_parts = parse_url($iframe_url);
        parse_str($url_parts['query'], $params);

        $id = $params['id'] ?? null;
        $backup = $params['backup'] ?? '0';

        if (!$id) {
            $this->log("No ID parameter found in iframe URL");
            return false;
        }

        $base_url = "https:

        if ($backup == '1') {
            $m3u8_url = "$base_url/newplaylist_g/$id/$id.m3u8";
        } else {
            $m3u8_url = "$base_url/newplaylist/$id/$id.m3u8";
        }

        $content = $this->fetch_html($m3u8_url);
        if ($content && strlen($content) > 10 && $content !== '0' && $content !== 'not found') {
            $this->log("✅ Valid M3U8 found: $m3u8_url");
            return $m3u8_url;
        }

        $backup_url = "$base_url/newplaylist_g/$id/$id.m3u8";
        $backup_content = $this->fetch_html($backup_url);
        if ($backup_content && strlen($backup_content) > 10 && $backup_content !== '0' && $backup_content !== 'not found') {
            $this->log("✅ Valid backup M3U8 found: $backup_url");
            return $backup_url;
        }

        $this->log("❌ No valid M3U8 found");
        return false;
    }

    protected function save_24hd_movie_m3u8_files($post_id, $movie_data) {
        $this->log("Saving 24-HD movie M3U8 files for post: $post_id");

        $results = [];

        foreach (['dubbed', 'subbed'] as $language) {
            if (!empty($movie_data[$language]) && isset($movie_data[$language]['m3u8_url'])) {
                $m3u8_url = $movie_data[$language]['m3u8_url'];

                $saved_files = $this->save_movie_m3u8_files($post_id, $language, $m3u8_url);

                if ($saved_files) {
                    $results[$language] = [
                        'files' => $saved_files,
                        'original_url' => $movie_data[$language]['original_url'],
                        'iframe_url' => $movie_data[$language]['iframe_url']
                    ];

                    $this->log("Successfully saved $language M3U8 files");
                } else {
                    $this->log("Failed to save $language M3U8 files");
                }
            }
        }

        return $results;
    }

    protected function save_movie_m3u8_files($post_id, $language, $m3u8_url) {
        $this->log("Saving movie M3U8 files for post $post_id, language: $language");

        $m3u8_content = $this->fetch_html($m3u8_url);
        if (!$m3u8_content) {
            $this->log("Failed to fetch M3U8 content for $language");
            return false;
        }

        $m3u8_files = [
            'master_original' => [
                'url' => $m3u8_url,
                'content' => $m3u8_content
            ]
        ];

        $this->extract_resolution_files($m3u8_content, $m3u8_url, $m3u8_files);

        return $this->save_m3u8_files($post_id, 'movie', $language, $m3u8_files);
    }

    protected function process_content($post_id, $config, $options = []) {
        $progress = ScrapingProgressManager::getInstance();

        $this->log("Processing 24-HD movie content for post ID: $post_id");
        $progress->updateProgress('process_dubbed', 0);

        $html = $this->fetch_html($this->current_url);
        if (!$html) {
            $this->log("Failed to fetch HTML for movie processing");
            return false;
        }

        $movie_data = $this->extract_24hd_movie_data($post_id, $html);
        if (!$movie_data) {
            $this->log("Failed to extract movie data");
            return false;
        }

        $results = [];
        $video_unavailable = false;

        foreach (['dubbed', 'subbed'] as $language) {
            $lang_display = ($language === 'dubbed') ? 'พากย์ไทย' : 'ซับไทย';
            $progress->updateProgress("process_$language", 10, "กำลังดึงไฟล์ $lang_display");

            if (empty($movie_data[$language])) {
                $this->log("No $language data found");
                $progress->updateProgress("process_$language", 50, "ไม่พบไฟล์ $lang_display");
                $results[$language] = false;
            } else {
                $progress->updateProgress("process_$language", 20, "กำลังดึงไฟล์ $lang_display");

                $saved_files = $this->save_movie_m3u8_files($post_id, $language, $movie_data[$language]['m3u8_url']);

                if ($saved_files) {
                    $results[$language] = [
                        'files' => $saved_files,
                        'original_url' => $movie_data[$language]['original_url'],
                        'iframe_url' => $movie_data[$language]['iframe_url']
                    ];

                    update_post_meta($post_id, "scraping_{$language}_status", 'completed');
                    update_post_meta($post_id, "scraping_{$language}_message", 'Successfully processed');
                    update_post_meta($post_id, "scraping_{$language}_files", $saved_files);
                    update_post_meta($post_id, "scraping_{$language}_url", $movie_data[$language]['iframe_url']);
                    update_post_meta($post_id, "scraping_{$language}_updated", current_time('mysql'));

                    if (isset($movie_data[$language]['original_url'])) {
                        update_post_meta($post_id, "{$language}_master_original_url", $movie_data[$language]['original_url']);
                    }

                    $progress->updateProgress("process_$language", 95, "ดึงไฟล์ $lang_display สำเร็จ");
                    $this->log("Successfully processed $language");
                } else {
                    $results[$language] = false;
                    $progress->updateProgress("process_$language", 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
                    $this->log("Failed to process $language");
                }
            }

            $progress->completeStep("process_$language");
        }

        $progress->updateProgress('finalize', 0);

        $success_count = count(array_filter($results, function($r) { return $r !== false; }));

        if ($success_count > 0) {
            $this->auto_fill_m3u8_urls($post_id, $results);
            $progress->updateProgress('finalize', 80);
            $progress->setCompleted("ดึงข้อมูลสำเร็จ $success_count ภาษา");
            $this->log("Successfully processed $success_count language(s)");
            return true;
        }

        $progress->setError('ไม่พบไฟล์วิดีโอที่ใช้งานได้', 'finalize');
        return false;
    }

    protected function extract_24hd_metadata($post_id, $html) {
        $this->log("Extracting 24-HD metadata for post ID: $post_id");

        $title = $this->extract_24hd_title($html);
        if ($title) {
            $current_title = get_the_title($post_id);
            $should_update = $this->should_auto_fill_title($current_title);

            if ($should_update) {
                wp_update_post([
                    'ID' => $post_id,
                    'post_title' => $title
                ]);
                $this->log("Title auto-filled and saved: $title (was: $current_title)");
            } else {
                $this->log("Title found but not auto-filled (current: $current_title)");
            }
        }

        $imdb_rating = $this->extract_24hd_imdb_rating($html);
        if ($imdb_rating) {
            update_post_meta($post_id, 'imdb_rating', $imdb_rating);
            $this->log("IMDb rating saved: $imdb_rating");
        } else {
            $this->log("No IMDb rating found to save");
        }

        $poster_url = $this->extract_24hd_poster($html);
        if ($poster_url) {
            $current_featured_image = get_post_thumbnail_id($post_id);
            if (!$current_featured_image) {
                $this->log("Downloading and setting featured image: $poster_url");

                if (function_exists('download_and_set_featured_image')) {
                    $result = download_and_set_featured_image($post_id, $poster_url);
                    if ($result) {
                        $this->log("Poster downloaded and set as featured image successfully");
                        update_post_meta($post_id, '_featured_image_url', $poster_url);
                    } else {
                        $this->log("Failed to download and set featured image");
                    }
                } else {
                    $this->log("download_and_set_featured_image function not found");
                    update_post_meta($post_id, '_featured_image_url', $poster_url);
                }
            } else {
                $this->log("Featured image already exists, skipping poster download");
            }
        }

        $youtube_id = $this->extract_24hd_youtube_id($html);
        if ($youtube_id) {
            update_post_meta($post_id, 'linkvideo', $youtube_id);
            $this->log("YouTube ID saved: $youtube_id");
        }

        $duration = $this->extract_duration($html);
        if ($duration) {
            update_post_meta($post_id, 'movie_duration', $duration);
            $this->log("Duration saved: $duration minutes");
        }

        $this->log("24-HD metadata extraction completed");
    }

    protected function process_24hd_movie_content($post_id, $movie_data, $options = []) {
        $progress = ScrapingProgressManager::getInstance();

        $this->log("Processing 24-HD movie content for post ID: $post_id");
        $progress->updateProgress('process_dubbed', 0);

        $results = [];
        $video_unavailable = false;

        foreach (['dubbed', 'subbed'] as $language) {
            $lang_display = ($language === 'dubbed') ? 'พากย์ไทย' : 'ซับไทย';
            $progress->updateProgress("process_$language", 10, "กำลังดึงไฟล์ $lang_display");

            if (empty($movie_data[$language])) {
                $this->log("No $language data found");
                $progress->updateProgress("process_$language", 50, "ไม่พบไฟล์ $lang_display");
                $results[$language] = false;
            } else {
                $progress->updateProgress("process_$language", 20, "กำลังดึงไฟล์ $lang_display");

                $saved_files = $this->save_movie_m3u8_files($post_id, $language, $movie_data[$language]['m3u8_url']);

                if ($saved_files) {
                    $results[$language] = [
                        'files' => $saved_files,
                        'original_url' => $movie_data[$language]['original_url'],
                        'iframe_url' => $movie_data[$language]['iframe_url']
                    ];

                    update_post_meta($post_id, "scraping_{$language}_status", 'completed');
                    update_post_meta($post_id, "scraping_{$language}_message", 'Successfully processed');
                    update_post_meta($post_id, "scraping_{$language}_files", $saved_files);
                    update_post_meta($post_id, "scraping_{$language}_url", $movie_data[$language]['iframe_url']);
                    update_post_meta($post_id, "scraping_{$language}_updated", current_time('mysql'));

                    if (isset($movie_data[$language]['original_url'])) {
                        update_post_meta($post_id, "{$language}_master_original_url", $movie_data[$language]['original_url']);
                    }

                    $progress->updateProgress("process_$language", 95, "ดึงไฟล์ $lang_display สำเร็จ");
                    $this->log("Successfully processed $language");
                } else {
                    $results[$language] = false;
                    $progress->updateProgress("process_$language", 90, "ไฟล์ $lang_display ไม่พร้อมใช้งาน");
                    $this->log("Failed to process $language");
                }
            }

            $progress->completeStep("process_$language");
        }

        $progress->updateProgress('finalize', 0);

        $success_count = count(array_filter($results, function($r) { return $r !== false; }));

        if ($success_count > 0) {
            $this->auto_fill_m3u8_urls($post_id, $results);
            $progress->updateProgress('finalize', 80);
            $progress->setCompleted("ดึงข้อมูลสำเร็จ $success_count ภาษา");
            $this->log("Successfully processed $success_count language(s)");
            return true;
        }

        $progress->setError('ไม่พบไฟล์วิดีโอที่ใช้งานได้', 'finalize');
        return false;
    }

    protected function make_24hd_ajax_request($url, $post_data) {
        $this->log("Making 24-HD AJAX request to: $url");
        $this->log("POST data: $post_data");

        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $post_data,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/x-www-form-urlencoded; charset=UTF-8',
                    'X-Requested-With: XMLHttpRequest',
                    'Accept: */*',
                    'Accept-Language: th-TH,th;q=0.9,en;q=0.8',
                    'Referer: https://www.24-hd.com/',
                    'Origin: https://www.24-hd.com'
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                $this->log("cURL error: $error");
                return ['success' => false, 'error' => $error];
            }

            if ($http_code === 200 && $response) {
                $this->log("24-HD AJAX request successful: " . strlen($response) . " characters");
                $this->log("Response preview: " . substr($response, 0, 200));
                return ['success' => true, 'content' => $response];
            } else {
                $this->log("24-HD AJAX request failed: HTTP $http_code");
                return ['success' => false, 'error' => "HTTP $http_code"];
            }
        }

        $this->log("cURL not available, trying wp_remote_post");

        $args = [
            'method' => 'POST',
            'timeout' => 30,
            'headers' => [
                'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8',
                'X-Requested-With' => 'XMLHttpRequest',
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer' => 'https://www.24-hd.com/',
                'Accept' => '*/*',
                'Accept-Language' => 'th-TH,th;q=0.9,en;q=0.8'
            ],
            'body' => $post_data
        ];

        $response = wp_remote_request($url, $args);

        if (is_wp_error($response)) {
            $this->log("wp_remote_post error: " . $response->get_error_message());
            return ['success' => false, 'error' => $response->get_error_message()];
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $content = wp_remote_retrieve_body($response);

        if ($status_code !== 200) {
            $this->log("wp_remote_post failed with status: $status_code");
            return ['success' => false, 'error' => "HTTP $status_code"];
        }

        $this->log("wp_remote_post successful: " . strlen($content) . " characters");
        return ['success' => true, 'content' => $content];
    }

    protected function extract_24hd_title($html) {
        $patterns = [
            '/<meta[^>]*property=["\']*og:title["\']*[^>]*content=["\']*([^"\']*?)["\']*[^>]*>/i',
            '/<div[^>]*class=["\']*[^"\']*movietext[^"\']*["\']*[^>]*>[\s\S]*?<h1[^>]*>([^<]*?)<\/h1>/i',
            '/<title[^>]*>([^<]*?)<\/title>/i'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $title = trim($matches[1]);
                if (!empty($title)) {
                    $title = str_replace(' 24-HD.COM', '', $title);
                    $title = preg_replace('/^ดูหนัง\s*/', '', $title);
                    $title = preg_replace('/\s*เต็มเรื่อง.*$/', '', $title);
                    $this->log("Found title: $title");
                    return $title;
                }
            }
        }

        $this->log("No title found");
        return null;
    }

    protected function extract_24hd_imdb_rating($html) {
        if (preg_match('/(\d+\.?\d*)\s*HD/', $html, $matches)) {
            $rating = trim($matches[1]);
            if (is_numeric($rating) && $rating >= 0 && $rating <= 10 && $rating != 24) {
                $this->log("Found IMDb rating from direct search: $rating");
                return $rating;
            }
        }

        $patterns = [
            '/<span[^>]*class=["\']*[^"\']*score[^"\']*["\']*[^>]*>[\s\S]*?(\d+\.?\d*)\s*HD[\s\S]*?<\/span>/i',
            '/IMDb[^>]*>(\d+\.?\d*)/i',
            '/<div[^>]*class=["\']*[^"\']*rating[^"\']*["\']*[^>]*>.*?(\d+\.?\d*)<\/div>/i',
            '/คะแนน[^>]*?(\d+\.?\d*)/i'
        ];

        $this->log("Searching for IMDb rating in HTML...");

        foreach ($patterns as $index => $pattern) {
            $this->log("Trying pattern " . ($index + 1) . ": $pattern");
            if (preg_match($pattern, $html, $matches)) {
                $rating = trim($matches[1]);
                $this->log("Pattern " . ($index + 1) . " matched: " . print_r($matches, true));
                if (is_numeric($rating) && $rating >= 0 && $rating <= 10 && $rating != 24) {
                    $this->log("Found IMDb rating: $rating");
                    return $rating;
                } else {
                    $this->log("Invalid rating value: $rating (not in range 0-10 or is 24)");
                }
            }
        }

        $this->log("No IMDb rating found - checking for '5.5 HD' in HTML");
        if (strpos($html, '5.5 HD') !== false) {
            $this->log("Found '5.5 HD' text in HTML but pattern didn't match");
        }

        return null;
    }

    protected function extract_24hd_poster($html) {
        $patterns = [
            '/<meta name="twitter:image" content="([^"]*?)"/i',
            '/<meta property="og:image" content="([^"]*?)"/i',
            '/<img[^>]*alt=["\']*ดูหนังออนไลน์[^"\']*["\']*[^>]*src=["\']*([^"\']*?)["\']*[^>]*>/i',
            '/<img[^>]*src=["\']*([^"\']*wp-content\/uploads[^"\']*\.jpg)["\']*[^>]*>/i',
            '/<img[^>]*class=["\']*[^"\']*poster[^"\']*["\']*[^>]*src=["\']*([^"\']*?)["\']*[^>]*>/i',
            '/<img[^>]*class=["\']*[^"\']*thumbnail[^"\']*["\']*[^>]*src=["\']*([^"\']*?)["\']*[^>]*>/i'
        ];

        $this->log("Searching for poster URL in HTML...");

        foreach ($patterns as $index => $pattern) {
            $this->log("Trying poster pattern " . ($index + 1) . ": $pattern");
            if (preg_match($pattern, $html, $matches)) {
                $poster_url = trim($matches[1]);
                $this->log("Poster pattern " . ($index + 1) . " matched: $poster_url");
                if (!empty($poster_url) && strpos($poster_url, 'data:image') !== 0) {
                    if (strpos($poster_url, 'http') !== 0) {
                        $poster_url = 'https://www.24-hd.com' . $poster_url;
                    }
                    if (filter_var($poster_url, FILTER_VALIDATE_URL)) {
                        $this->log("Found poster URL: $poster_url");
                        return $poster_url;
                    }
                } else {
                    $this->log("Empty or invalid poster URL: '$poster_url'");
                }
            }
        }

        $this->log("No poster URL found - checking for specific meta tags");
        if (strpos($html, 'twitter:image') !== false) {
            $this->log("Found 'twitter:image' in HTML");
        }
        if (strpos($html, 'og:image') !== false) {
            $this->log("Found 'og:image' in HTML");
        }

        return null;
    }

    protected function extract_24hd_youtube_id($html) {
        $patterns = [
            '/<div[^>]*id=["\']*thumbnail_container["\']*[^>]*>[\s\S]*?img[^>]*src=["\']*https:\/\/img\.youtube\.com\/vi\/([a-zA-Z0-9_-]{11})\/[^"\']*["\']*[^>]*>/i',
            '/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})/i',
            '/youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/i',
            '/data-youtube-id=["\']*([a-zA-Z0-9_-]{11})["\']*/',
            '/<iframe[^>]*src=["\']*[^"\']*youtube[^"\']*\/embed\/([a-zA-Z0-9_-]{11})[^"\']*["\']*[^>]*>/i',
            '/trailer[^>]*href=["\']*[^"\']*(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})[^"\']*["\']*[^>]*>/i'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $html, $matches)) {
                $youtube_id = trim($matches[1]);
                if (!empty($youtube_id) && strlen($youtube_id) === 11) {
                    $this->log("Found YouTube ID: $youtube_id");
                    return $youtube_id;
                }
            }
        }

        $this->log("No YouTube ID found");
        return null;
    }

    protected function extract_24hd_duration($post_id, $movie_data) {
        $this->log("Extracting movie duration from M3U8 files");

        if (empty($movie_data['dubbed']) && empty($movie_data['subbed'])) {
            $this->log("No movie data available for duration extraction");
            return false;
        }

        $m3u8_url = null;
        if (!empty($movie_data['dubbed']) && isset($movie_data['dubbed']['m3u8_url'])) {
            $m3u8_url = $movie_data['dubbed']['m3u8_url'];
        } elseif (!empty($movie_data['subbed']) && isset($movie_data['subbed']['m3u8_url'])) {
            $m3u8_url = $movie_data['subbed']['m3u8_url'];
        }

        if (!$m3u8_url) {
            $this->log("No M3U8 URL found for duration extraction");
            return false;
        }

        $this->log("Fetching M3U8 content for duration: $m3u8_url");
        $master_content = $this->fetch_html($m3u8_url);
        if (!$master_content) {
            $this->log("Failed to fetch M3U8 content for duration");
            return false;
        }

        $duration = $this->extract_duration_from_m3u8($master_content, $m3u8_url);
        if ($duration > 0) {
            update_post_meta($post_id, 'movie_duration', $duration);
            $this->log("Movie duration extracted and saved: $duration minutes");
            return $duration;
        }

        $this->log("Could not calculate duration from M3U8 content");
        return false;
    }

    protected function extract_duration_from_m3u8($master_content, $base_url) {
        $this->log("Extracting duration from M3U8 content");

        if (strpos($master_content, '#EXT-X-STREAM-INF:') !== false) {
            $this->log("Master M3U8 detected, trying to get resolution file for duration");

            $lines = explode("\n", $master_content);
            $resolution_url = null;

            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line) && strpos($line, '#') !== 0 && strpos($line, '.m3u8') !== false) {
                    if (strpos($line, 'http') === 0) {
                        $resolution_url = $line;
                    } else {
                        $base_dir = dirname($base_url);
                        $resolution_url = $base_dir . '/' . ltrim($line, '/');
                    }
                    break;
                }
            }

            if ($resolution_url) {
                $this->log("Fetching resolution M3U8 for duration: $resolution_url");
                $resolution_content = $this->fetch_html($resolution_url);
                if ($resolution_content) {
                    return $this->calculate_m3u8_duration($resolution_content);
                }
            }

            return $this->estimate_movie_duration();
        }

        if (strpos($master_content, '#EXTINF:') !== false) {
            $this->log("Segment M3U8 detected, calculating duration from segments");
            return $this->calculate_m3u8_duration($master_content);
        }

        $this->log("Cannot determine duration from M3U8 content");
        return $this->estimate_movie_duration();
    }

    protected function calculate_m3u8_duration($m3u8_content) {
        $this->log("Calculating duration from M3U8 segments");

        $total_duration = 0;
        $segment_count = 0;

        if (preg_match_all('/#EXTINF:(\d+\.?\d*)/', $m3u8_content, $matches)) {
            foreach ($matches[1] as $duration) {
                $total_duration += floatval($duration);
                $segment_count++;
            }
        }

        if ($total_duration > 0) {
            $duration_minutes = round($total_duration / 60);
            $this->log("Calculated duration: $segment_count segments, $total_duration seconds = $duration_minutes minutes");
            return $duration_minutes;
        }

        if (preg_match('/#EXT-X-TARGETDURATION:(\d+)/', $m3u8_content, $matches)) {
            $target_duration = intval($matches[1]);
            $ts_count = substr_count($m3u8_content, '.ts');
            if ($ts_count > 0) {
                $estimated_duration = round(($target_duration * $ts_count) / 60);
                $this->log("Estimated duration: $ts_count segments × {$target_duration}s = $estimated_duration minutes");
                return $estimated_duration;
            }
        }

        $this->log("Duration calculation failed");
        return 0;
    }

    protected function estimate_movie_duration() {
        $this->log("Estimating movie duration based on typical lengths");
        $typical_durations = [90, 120, 105, 135, 150, 75];
        $selected_duration = $typical_durations[0];
        $this->log("Selected typical movie duration: $selected_duration minutes");
        return $selected_duration;
    }

    protected function auto_fill_m3u8_urls($post_id, $results) {
        $this->log("Auto-filling M3U8 URLs for post ID: $post_id");

        $scraper_settings = get_option('movie_scraper_settings', []);
        $auto_fill_m3u8 = $scraper_settings['auto_fill_m3u8'] ?? true;

        if (!$auto_fill_m3u8) {
            $this->log("Auto-fill M3U8 is disabled");
            return;
        }

        $use_downloaded_files = $scraper_settings['use_downloaded_files'] ?? true;

        if ($use_downloaded_files) {
            if (isset($results['dubbed']) && $results['dubbed'] && isset($results['dubbed']['master'])) {
                if (!get_post_meta($post_id, 'm3u8_dubbed', true)) {
                    update_post_meta($post_id, 'm3u8_dubbed', $results['dubbed']['master']);
                    $this->log("Auto-filled M3U8 dubbed URL (downloaded): " . $results['dubbed']['master']);
                }
            }

            if (isset($results['subbed']) && $results['subbed'] && isset($results['subbed']['master'])) {
                if (!get_post_meta($post_id, 'm3u8_subbed', true)) {
                    update_post_meta($post_id, 'm3u8_subbed', $results['subbed']['master']);
                    $this->log("Auto-filled M3U8 subbed URL (downloaded): " . $results['subbed']['master']);
                }
            }
        } else {
            $dubbed_original_url = get_post_meta($post_id, 'dubbed_master_original_url', true);
            if ($dubbed_original_url) {
                update_post_meta($post_id, 'movie_url_dubbed', $dubbed_original_url);
                $this->log("Auto-filled dubbed URL (original): " . $dubbed_original_url);
            }

            $subbed_original_url = get_post_meta($post_id, 'subbed_master_original_url', true);
            if ($subbed_original_url) {
                update_post_meta($post_id, 'movie_url_subbed', $subbed_original_url);
                $this->log("Auto-filled subbed URL (original): " . $subbed_original_url);
            }
        }
    }

    protected function extract_resolution_files($master_content, $master_url, &$files) {
        $this->log("Extracting resolution files from master M3U8 (24-HD specific)");

        $lines = explode("\n", $master_content);

        $base_url = dirname($master_url);
        if (substr($base_url, -1) !== '/') {
            $base_url .= '/';
        }

        $resolutions_found = [];
        $current_resolution = null;

        foreach ($lines as $line) {
            $line = trim($line);

            if (strpos($line, '#EXT-X-STREAM-INF:') === 0) {
                if (preg_match('/RESOLUTION=(\d+)x(\d+)/', $line, $matches)) {
                    $current_resolution = $matches[2];
                    $this->log("Found resolution: {$current_resolution}p");
                }
            } elseif (!empty($line) && strpos($line, '#') !== 0 && strpos($line, '.m3u8') !== false && $current_resolution) {
                $resolutions_found[] = $current_resolution;

                if (strpos($line, 'http') === 0) {
                    $resolution_url = $line;
                } elseif (strpos($line, '//') === 0) {
                    $resolution_url = 'https:' . $line;
                } else {
                    $resolution_url = $base_url . ltrim($line, '/');
                }

                $this->log("Attempting to fetch resolution URL: $resolution_url");
                $resolution_content = $this->fetch_html($resolution_url);

                if ($resolution_content && strlen($resolution_content) > 10 && strpos($resolution_content, '#EXTINF:') !== false) {
                    $files['resolutions'][$current_resolution] = [
                        'url' => $resolution_url,
                        'content' => $resolution_content,
                        'filename' => $current_resolution . '.m3u8'
                    ];
                    $this->log("Successfully extracted resolution: {$current_resolution}p");
                } else {
                    $this->log("Failed to fetch or invalid content for resolution: {$current_resolution}p");

                    $files['resolutions'][$current_resolution] = [
                        'url' => $resolution_url,
                        'content' => "
                        'filename' => $current_resolution . '.m3u8'
                    ];
                    $this->log("Created fallback content for resolution: {$current_resolution}p");
                }

                $current_resolution = null;
            }
        }

        if (empty($resolutions_found)) {
            $this->log("No resolution files found in master M3U8");
        } else {
            $this->log("Total resolutions extracted: " . count($resolutions_found));
        }
    }

    protected function should_auto_fill_title($current_title) {
        if (empty($current_title)) {
            return true;
        }

        $draft_patterns = [
            '/^Auto Draft$/i',
            '/^ฉบับร่าง$/i',
            '/^Draft$/i',
            '/^โพสต์ฉบับร่าง$/i',
            '/^Post Draft$/i',
            '/^Untitled$/i',
            '/^ไม่มีชื่อ$/i'
        ];

        foreach ($draft_patterns as $pattern) {
            if (preg_match($pattern, trim($current_title))) {
                $this->log("Title matches draft pattern: $current_title");
                return true;
            }
        }

        $this->log("Title does not match draft patterns: $current_title");
        return false;
    }
}

class MovieScrapingBackgroundProcess extends WP_Background_Process {
    protected $action = 'movie_scraping_process';
    
    protected function task($item) {
        $post_id = $item['post_id'];
        $movie_url = $item['movie_url'];
        $options = $item['options'] ?? [];
        
        movie_scraper_log("Background processing movie scraping for post ID: $post_id, URL: $movie_url");
        
        $scraper = new MovieScraper();
        $result = $scraper->scrape($post_id, $movie_url, $options);
        
        if ($result === 'video_unavailable') {
            movie_scraper_log("Background process: Video unavailable for post ID: $post_id");
            return false;
        }
        
        if ($result) {
            movie_scraper_log("Background process: Successfully scraped movie for post ID: $post_id");
        } else {
            movie_scraper_log("Background process: Failed to scrape movie for post ID: $post_id");
        }
        
        return false;
    }
    
    protected function complete() {
        parent::complete();
        movie_scraper_log("Movie scraping background process completed");
    }
}

function init_movie_scraping_background_process() {
    global $movie_scraping_background_process;
    $movie_scraping_background_process = new MovieScrapingBackgroundProcess();
}
add_action('init', 'init_movie_scraping_background_process');

function queue_movie_scraping($post_id, $movie_url, $options = []) {
    global $movie_scraping_background_process;
    
    if (!$movie_scraping_background_process) {
        init_movie_scraping_background_process();
    }
    
    $movie_scraping_background_process->push_to_queue([
        'post_id' => $post_id,
        'movie_url' => $movie_url,
        'options' => $options
    ]);
    
    $movie_scraping_background_process->save()->dispatch();
}

function start_movie_scraping($post_id, $movie_url, $options = []) {
    $scraper = new MovieScraper();
    return $scraper->scrape($post_id, $movie_url, $options);
}

function test_movie_website_detection($url) {
    $scraper = new MovieScraper();
    $website = $scraper->detect_website($url);

    $supported_websites = [
        '22-hdd' => 'HD22MovieScraper',
        '24-hd' => 'HD24MovieScraper'
    ];

    return [
        'url' => $url,
        'detected_website' => $website,
        'scraper_class' => $supported_websites[$website] ?? 'ไม่รองรับ',
        'is_supported' => isset($supported_websites[$website])
    ];
}

if (!function_exists('movie_scraper_log')) {
    function movie_scraper_log($message) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('[Movie Scraper] ' . $message);
        }
    }
} 