<?php

require_once '../includes/scrapers/BaseScraper.php';
require_once '../includes/scrapers/MovieScraper.php';

class HD24ScraperTest {
    
    public function __construct() {
        $this->log("Starting 24-HD Scraper Test");
    }
    
    public function log($message) {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . "\n";
    }
    
    public function testMetadataExtraction() {
        $this->log("=== Testing Metadata Extraction ===");
        
        $test_url = 'https://www.24-hd.com/stolen/';
        $this->log("Test URL: $test_url");
        
        $html = $this->fetchHtml($test_url);
        if (!$html) {
            $this->log("❌ Failed to fetch HTML");
            return false;
        }
        
        $this->log("✅ HTML fetched successfully: " . strlen($html) . " characters");
        
        $scraper = new HD24MovieScraper();
        
        $title = $this->callPrivateMethod($scraper, 'extract_24hd_title', [$html]);
        $this->log("Title: " . ($title ?: 'Not found'));
        
        $rating = $this->callPrivateMethod($scraper, 'extract_24hd_imdb_rating', [$html]);
        $this->log("IMDb Rating: " . ($rating ?: 'Not found'));
        
        $poster = $this->callPrivateMethod($scraper, 'extract_24hd_poster', [$html]);
        $this->log("Poster URL: " . ($poster ?: 'Not found'));
        
        $youtube = $this->callPrivateMethod($scraper, 'extract_24hd_youtube_id', [$html]);
        $this->log("YouTube ID: " . ($youtube ?: 'Not found'));
        
        return true;
    }
    
    public function testPostIdExtraction() {
        $this->log("=== Testing Post ID Extraction ===");
        
        $test_url = 'https://www.24-hd.com/stolen/';
        $html = $this->fetchHtml($test_url);
        
        if (!$html) {
            $this->log("❌ Failed to fetch HTML");
            return false;
        }
        
        $post_id = null;
        if (preg_match('/"post_id"\s*:\s*(\d+)/', $html, $matches)) {
            $post_id = $matches[1];
            $this->log("✅ Found post_id from JSON: $post_id");
        }
        
        if (!$post_id && preg_match('/data-post-id=["\']*([^"\']+)["\']*/', $html, $matches)) {
            $post_id = $matches[1];
            $this->log("✅ Found post_id from data attribute: $post_id");
        }
        
        if (!$post_id) {
            $this->log("❌ No post_id found");
            return false;
        }
        
        return $post_id;
    }
    
    public function testAjaxRequest() {
        $this->log("=== Testing AJAX Request ===");
        
        $post_id = $this->testPostIdExtraction();
        if (!$post_id) {
            return false;
        }
        
        $scraper = new HD24MovieScraper();
        
        $languages = ['Thai', 'Sound Track'];
        
        foreach ($languages as $language) {
            $this->log("Testing language: $language");
            
            $post_data = "action=halim_ajax_player&nonce=&episode=1&postid={$post_id}&lang=" . urlencode($language) . "&server=1";
            $this->log("POST data: $post_data");
            
            $response = $this->callPrivateMethod($scraper, 'make_24hd_ajax_request', [
                'https://api.24-hd.com/get.php',
                $post_data
            ]);
            
            if ($response && $response['success']) {
                $this->log("✅ AJAX request successful for $language");
                $this->log("Response length: " . strlen($response['content']));
                
                if (preg_match('/<iframe[^>]*src=["\'](https:\/\/main\.24playerhd\.com\/[^"\']*?)["\']/i', $response['content'], $matches)) {
                    $iframe_url = $matches[1];
                    $this->log("✅ Found iframe URL: $iframe_url");
                } else {
                    $this->log("❌ No iframe found in response");
                }
            } else {
                $this->log("❌ AJAX request failed for $language");
                if (isset($response['error'])) {
                    $this->log("Error: " . $response['error']);
                }
            }
        }
        
        return true;
    }
    
    public function testFullScraping() {
        $this->log("=== Testing Full Scraping Process ===");
        
        $test_url = 'https://www.24-hd.com/stolen/';
        $fake_post_id = 999999;
        
        $scraper = new HD24MovieScraper();
        
        try {
            $this->log("Starting full scrape test...");
            
            $html = $this->fetchHtml($test_url);
            if (!$html) {
                $this->log("❌ Failed to fetch HTML");
                return false;
            }
            
            $movie_data = $this->callPrivateMethod($scraper, 'extract_24hd_movie_data', [$fake_post_id, $html]);
            
            if ($movie_data) {
                $this->log("✅ Movie data extracted successfully");
                $this->log("Dubbed data: " . (empty($movie_data['dubbed']) ? 'Empty' : 'Found'));
                $this->log("Subbed data: " . (empty($movie_data['subbed']) ? 'Empty' : 'Found'));
                
                if (!empty($movie_data['dubbed'])) {
                    $this->log("Dubbed M3U8: " . $movie_data['dubbed']['m3u8_url']);
                }
                
                if (!empty($movie_data['subbed'])) {
                    $this->log("Subbed M3U8: " . $movie_data['subbed']['m3u8_url']);
                }
            } else {
                $this->log("❌ Failed to extract movie data");
                return false;
            }
            
        } catch (Exception $e) {
            $this->log("❌ Exception during scraping: " . $e->getMessage());
            return false;
        }
        
        return true;
    }
    
    private function fetchHtml($url) {
        $this->log("Fetching HTML from: $url");
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code !== 200) {
            $this->log("HTTP Error: $http_code");
            return false;
        }
        
        return $response;
    }
    
    private function callPrivateMethod($object, $method, $args = []) {
        $reflection = new ReflectionClass($object);
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $args);
    }
    
    public function runAllTests() {
        $this->log("🚀 Starting 24-HD Scraper Tests");
        $this->log("=" * 50);
        
        $tests = [
            'testMetadataExtraction',
            'testPostIdExtraction', 
            'testAjaxRequest',
            'testFullScraping'
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            $this->log("");
            $result = $this->$test();
            if ($result) {
                $passed++;
                $this->log("✅ $test PASSED");
            } else {
                $this->log("❌ $test FAILED");
            }
        }
        
        $this->log("");
        $this->log("=" * 50);
        $this->log("🏁 Test Results: $passed/$total tests passed");
        
        if ($passed === $total) {
            $this->log("🎉 All tests passed! HD24MovieScraper is ready.");
        } else {
            $this->log("⚠️  Some tests failed. Check implementation.");
        }
    }
}

if (php_sapi_name() === 'cli') {
    $test = new HD24ScraperTest();
    $test->runAllTests();
} else {
    echo "This script should be run from command line.\n";
}

?>
