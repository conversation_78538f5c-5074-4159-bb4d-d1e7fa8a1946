<?php
/*
Plugin Name: HLS P2P System
Description: Plugin for uploading videos to servers with load balancing.
Version: 1.08
Author: Aek AI
*/

if (!defined('ABSPATH')) {
    exit;
}

include_once plugin_dir_path(__FILE__) . 'includes/settings.php';
include_once plugin_dir_path(__FILE__) . 'includes/uploader.php';
include_once plugin_dir_path(__FILE__) . 'includes/add-video.php';
include_once plugin_dir_path(__FILE__) . 'includes/video-list.php';
if (file_exists(plugin_dir_path(__FILE__) . 'includes/scrapers/BaseScraper.php')) {
    include_once plugin_dir_path(__FILE__) . 'includes/scrapers/BaseScraper.php';
}
if (file_exists(plugin_dir_path(__FILE__) . 'includes/scrapers/MovieScraper.php')) {
    include_once plugin_dir_path(__FILE__) . 'includes/scrapers/MovieScraper.php';
}
if (file_exists(plugin_dir_path(__FILE__) . 'includes/scrapers/SeriesScraper.php')) {
    include_once plugin_dir_path(__FILE__) . 'includes/scrapers/SeriesScraper.php';
}


function enqueue_video_uploader_scripts() {
    if (is_admin()) {
        wp_enqueue_script('jquery');
        if (!wp_script_is('sweetalert2', 'enqueued')) {
            wp_enqueue_script('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11', array('jquery'), '11.0.0', true);
        }
    }
}
add_action('admin_enqueue_scripts', 'enqueue_video_uploader_scripts');

function vu_enqueue_scripts($hook) {
    if (strpos($hook, 'video-list') !== false || 
        strpos($hook, 'tools') !== false || 
        strpos($hook, 'post.php') !== false || 
        strpos($hook, 'post-new.php') !== false) {
        
        wp_enqueue_style('sweetalert2', 'https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css');
    }
    
    wp_enqueue_style('vu-style', plugin_dir_url(__FILE__) . 'css/style.css');
    
    if (strpos($hook, 'video-list') !== false || strpos($hook, 'tools') !== false) {
        wp_enqueue_script('vu-script', plugin_dir_url(__FILE__) . 'js/vu-script.js', array('jquery'), '1.08', true);
    }
}
add_action('admin_enqueue_scripts', 'vu_enqueue_scripts');

function vu_render_menu() {
    add_menu_page('Video List','HLS P2P System','edit_posts','video-list','vu_render_video_list_page','dashicons-video-alt3');
    add_submenu_page('video-list','Video List','Video List','edit_posts','video-list','vu_render_video_list_page');
    add_submenu_page('video-list','Tools','Tools','edit_posts','tools','vu_render_tools_page');
    add_submenu_page('video-list','Settings','Settings','edit_posts','settings','vu_render_settings_page');
}
add_action('admin_menu','vu_render_menu');

register_activation_hook(__FILE__, function() {
    flush_rewrite_rules();
});

function vu_render_tools_page() {
    ?>
    <div class="wrap">
        <h1>Tools</h1>
        <p>Here you can find various tools to manage your video uploader.</p>
    </div>
    <?php
}

function display_video_uploader_page() {
    echo '<div class="wrap">';
    echo '<h1>Video Uploader</h1>';
    echo '<form method="post" enctype="multipart/form-data">';
    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th scope="row">Select Video File</th>';
    echo '<td><input type="file" name="video_file" accept="video/*" required></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th scope="row">Video Title</th>';
    echo '<td><input type="text" name="video_title" class="regular-text" required></td>';
    echo '</tr>';
    echo '</table>';
    echo '<p class="submit"><input type="submit" name="upload_video" class="button-primary" value="Upload Video"></p>';
    echo '</form>';
    echo '</div>';
}
