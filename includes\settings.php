<?php
function vu_render_settings_page() {
    $servers = get_option('vu_servers', []);
    $api_key = get_option('vu_api_key', '');
    $post_types = get_option('vu_post_types', ['movie', 'serie', 'anime', 'adult']);
    $access_token = get_option('vu_access_token', '');
    $client_id = get_option('vu_client_id', '');
    $client_secret = get_option('vu_client_secret', '');
    $cf_email = get_option('cf_email', '');
    $cf_api_key = get_option('cf_api_key', '');
    $cf_zone_id = get_option('cf_zone_id', '');
    $master_ip = get_option('vu_master_ip', '');
    $hls_mode = get_option('vu_hls_mode', true);
    $scraping_mode = get_option('vu_scraping_mode', false);
    $scraping_servers = get_option('vu_scraping_servers', []);
    $all_post_types = get_post_types(['public' => true], 'names');
    unset($all_post_types['attachment']);
    ?>
    <style>
    /* Modern Design System */
    .wrap {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
        width: 100%;
        margin: 0;
        padding: 20px 40px;
        box-sizing: border-box;
    }
    
    /* Header Styling */
    .wrap h1 {
        font-size: 2rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    /* Status Message */
    #vu-status-message {
        border-radius: 8px;
        padding: 12px 16px;
        margin-bottom: 20px;
        font-weight: 500;
    }
    
    /* Settings Section Containers */
    .settings-section-wrapper {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        display: none;
    }
    
    .settings-section-wrapper:hover {
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
    }
    

    
    /* Server Field Containers */
    .vu-server-field, .vu-scraping-server-field {
        display: grid;
        gap: 16px;
        align-items: center;
        margin-bottom: 16px;
        padding: 20px;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    .vu-server-field {
        grid-template-columns: 2fr 1fr auto auto auto auto;
    }
    
    .vu-scraping-server-field {
        grid-template-columns: 2fr 1fr 1fr auto;
    }
    
    /* Ensure consistent button sizing */
    .vu-server-field button, .vu-scraping-server-field button {
        min-width: 90px;
        height: 40px;
        white-space: nowrap;
    }
    
    .vu-server-field:hover, .vu-scraping-server-field:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        transform: translateY(-2px);
    }
    
    /* Input Styling */
    .vu-server-field input, .vu-scraping-server-field input, 
    .vu-master-server-field input, .vu-api-field input, 
    .vu-api-field textarea {
        padding: 12px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: #ffffff;
        font-weight: 500;
    }
    
    .vu-server-field input:focus, .vu-scraping-server-field input:focus,
    .vu-master-server-field input:focus, .vu-api-field input:focus,
    .vu-api-field textarea:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        background: #fafbff;
    }
    

    
    /* Button Styling */
    .button {
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        cursor: pointer;
    }
    
    .button-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-color: #3b82f6;
    }
    
    .button-primary:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    }
    

    
    .button.vu-add-cdn, .button.vu-check-server {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-color: #10b981;
        padding: 10px 16px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .button.vu-add-cdn:hover, .button.vu-check-server:hover {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
    }
    
    .button.vu-check-server {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-color: #3b82f6;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .button.vu-check-server:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
    }
    
    .button.vu-remove-server, .button.vu-remove-scraping-server {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
        border-color: #ef4444;
        padding: 10px 16px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .button.vu-remove-server:hover, .button.vu-remove-scraping-server:hover {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
    }
    
    #vu-add-server, #vu-add-scraping-server {
        width: 100%;
        margin-top: 16px;
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        color: white;
        border-color: #8b5cf6;
        padding: 16px 24px;
        font-size: 15px;
        font-weight: 600;
        text-align: center;
        border-radius: 12px;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    }
    
    #vu-add-server:hover, #vu-add-scraping-server:hover {
        background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
    }
    
    #vu-add-server:disabled, #vu-add-scraping-server:disabled {
        background: #cccccc !important;
        cursor: not-allowed !important;
        transform: none !important;
        box-shadow: none !important;
        opacity: 0.6;
    }
    
    /* Server Status Button */
    .vu-server-status {
        padding: 10px 16px;
        font-weight: 600;
        border-radius: 8px;
        border: 2px solid;
        cursor: default;
        font-size: 13px;
        transition: all 0.3s ease;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .vu-server-status.not-connected {
        background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
        color: white;
        border-color: #ef4444;
        box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
    }
    
    .vu-server-status.connected {
        background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
        color: white;
        border-color: #10b981;
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
    }
    

    

        margin-top: 16px;
    }
    

    

    
    /* No Servers Message */
    .no-servers-message {
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        font-style: italic;
        padding: 24px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px dashed rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        margin-bottom: 20px;
        backdrop-filter: blur(10px);
    }
    
    .no-servers-message:before {
        content: "📝";
        font-size: 2em;
        display: block;
        margin-bottom: 8px;
    }
    

    
    /* Modern HLS Settings Design */
    .hls-modern-wrapper {
        display: flex;
        flex-direction: column;
        gap: 32px;
        margin-bottom: 32px;
    }
    
    .hls-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }
    
    .hls-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
        border-color: #cbd5e1;
    }
    
    .hls-card-header {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: white;
        padding: 24px 32px;
        border-bottom: 2px solid #475569;
    }
    
    .hls-master-card .hls-card-header {
        background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
        color: #92400e;
        border-bottom-color: #d97706;
    }
    
    .hls-clients-card .hls-card-header {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-bottom-color: #1e40af;
    }
    
    .hls-api-card .hls-card-header {
        background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        color: white;
        border-bottom-color: #4338ca;
    }
    
    .scraping-servers-card .hls-card-header {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-bottom-color: #047857;
    }
    
    .hls-card-header h3 {
        margin: 0 0 8px 0;
        font-size: 1.4rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .hls-card-header p {
        margin: 0;
        color: #cbd5e1;
        font-size: 0.95rem;
        opacity: 0.9;
    }
    
    .hls-card-content {
        padding: 32px;
    }
    
    .hls-section {
        margin-bottom: 32px;
    }
    
    .hls-section:last-child {
        margin-bottom: 0;
    }
    
    .hls-section h4 {
        margin: 0 0 20px 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        padding-bottom: 12px;
        border-bottom: 2px solid #e2e8f0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .hls-grid {
        display: grid;
        gap: 24px;
    }
    
    .hls-grid-2 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .hls-grid-3 {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .hls-input-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }
    
    .hls-input-group label {
        font-weight: 600;
        color: #374151;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        opacity: 0.8;
    }
    
    .hls-input-group input,
    .hls-input-group textarea,
    .hls-input-group select {
        width: 100%;
        padding: 14px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #ffffff;
        box-sizing: border-box;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .hls-input-group input:focus,
    .hls-input-group textarea:focus,
    .hls-input-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        background: #fefefe;
    }
    
    .hls-input-group textarea {
        resize: vertical;
        min-height: 120px;
        font-family: 'Courier New', monospace;
        line-height: 1.5;
    }
    
    .hls-checkbox-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        margin-top: 8px;
    }
    
    .hls-checkbox {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px 16px;
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }
    
    .hls-checkbox:hover {
        background: #f1f5f9;
        border-color: #cbd5e1;
        transform: translateY(-1px);
    }
    
    .hls-checkbox input[type="checkbox"] {
        margin: 0;
        width: 18px;
        height: 18px;
        accent-color: #3b82f6;
    }
    
    .hls-checkbox input[type="checkbox"]:checked + .hls-checkbox-label {
        font-weight: 600;
        color: #1e40af;
    }
    
    .hls-server-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 24px;
    }
    
    .hls-empty-state {
        text-align: center;
        padding: 48px 24px;
        background: #f8fafc;
        border: 2px dashed #cbd5e1;
        border-radius: 16px;
        color: #6b7280;
    }
    
    .hls-empty-icon {
        font-size: 3rem;
        margin-bottom: 16px;
        opacity: 0.7;
    }
    
    .hls-empty-state p {
        margin: 0 0 8px 0;
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    .hls-empty-state small {
        opacity: 0.8;
    }
    
    .hls-server-item {
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        padding: 24px;
        transition: all 0.3s ease;
    }
    
    .hls-server-item:hover {
        border-color: #cbd5e1;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    }
    
    .hls-server-info {
        display: grid;
        grid-template-columns: 5fr 3fr;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .hls-server-input,
    .hls-server-cdn {
        display: flex;
        flex-direction: column;
    }
    
    .hls-server-input input,
    .hls-server-cdn select {
        width: 100%;
        padding: 14px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #ffffff;
        box-sizing: border-box;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        max-width: none;
    }
    
    .hls-server-input input:focus,
    .hls-server-cdn select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
        background: #fefefe;
    }
    
    .hls-server-input label,
    .hls-server-cdn label {
        font-weight: 600;
        color: #374151;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        opacity: 0.8;
    }
    
    .scraping-server-list {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-bottom: 24px;
    }
    
    .scraping-server-item {
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 16px;
        padding: 24px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    .scraping-server-item:hover {
        border-color: #10b981;
        box-shadow: 0 4px 16px rgba(16, 185, 129, 0.15);
        transform: translateY(-2px);
    }
    
    .scraping-server-info {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .scraping-server-input {
        display: flex;
        flex-direction: column;
    }
    
    .scraping-server-input input {
        width: 100%;
        padding: 14px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background: #ffffff;
        box-sizing: border-box;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        max-width: none;
    }
    
    .scraping-server-input input:focus {
        outline: none;
        border-color: #10b981;
        box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
        background: #fefefe;
    }
    
    .scraping-server-input label {
        font-weight: 600;
        color: #374151;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 6px;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        opacity: 0.8;
    }
    
    .scraping-server-actions {
        display: flex;
        gap: 12px;
        align-items: center;
        justify-content: flex-end;
    }
    
    .scraping-server-status {
        padding: 10px 16px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        cursor: default;
        transition: all 0.3s ease;
    }
    
    .scraping-server-status.not-connected {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }
    
    .scraping-server-status.connected {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #bbf7d0;
    }
    
    .scraping-server-status.checking {
        background: #fef3c7;
        color: #d97706;
        border: 1px solid #fed7aa;
    }
    
    .hls-server-actions {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 12px;
    }
    
    .hls-btn {
        padding: 12px 16px;
        border: none;
        border-radius: 10px;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        text-decoration: none;
        box-sizing: border-box;
        min-height: 44px;
        text-align: center;
    }
    
    .hls-btn-primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    .hls-btn-primary:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }
    
    .hls-btn-secondary {
        background: #6b7280;
        color: white;
    }
    
    .hls-btn-secondary:hover {
        background: #4b5563;
        transform: translateY(-1px);
    }
    
    .hls-btn-info {
        background: #0ea5e9;
        color: white;
    }
    
    .hls-btn-info:hover {
        background: #0284c7;
        transform: translateY(-1px);
    }
    
    .hls-btn-danger {
        background: #ef4444;
        color: white;
    }
    
    .hls-btn-danger:hover {
        background: #dc2626;
        transform: translateY(-1px);
    }
    
    .hls-add-btn {
        width: 100%;
        padding: 16px 24px;
        font-size: 1rem;
        justify-content: center;
    }
    
    .hls-btn-icon {
        font-size: 1.1em;
    }
    
    .hls-server-status {
        padding: 12px 16px;
        border: none;
        border-radius: 10px;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: default;
        min-height: 44px;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .hls-server-status.not-connected {
        background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
        color: #dc2626;
        border: 2px solid #f87171;
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.2);
    }
    
    .hls-server-status.connected {
        background: linear-gradient(135deg, #bbf7d0 0%, #86efac 100%);
        color: #16a34a;
        border: 2px solid #4ade80;
        box-shadow: 0 2px 8px rgba(22, 163, 74, 0.2);
        animation: pulse 3s infinite;
    }
    
    .hls-server-status.checking {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #d97706;
        border: 2px solid #facc15;
        box-shadow: 0 2px 8px rgba(217, 119, 6, 0.2);
        animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.6; }
    }
    
    /* Modern CDN Popup Styles */
    .cdn-popup-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(4px);
    }
    
    .cdn-popup-content {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        border: 2px solid #e2e8f0;
    }
    
    .cdn-popup-header {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: white;
        padding: 24px 32px;
        border-radius: 18px 18px 0 0;
        border-bottom: 2px solid #475569;
    }
    
    .cdn-popup-header h3 {
        margin: 0 0 8px 0;
        font-size: 1.3rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .cdn-popup-header p {
        margin: 0;
        color: #cbd5e1;
        font-size: 0.95rem;
        opacity: 0.9;
    }
    
    .cdn-popup-body {
        padding: 32px;
    }
    
    .cdn-section {
        margin-bottom: 32px;
    }
    
    .cdn-section:last-child {
        margin-bottom: 0;
    }
    
    .cdn-section h4 {
        margin: 0 0 16px 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        padding-bottom: 8px;
        border-bottom: 2px solid #e2e8f0;
    }
    
    .cdn-list-container {
        min-height: 60px;
        max-height: 200px;
        overflow-y: auto;
        border: 2px dashed #cbd5e1;
        border-radius: 12px;
        padding: 16px;
        background: #f8fafc;
    }
    
    .cdn-item-modern {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 8px;
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .cdn-item-modern:hover {
        border-color: #cbd5e1;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .cdn-item-modern:last-child {
        margin-bottom: 0;
    }
    
    .cdn-item-modern span {
        font-weight: 500;
        color: #374151;
        flex: 1;
    }
    
    .cdn-input-group {
        display: flex;
        gap: 12px;
        align-items: center;
    }
    
    .cdn-input-group input {
        flex: 1;
        padding: 14px 16px;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }
    
    .cdn-input-group input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    }
    
    .cdn-popup-footer {
        padding: 24px 32px;
        border-top: 2px solid #e2e8f0;
        background: #f8fafc;
        border-radius: 0 0 18px 18px;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }
    
    .cdn-popup-footer .hls-btn {
        min-width: 120px;
        justify-content: center;
    }
    

    

    /* Responsive Design for Modern HLS Settings */
    @media (max-width: 1400px) {
        .hls-grid-3 {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    @media (max-width: 1024px) {
        .hls-grid-3,
        .hls-grid-2 {
            grid-template-columns: 1fr;
        }
        
        .hls-server-info {
            grid-template-columns: 1fr;
        }
        
        .hls-server-actions {
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }
        
        .hls-checkbox-grid {
            grid-template-columns: 1fr;
        }
    }
    
    @media (max-width: 768px) {
        .wrap {
            padding: 16px 20px;
        }
        
        .vu-mode-toggles {
            padding: 24px 20px;
        }
        
        .hls-modern-wrapper {
            gap: 24px;
        }
        
        .hls-card-header {
            padding: 20px 24px;
        }
        
        .hls-card-content {
            padding: 24px;
        }
        
        .hls-grid {
            gap: 16px;
        }
        
        .hls-server-actions {
            justify-content: flex-start;
        }
        
        .hls-btn {
            padding: 8px 16px;
            font-size: 0.85rem;
        }
        
        .hls-add-btn {
            padding: 14px 20px;
        }
    }
    
    @media (max-width: 480px) {
        .wrap {
            padding: 12px 16px;
        }
        
        .vu-mode-toggle {
            flex-direction: column;
            text-align: center;
            gap: 16px;
        }
        
        .vu-toggle-switch {
            margin-left: 0;
        }
        
        .hls-card-header {
            padding: 16px 20px;
        }
        
        .hls-card-header h3 {
            font-size: 1.2rem;
        }
        
        .hls-card-content {
            padding: 20px;
        }
        
        .hls-server-item {
            padding: 16px;
        }
        
        .hls-server-actions {
            flex-direction: column;
            align-items: stretch;
        }
        
        .hls-btn {
            justify-content: center;
            width: 100%;
        }
        
        .hls-input-group input,
        .hls-input-group textarea,
        .hls-input-group select {
            padding: 12px 14px;
            font-size: 0.9rem;
        }
        
        .form-actions {
            padding: 20px 16px;
        }
        
        .form-actions button {
            padding: 14px 32px;
            font-size: 15px;
            min-width: 180px;
        }
        
        .cdn-popup-content {
            width: 95%;
            margin: 10px;
        }
        
        .cdn-popup-header,
        .cdn-popup-body,
        .cdn-popup-footer {
            padding: 16px 20px;
        }
        
        .cdn-input-group {
            flex-direction: column;
            align-items: stretch;
        }
        
        .cdn-popup-footer {
            flex-direction: column;
        }
        
        .cdn-popup-footer .hls-btn {
            width: 100%;
        }
    }
    
    @media (max-width: 600px) {
        .hls-server-actions {
            grid-template-columns: 1fr;
            gap: 8px;
        }
        
        .hls-btn {
            font-size: 0.8rem;
            padding: 10px 12px;
            min-height: 40px;
        }
        
        .hls-server-status {
            font-size: 0.8rem;
            padding: 10px 12px;
            min-height: 40px;
        }
    }
    
    /* Mode Toggle Section */
    .vu-mode-toggles {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 16px;
        padding: 32px;
        margin-bottom: 32px;
        box-shadow: 0 8px 32px rgba(102, 126, 234, 0.2);
        color: white;
    }
    
    .vu-mode-toggles h2 {
        margin-top: 0;
        margin-bottom: 24px;
        font-size: 1.5rem;
        font-weight: 700;
        text-align: center;
        color: white;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .vu-mode-toggle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        margin-bottom: 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .vu-mode-toggle:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }
    
    .vu-mode-toggle:last-child {
        margin-bottom: 0;
    }
    
    .vu-mode-info {
        flex: 1;
    }
    
    .vu-mode-title {
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 8px;
        color: white;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .vu-mode-title:before {
        font-size: 1.3em;
    }
    
    .vu-mode-toggle:nth-child(2) .vu-mode-title:before {
        content: "🎬";
    }
    
    .vu-mode-toggle:nth-child(3) .vu-mode-title:before {
        content: "🌐";
    }
    
    .vu-mode-desc {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
        line-height: 1.4;
    }
    
    /* Modern Toggle Switch */
    .vu-toggle-switch {
        position: relative;
        display: inline-block;
        width: 70px;
        height: 40px;
        margin-left: 20px;
    }
    
    .vu-toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .vu-toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.3);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 40px;
        border: 2px solid rgba(255, 255, 255, 0.2);
    }
    
    .vu-toggle-slider:before {
        position: absolute;
        content: "";
        height: 32px;
        width: 32px;
        left: 2px;
        bottom: 2px;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    input:checked + .vu-toggle-slider {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-color: #10b981;
        box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);
    }
    
    input:focus + .vu-toggle-slider {
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
    }
    
    input:checked + .vu-toggle-slider:before {
        transform: translateX(30px);
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
    }
    
    /* Hover Effects */
    .vu-toggle-switch:hover .vu-toggle-slider {
        background: rgba(255, 255, 255, 0.4);
    }
    
    .vu-toggle-switch:hover input:checked + .vu-toggle-slider {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
    }
    
    /* Status Messages */
    .notice-success {
        background: #d4edda;
        border-left: 4px solid #28a745;
        color: #155724;
    }
    
    .notice-error {
        background: #f8d7da;
        border-left: 4px solid #dc3545;
        color: #721c24;
    }
    
    /* Form Actions */
    .form-actions {
        text-align: center;
        margin-top: 40px;
        padding: 32px;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 16px;
        border: 2px solid #cbd5e1;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    }
    
    .form-actions button {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border: none;
        padding: 18px 48px;
        border-radius: 12px;
        font-size: 17px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        min-width: 220px;
        width: 100%;
        max-width: 400px;
    }
    
    .form-actions button:before {
        content: "💾";
        font-size: 1.2em;
    }
    
    .form-actions button:hover {
        background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.5);
    }
    
    .form-actions button:active {
        transform: translateY(0);
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
    }
    
    .form-actions button:disabled {
        background: #94a3b8 !important;
        cursor: not-allowed !important;
        transform: none !important;
        box-shadow: none !important;
    }
    </style>
    <div class="wrap">
        <h1>Server Settings</h1>
        <div id="vu-status-message" style="display: none; padding: 10px; margin-bottom: 10px;"></div>
        
        <div class="vu-mode-toggles">
            <h2>Video Processing Modes</h2>
            <div class="vu-mode-toggle">
                <div class="vu-mode-info">
                    <div class="vu-mode-title">HLS Mode</div>
                    <div class="vu-mode-desc">ใช้ระบบ HLS สำหรับการประมวลผลวิดีโอจาก Google Drive</div>
                </div>
                <label class="vu-toggle-switch">
                    <input type="checkbox" id="vu_hls_mode" name="vu_hls_mode" <?php echo $hls_mode ? 'checked' : ''; ?>>
                    <span class="vu-toggle-slider"></span>
                </label>
            </div>
            <div class="vu-mode-toggle">
                <div class="vu-mode-info">
                    <div class="vu-mode-title">Scraping Mode</div>
                    <div class="vu-mode-desc">ใช้ระบบ Scraping สำหรับการดึงไฟล์ M3U8 จากเว็บไซต์</div>
                </div>
                <label class="vu-toggle-switch">
                    <input type="checkbox" id="vu_scraping_mode" name="vu_scraping_mode" <?php echo $scraping_mode ? 'checked' : ''; ?>>
                    <span class="vu-toggle-slider"></span>
                </label>
            </div>
        </div>

        <form id="vu-settings-form" method="post" action="<?php echo admin_url('admin-post.php'); ?>">
            <input type="hidden" name="action" value="save_vu_server_settings">
            <input type="hidden" id="hidden_hls_mode" name="vu_hls_mode_value" value="<?php echo $hls_mode ? '1' : '0'; ?>">
            <input type="hidden" id="hidden_scraping_mode" name="vu_scraping_mode_value" value="<?php echo $scraping_mode ? '1' : '0'; ?>">

            <!-- HLS Settings Section - Modern Design -->
            <div id="hls-settings-wrapper" class="hls-modern-wrapper">
                <!-- Master Server Card -->
                <div class="hls-card hls-master-card">
                    <div class="hls-card-header">
                        <h3>🎯 Master Server</h3>
                        <p>Primary server configuration for HLS streaming</p>
            </div>
                    <div class="hls-card-content">
                        <div class="hls-input-group">
                            <label for="vu_master_ip">Server IP Address</label>
                            <input type="text" id="vu_master_ip" name="vu_master_ip" placeholder="*************" value="<?php echo esc_attr($master_ip); ?>" />
                        </div>
                    </div>
                </div>

                <!-- Client Servers Card -->
                <div class="hls-card hls-clients-card">
                    <div class="hls-card-header">
                        <h3>🌐 Client Servers</h3>
                        <p>Configure multiple client servers for load balancing</p>
                    </div>
                    <div class="hls-card-content">
                        <div id="vu-server-fields" class="hls-server-list">
                            <?php if (empty($servers)) : ?>
                                <div class="hls-empty-state">
                                    <div class="hls-empty-icon">📡</div>
                                    <p>No client servers configured</p>
                                    <small>Add your first server to get started</small>
                                </div>
                            <?php else : ?>
                <?php foreach ($servers as $index => $server) : ?>
                                    <div class="hls-server-item" data-index="<?php echo $index; ?>">
                                        <div class="hls-server-info">
                                            <div class="hls-server-input">
                                                <label>Server IP</label>
                                                <input type="text" name="vu_servers[<?php echo $index; ?>][ip]" placeholder="Server IP Address" value="<?php echo esc_attr($server['ip']); ?>" />
                                            </div>
                                            <div class="hls-server-cdn">
                                                <label>CDN Domains</label>
                        <select name="vu_servers[<?php echo $index; ?>][cdn][]" class="cdn-select">
                            <?php
                            $cdnList = get_option("vu_server_cdn_$index", []);
                            foreach ($cdnList as $cdnDomain) : ?>
                                <option value="<?php echo esc_attr($cdnDomain); ?>" selected><?php echo esc_attr($cdnDomain); ?></option>
                            <?php endforeach; ?>
                        </select>
                                            </div>
                                        </div>
                                        <div class="hls-server-actions">
                                            <button type="button" class="hls-btn hls-btn-secondary vu-add-cdn">Add CDN</button>
                                            <button type="button" class="hls-btn hls-btn-info vu-check-server">Check</button>
                                            <button type="button" class="hls-server-status not-connected">Unknown</button>
                                            <button type="button" class="hls-btn hls-btn-danger vu-remove-server">Remove</button>
                                        </div>
                    </div>
                <?php endforeach; ?>
                            <?php endif; ?>
            </div>
                        <button type="button" id="vu-add-server" class="hls-btn hls-btn-primary hls-add-btn">
                            <span class="hls-btn-icon">➕</span>
                            Add Client Server
                        </button>
                    </div>
                </div>

                <!-- API Configuration Card -->
                <div class="hls-card hls-api-card">
                    <div class="hls-card-header">
                        <h3>🔧 API Configuration</h3>
                        <p>Google Drive API and Cloudflare settings</p>
                    </div>
                    <div class="hls-card-content">
                        <!-- Google API Section -->
                        <div class="hls-section">
                            <h4>🔑 Google Drive API</h4>
                            <div class="hls-grid hls-grid-3">
                                <div class="hls-input-group">
                            <label for="vu_api_key">API Key</label>
                                    <input type="text" id="vu_api_key" name="vu_api_key" value="<?php echo esc_attr($api_key); ?>" />
                        </div>
                                <div class="hls-input-group">
                            <label for="vu_client_id">Client ID</label>
                                    <input type="text" id="vu_client_id" name="vu_client_id" value="<?php echo esc_attr($client_id); ?>" />
                        </div>
                                <div class="hls-input-group">
                            <label for="vu_client_secret">Client Secret</label>
                                    <input type="text" id="vu_client_secret" name="vu_client_secret" value="<?php echo esc_attr($client_secret); ?>" />
                        </div>
                    </div>
                        </div>

                        <!-- Cloudflare Section -->
                        <div class="hls-section">
                            <h4>☁️ Cloudflare CDN</h4>
                            <div class="hls-grid hls-grid-3">
                                <div class="hls-input-group">
                                    <label for="cf_email">Email</label>
                                    <input type="email" id="cf_email" name="cf_email" value="<?php echo esc_attr($cf_email); ?>" />
                        </div>
                                <div class="hls-input-group">
                                    <label for="cf_api_key">API Key</label>
                                    <input type="text" id="cf_api_key" name="cf_api_key" value="<?php echo esc_attr($cf_api_key); ?>" />
                        </div>
                                <div class="hls-input-group">
                                    <label for="cf_zone_id">Zone ID</label>
                                    <input type="text" id="cf_zone_id" name="cf_zone_id" value="<?php echo esc_attr($cf_zone_id); ?>" />
                    </div>
                </div>
                        </div>

                        <!-- Post Types & Access Token Section -->
                        <div class="hls-section">
                            <div class="hls-grid hls-grid-2">
                                <div class="hls-input-group">
                                    <label>📝 Post Types</label>
                                    <div class="hls-checkbox-grid">
                            <?php foreach ($all_post_types as $post_type) : ?>
                                            <label class="hls-checkbox">
                                    <input type="checkbox" name="vu_post_types[]" value="<?php echo esc_attr($post_type); ?>" <?php echo in_array($post_type, $post_types) ? 'checked' : ''; ?>>
                                                <span class="hls-checkbox-label"><?php echo esc_html($post_type); ?></span>
                                </label>
                            <?php endforeach; ?>
                        </div>
                    </div>
                                <div class="hls-input-group">
                                    <label for="vu_access_token">🎫 Access Token</label>
                                    <textarea id="vu_access_token" name="vu_access_token" rows="6"><?php echo esc_textarea($access_token); ?></textarea>
                    </div>
                </div>
            </div>
                    </div>
                </div>
            </div>

            <!-- Scraping Settings Section -->
            <div id="scraping-settings-wrapper" class="settings-section-wrapper">
                <!-- Scraping Servers Card -->
                <div class="hls-card scraping-servers-card">
                    <div class="hls-card-header">
                        <h3>🔗 Scraping Servers</h3>
                        <p>Configure M3U8 scraping servers for content extraction</p>
                    </div>
                    <div class="hls-card-content">
                        <div id="vu-scraping-server-fields" class="scraping-server-list">
                            <?php if (empty($scraping_servers)) : ?>
                                <div class="hls-empty-state">
                                    <div class="hls-empty-icon">🔗</div>
                                    <p>No scraping servers configured</p>
                                    <small>Add your first scraping server to get started</small>
                                </div>
                            <?php else : ?>
                                <?php foreach ($scraping_servers as $index => $server) : ?>
                                    <div class="scraping-server-item" data-index="<?php echo $index; ?>">
                                        <div class="scraping-server-info">
                                            <div class="scraping-server-input">
                                                <label>Server IP</label>
                                                <input type="text" name="vu_scraping_servers[<?php echo $index; ?>][ip]" placeholder="*************" value="<?php echo esc_attr($server['ip']); ?>" required />
                                            </div>
                                            <div class="scraping-server-input">
                                                <label>Username</label>
                                                <input type="text" name="vu_scraping_servers[<?php echo $index; ?>][username]" placeholder="admin" value="<?php echo esc_attr($server['username']); ?>" />
                                            </div>
                                            <div class="scraping-server-input">
                                                <label>Password</label>
                                                <input type="password" name="vu_scraping_servers[<?php echo $index; ?>][password]" placeholder="••••••••" value="<?php echo esc_attr($server['password']); ?>" />
                                            </div>
                                        </div>
                                        <div class="scraping-server-actions">
                                            <button type="button" class="hls-btn hls-btn-info scraping-test-connection">Test</button>
                                            <button type="button" class="scraping-server-status not-connected">Unknown</button>
                                            <button type="button" class="hls-btn hls-btn-danger vu-remove-scraping-server">Remove</button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        <button type="button" id="vu-add-scraping-server" class="hls-btn hls-btn-primary hls-add-btn">
                            <span class="hls-btn-icon">➕</span>
                            Add Scraping Server
                        </button>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" id="vu-save-settings" class="button button-primary button-large">
                    Save All Settings
                </button>
            </div>
        </form>
    </div>
    <script>
    // Settings Page JavaScript v1.09 - Fixed duplicate server creation
    var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
    jQuery(document).ready(function($) {
        // Show/hide sections based on toggle states with smooth animations
        function toggleSettingsSections() {
            var hlsEnabled = $('#vu_hls_mode').is(':checked');
            var scrapingEnabled = $('#vu_scraping_mode').is(':checked');
            
            console.log('HLS Mode:', hlsEnabled, 'Scraping Mode:', scrapingEnabled);
            
            // Update hidden fields
            $('#hidden_hls_mode').val(hlsEnabled ? '1' : '0');
            $('#hidden_scraping_mode').val(scrapingEnabled ? '1' : '0');
            
            // Show/hide sections with smooth animation
            if (hlsEnabled) {
                $('#hls-settings-wrapper').slideDown(300);
            } else {
                $('#hls-settings-wrapper').slideUp(300);
            }
            
            if (scrapingEnabled) {
                $('#scraping-settings-wrapper').slideDown(300);
            } else {
                $('#scraping-settings-wrapper').slideUp(300);
            }
        }
        
        // Toggle event handlers
        $('#vu_hls_mode, #vu_scraping_mode').on('change', function() {
            console.log('Toggle changed:', $(this).attr('id'), $(this).is(':checked'));
            toggleSettingsSections();
        });
        
        // Also handle click events on the slider for better responsiveness
        $('.vu-toggle-slider').on('click', function() {
            var checkbox = $(this).prev('input[type="checkbox"]');
            setTimeout(function() {
                toggleSettingsSections();
            }, 50);
        });
        
        // Initial call to set visibility on page load
        setTimeout(function() {
            toggleSettingsSections();
            checkAllServerStatuses();
            setupAutoStatusCheck();
        }, 100);
        
        function setupAutoStatusCheck() {
            var checkTimeout;
            
            // Add auto-check on input change with debounce
            $(document).on('input blur', '.hls-server-item input[name*="[ip]"]', function() {
                var $input = $(this);
                var ip = $input.val().trim();
                var serverItem = $input.closest('.hls-server-item');
                var statusButton = serverItem.find('.hls-server-status');
                
                // Clear previous timeout
                if (checkTimeout) {
                    clearTimeout(checkTimeout);
                }
                
                if (ip) {
                    // Show checking state immediately
                    statusButton.removeClass('not-connected connected').addClass('checking').text('Checking...');
                    
                    // Debounce the actual check
                    checkTimeout = setTimeout(function() {
                        $.ajax({
                            url: ajaxurl,
                            method: 'POST',
                            data: { 
                                action: 'check_vu_server_connection',
                                ip: ip
                            },
                            success: function(response) {
                                if (response.success) {
                                    statusButton.removeClass('not-connected checking').addClass('connected').text('✓ Connected');
                                } else {
                                    statusButton.removeClass('connected checking').addClass('not-connected').text('✗ Offline');
                                }
                            },
                            error: function() {
                                statusButton.removeClass('connected checking').addClass('not-connected').text('✗ Error');
                            }
                        });
                    }, 1000); // Wait 1 second after user stops typing
                } else {
                    statusButton.removeClass('connected checking').addClass('not-connected').text('Unknown');
                }
            });
        }
        
        // Repeater for HLS Servers - Prevent duplicate events with debouncing
        var addServerTimeout;
        $('#vu-add-server').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $button = $(this);
            
            // Disable button temporarily
            if ($button.prop('disabled')) {
                return false;
            }
            
            $button.prop('disabled', true);
            
            // Debounce to prevent rapid clicks
            if (addServerTimeout) {
                clearTimeout(addServerTimeout);
            }
            
            addServerTimeout = setTimeout(function() {
                var serverIndex = $('#vu-server-fields .hls-server-item').length;
                $('.hls-empty-state').hide();
                var newField = `
                    <div class="hls-server-item" data-index="${serverIndex}">
                        <div class="hls-server-info">
                            <div class="hls-server-input">
                                <label>Server IP</label>
                                <input type="text" name="vu_servers[${serverIndex}][ip]" placeholder="Server IP Address" required />
                            </div>
                            <div class="hls-server-cdn">
                                <label>CDN Domains</label>
                                <select name="vu_servers[${serverIndex}][cdn][]" class="cdn-select"></select>
                            </div>
                        </div>
                        <div class="hls-server-actions">
                            <button type="button" class="hls-btn hls-btn-secondary vu-add-cdn">Add CDN</button>
                            <button type="button" class="hls-btn hls-btn-info vu-check-server">Check</button>
                            <button type="button" class="hls-server-status not-connected">Unknown</button>
                            <button type="button" class="hls-btn hls-btn-danger vu-remove-server">Remove</button>
                        </div>
                    </div>`;
                $('#vu-server-fields').append(newField);
                
                // Re-enable button
                $button.prop('disabled', false);
            }, 200);
        });

        // Repeater for Scraping Servers - Prevent duplicate events with debouncing
        var addScrapingServerTimeout;
        $('#vu-add-scraping-server').off('click').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $button = $(this);
            
            // Disable button temporarily
            if ($button.prop('disabled')) {
                return false;
            }
            
            $button.prop('disabled', true);
            
            // Debounce to prevent rapid clicks
            if (addScrapingServerTimeout) {
                clearTimeout(addScrapingServerTimeout);
            }
            
            addScrapingServerTimeout = setTimeout(function() {
                var serverIndex = $('#vu-scraping-server-fields .scraping-server-item').length;
                $('.hls-empty-state').hide();
                var newField = `
                    <div class="scraping-server-item" data-index="${serverIndex}">
                        <div class="scraping-server-info">
                            <div class="scraping-server-input">
                                <label>Server IP</label>
                                <input type="text" name="vu_scraping_servers[${serverIndex}][ip]" placeholder="*************" required />
                            </div>
                            <div class="scraping-server-input">
                                <label>Username</label>
                                <input type="text" name="vu_scraping_servers[${serverIndex}][username]" placeholder="admin" />
                            </div>
                            <div class="scraping-server-input">
                                <label>Password</label>
                                <input type="password" name="vu_scraping_servers[${serverIndex}][password]" placeholder="••••••••" />
                            </div>
                        </div>
                        <div class="scraping-server-actions">
                            <button type="button" class="hls-btn hls-btn-info scraping-test-connection">Test</button>
                            <button type="button" class="scraping-server-status not-connected">Unknown</button>
                            <button type="button" class="hls-btn hls-btn-danger vu-remove-scraping-server">Remove</button>
                        </div>
                    </div>`;
                $('#vu-scraping-server-fields').append(newField);
                
                // Re-enable button
                $button.prop('disabled', false);
            }, 200);
        });

        // Remove HLS Server
        $('#vu-server-fields').on('click', '.vu-remove-server', function() {
            $(this).closest('.hls-server-item').remove();
            if ($('#vu-server-fields .hls-server-item').length === 0) {
                $('.hls-empty-state').show();
            }
        });

        // Remove Scraping Server
        $('#vu-scraping-server-fields').on('click', '.vu-remove-scraping-server', function() {
            $(this).closest('.scraping-server-item').remove();
            if ($('#vu-scraping-server-fields .scraping-server-item').length === 0) {
                $('.hls-empty-state').show();
            }
        });

        // Add CDN Handler for new HLS design
        $(document).on('click', '.vu-add-cdn', function() {
            var select = $(this).closest('.hls-server-item').find('.cdn-select');
            var ip = $(this).closest('.hls-server-item').find('input[name*="[ip]"]').val();
            if (!ip) {
                showModernAlert('⚠️ Please enter Server IP first.', 'warning');
                return;
            }
            showCdnPopup(select, ip);
        });

        // Check Server Handler for new HLS design
        $(document).on('click', '.vu-check-server', function() {
            var $button = $(this);
            var serverItem = $button.closest('.hls-server-item');
            var ip = serverItem.find('input[name*="[ip]"]').val();
            var statusButton = serverItem.find('.hls-server-status');
            
            if (!ip) {
                showModernAlert('⚠️ Please enter Server IP first.', 'warning');
                return;
            }
            
            // Show checking state
            statusButton.removeClass('not-connected connected').addClass('checking').text('Checking...');
            $button.prop('disabled', true).text('Checking...');
            
            $.ajax({
                url: ajaxurl,
                method: 'POST',
                data: { 
                    action: 'check_vu_server_connection',
                    ip: ip 
                },
                success: function(response) {
                    if (response.success) {
                        statusButton.removeClass('not-connected checking').addClass('connected').text('✓ Connected');
                    } else {
                        statusButton.removeClass('connected checking').addClass('not-connected').text('✗ Offline');
                    }
                },
                error: function() {
                    statusButton.removeClass('connected checking').addClass('not-connected').text('✗ Error');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Check');
                }
            });
        });

        // Test Connection Handler for Scraping Servers
        $(document).on('click', '.scraping-test-connection', function() {
            var $button = $(this);
            var serverItem = $button.closest('.scraping-server-item');
            var ip = serverItem.find('input[name*="[ip]"]').val();
            var username = serverItem.find('input[name*="[username]"]').val();
            var password = serverItem.find('input[name*="[password]"]').val();
            var statusButton = serverItem.find('.scraping-server-status');
            
            if (!ip) {
                showModernAlert('⚠️ Please enter Server IP first.', 'warning');
                return;
            }
            
            // Show checking state
            statusButton.removeClass('not-connected connected').addClass('checking').text('Testing...');
            $button.prop('disabled', true).text('Testing...');
            
            // Simulate test connection (replace with actual AJAX call)
            setTimeout(function() {
                // For demo purposes, randomly succeed or fail
                var success = Math.random() > 0.3;
                
                if (success) {
                    statusButton.removeClass('not-connected checking').addClass('connected').text('✓ Connected');
                    showModernAlert('✅ Scraping server connection successful!', 'success');
                } else {
                    statusButton.removeClass('connected checking').addClass('not-connected').text('✗ Failed');
                    showModernAlert('❌ Failed to connect to scraping server.', 'error');
                }
                
                $button.prop('disabled', false).text('Test');
            }, 1500);
        });

        // CDN Popup Functions (ported from vu-script.js)
        function showCdnPopup(select, ip) {
            // Create modern popup if not exists
            if ($('#cdn-popup-modern').length === 0) {
                createModernCdnPopup();
            }
            
            var popup = $('#cdn-popup-modern');
            var cdnListContainer = $('#cdn-list-modern');
            $('#popup-ip-modern').text(ip);
            popup.fadeIn();
            
            // Clear and populate existing CDNs
            cdnListContainer.empty();
            var hasExistingCDN = false;
            select.find('option').each(function() {
                if ($(this).val() && $(this).val() !== 'add-new') {
                    cdnListContainer.append(
                        '<div class="cdn-item-modern">' + 
                            '<span>' + $(this).val() + '</span>' +
                            '<button type="button" class="hls-btn hls-btn-danger cdn-remove-btn">Remove</button>' +
                        '</div>'
                    );
                    hasExistingCDN = true;
                }
            });
            
            // Show empty state if no CDNs exist
            if (!hasExistingCDN) {
                cdnListContainer.append(
                    '<div class="cdn-empty-state">' +
                        '<div style="text-align: center; color: #9ca3af; padding: 20px;">' +
                            '<div style="font-size: 2rem; margin-bottom: 10px;">🌐</div>' +
                            '<p style="margin: 0; font-size: 0.9rem;">No CDN domains configured</p>' +
                            '<small style="color: #6b7280;">Add your first CDN domain below</small>' +
                        '</div>' +
                    '</div>'
                );
            }
        }

        function createModernCdnPopup() {
            var popupHTML = `
                <div id="cdn-popup-modern" class="cdn-popup-overlay" style="display: none;">
                    <div class="cdn-popup-content">
                        <div class="cdn-popup-header">
                            <h3>🌐 CDN Domains Management</h3>
                            <p>Server IP: <span id="popup-ip-modern"></span></p>
                        </div>
                        <div class="cdn-popup-body">
                            <div class="cdn-section">
                                <h4>Existing CDN Domains</h4>
                                <div id="cdn-list-modern" class="cdn-list-container"></div>
                            </div>
                            <div class="cdn-section">
                                <h4>Add New CDN Domain</h4>
                                <div class="cdn-input-group">
                                    <input type="text" id="new-cdn-input" placeholder="Enter CDN domain (e.g., cdn.example.com)" />
                                    <button type="button" id="add-cdn-btn" class="hls-btn hls-btn-secondary">Add Domain</button>
                                </div>
                            </div>
                        </div>
                        <div class="cdn-popup-footer">
                            <button type="button" id="save-cdn-modern" class="hls-btn hls-btn-primary">Save Changes</button>
                            <button type="button" id="cancel-cdn-modern" class="hls-btn hls-btn-secondary">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
            $('body').append(popupHTML);

            // Event handlers for popup
            $('#add-cdn-btn').on('click', function() {
                var domain = $('#new-cdn-input').val().trim();
                if (domain) {
                    // Validate domain format
                    if (!domain.match(/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)) {
                        showModernAlert('⚠️ Please enter a valid domain (e.g., cdn.example.com)', 'warning');
                        return;
                    }
                    
                    // Check for duplicates
                    var exists = false;
                    $('#cdn-list-modern .cdn-item-modern span').each(function() {
                        if ($(this).text().trim() === domain) {
                            exists = true;
                            return false;
                        }
                    });
                    
                    if (exists) {
                        showModernAlert('⚠️ This domain already exists!', 'warning');
                        return;
                    }
                    
                    $('#cdn-list-modern').append(
                        '<div class="cdn-item-modern">' + 
                            '<span>' + domain + '</span>' +
                            '<button type="button" class="hls-btn hls-btn-danger cdn-remove-btn">Remove</button>' +
                        '</div>'
                    );
                    $('#new-cdn-input').val('');
                } else {
                    showModernAlert('⚠️ Please enter a CDN domain', 'warning');
                }
            });
            
            // Allow Enter key to add domain
            $(document).on('keypress', '#new-cdn-input', function(e) {
                if (e.which === 13) {
                    $('#add-cdn-btn').click();
                }
            });

            $(document).on('click', '.cdn-remove-btn', function() {
                $(this).closest('.cdn-item-modern').remove();
            });

            $('#save-cdn-modern').on('click', function() {
                var cdnDomains = [];
                $('#cdn-list-modern .cdn-item-modern span').each(function() {
                    cdnDomains.push($(this).text().trim());
                });
                
                var ip = $('#popup-ip-modern').text();
                var select = findSelectByIP(ip);
                
                // Save to server
                $.post(ajaxurl, {
                    action: 'save_cdn_domains',
                    cdn_domains: cdnDomains,
                    server_ip: ip
                }, function(response) {
                    if (response.success) {
                        // Update select options
                        if (select) {
                            select.find('option').remove();
                            $.each(response.data, function(index, domain) {
                                select.append(new Option(domain, domain));
                            });
                        }
                        $('#cdn-popup-modern').fadeOut();
                        
                        showModernAlert('✅ CDN domains saved successfully!', 'success');
                    } else {
                        showModernAlert('❌ Error saving CDN domains: ' + (response.data || 'Unknown error'), 'error');
                    }
                });
            });

            $('#cancel-cdn-modern').on('click', function() {
                $('#cdn-popup-modern').fadeOut();
            });

            // Close on overlay click
            $('#cdn-popup-modern').on('click', function(e) {
                if (e.target === this) {
                    $(this).fadeOut();
                }
            });
        }

        function findSelectByIP(ip) {
            var foundSelect = null;
            $('.hls-server-item').each(function() {
                var inputIP = $(this).find('input[name*="[ip]"]').val();
                if (inputIP === ip) {
                    foundSelect = $(this).find('.cdn-select');
                    return false;
                }
            });
            return foundSelect;
        }

        function showModernAlert(message, type) {
            type = type || 'info';
            var colors = {
                'success': { bg: 'linear-gradient(135deg, #10b981 0%, #059669 100%)', shadow: 'rgba(16, 185, 129, 0.3)' },
                'error': { bg: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)', shadow: 'rgba(239, 68, 68, 0.3)' },
                'warning': { bg: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)', shadow: 'rgba(245, 158, 11, 0.3)' },
                'info': { bg: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)', shadow: 'rgba(59, 130, 246, 0.3)' }
            };
            
            var alertMsg = $('<div class="modern-alert-message">' + message + '</div>');
            alertMsg.css({
                'position': 'fixed',
                'top': '20px',
                'right': '20px',
                'background': colors[type].bg,
                'color': 'white',
                'padding': '16px 24px',
                'border-radius': '12px',
                'box-shadow': '0 8px 25px ' + colors[type].shadow,
                'z-index': '10001',
                'font-weight': '600',
                'font-size': '0.95rem',
                'max-width': '350px',
                'word-wrap': 'break-word'
            });
            $('body').append(alertMsg);
            
            setTimeout(function() {
                alertMsg.fadeOut(500, function() {
                    $(this).remove();
                });
            }, type === 'error' ? 5000 : 3000);
        }

        function checkAllServerStatuses() {
            var serverCount = $('.hls-server-item').length;
            var checkedCount = 0;
            
            if (serverCount === 0) {
                return;
            }
            
            // Show initial message
            showModernAlert('🔍 Checking ' + serverCount + ' server(s) status...', 'info');
            
            $('.hls-server-item').each(function() {
                var serverItem = $(this);
                var ip = serverItem.find('input[name*="[ip]"]').val();
                var statusButton = serverItem.find('.hls-server-status');
                
                if (ip && ip.trim() !== '') {
                    // Show checking state
                    statusButton.removeClass('not-connected connected').addClass('checking').text('Checking...');
                    
                    // Check server status
                    $.ajax({
                        url: ajaxurl,
                        method: 'POST',
                        data: { 
                            action: 'check_vu_server_connection',
                            ip: ip.trim()
                        },
                        success: function(response) {
                            if (response.success) {
                                statusButton.removeClass('not-connected checking').addClass('connected').text('✓ Connected');
                            } else {
                                statusButton.removeClass('connected checking').addClass('not-connected').text('✗ Offline');
                            }
                        },
                        error: function() {
                            statusButton.removeClass('connected checking').addClass('not-connected').text('✗ Error');
                        },
                        complete: function() {
                            checkedCount++;
                            if (checkedCount === serverCount) {
                                var connectedCount = $('.hls-server-status.connected').length;
                                var offlineCount = $('.hls-server-status.not-connected').length;
                                showModernAlert('✅ Status check complete: ' + connectedCount + ' online, ' + (offlineCount - connectedCount) + ' offline', 'success');
                            }
                        }
                    });
                } else {
                    // No IP, show unknown status
                    statusButton.removeClass('connected checking').addClass('not-connected').text('Unknown');
                    checkedCount++;
                    if (checkedCount === serverCount) {
                        var connectedCount = $('.hls-server-status.connected').length;
                        showModernAlert('✅ Status check complete: ' + connectedCount + ' server(s) configured', 'info');
                    }
                }
            });
        }

        $('#vu-settings-form').on('submit', function(e) {
            e.preventDefault();
            
            // Show loading state
            var submitButton = $(this).find('#vu-save-settings');
            var originalText = submitButton.text();
            submitButton.text('Saving...').prop('disabled', true);
            
            var formData = $(this).serialize();
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: formData + '&action=save_vu_server_settings',
                timeout: 30000,
                success: function(response) {
                    if (response && response.success) {
                        $('#vu-status-message')
                            .removeClass('notice-error')
                            .addClass('notice-success')
                            .html('<p>✅ Settings saved successfully!</p>')
                            .show();
                    } else {
                        var errorMsg = response && response.data ? response.data : 'Unknown error occurred';
                        $('#vu-status-message')
                            .removeClass('notice-success')
                            .addClass('notice-error')
                            .html('<p>❌ Error saving settings: ' + errorMsg + '</p>')
                            .show();
                    }
                    
                    setTimeout(function() {
                        $('#vu-status-message').fadeOut();
                    }, 4000);
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', xhr, status, error);
                    $('#vu-status-message')
                        .removeClass('notice-success')
                        .addClass('notice-error')
                        .html('<p>❌ Network error saving settings. Please check your connection and try again.</p>')
                        .show();
                    
                    setTimeout(function() {
                        $('#vu-status-message').fadeOut();
                    }, 4000);
                },
                complete: function() {
                    // Reset button state
                    submitButton.text(originalText).prop('disabled', false);
                }
            });
        });
    });
    </script>
    <div id="cdn-popup">
        <div id="cdn-popup-header">
            <h2>Add CDN Domain for <span id="popup-ip"></span></h2>
            <div id="cdn-popup-actions">
                <button type="button" id="cdn-export-btn">Export</button>
                <button type="button" id="cdn-import-btn">Import</button>
                <button type="button" id="cdn-clear-btn">Clear All</button>
                <input type="file" id="cdn-import-file" accept=".json,application/json" style="display:none;">
            </div>
        </div>
        <div id="cdn-list"></div>
        <div class="repeater-container">
            <button type="button" id="add-cdn-field">Add</button>
        </div>
        <div class="form-actions">
            <button type="button" id="save-cdn-domain">Save</button>
            <button type="button" id="cancel-cdn-domain">Cancel</button>
        </div>
    </div>
<?php
}

function vu_save_server_settings() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized');
        exit;
    }
    $old_servers = get_option('vu_servers', []);
    $servers = isset($_POST['vu_servers']) ? $_POST['vu_servers'] : [];
    foreach ($old_servers as $i => $old_server) {
        if (!isset($servers[$i])) {
            delete_option("vu_server_cdn_$i");
        } else {
            if ($servers[$i]['ip'] !== $old_server['ip']) {
                delete_option("vu_server_cdn_$i");
            }
        }
    }
    update_option('vu_servers', $servers);
    if (isset($_POST['vu_api_key'])) {
        update_option('vu_api_key', sanitize_text_field($_POST['vu_api_key']));
    }
    if (isset($_POST['vu_post_types'])) {
        update_option('vu_post_types', array_map('sanitize_text_field', $_POST['vu_post_types']));
    }
    if (isset($_POST['vu_access_token'])) {
        update_option('vu_access_token', wp_unslash($_POST['vu_access_token']));
    }
    if (isset($_POST['vu_client_id'])) {
        update_option('vu_client_id', sanitize_text_field($_POST['vu_client_id']));
    }
    if (isset($_POST['vu_client_secret'])) {
        update_option('vu_client_secret', sanitize_text_field($_POST['vu_client_secret']));
    }
    if (isset($_POST['cf_email'])) {
        update_option('cf_email', sanitize_email($_POST['cf_email']));
    }
    if (isset($_POST['cf_api_key'])) {
        update_option('cf_api_key', sanitize_text_field($_POST['cf_api_key']));
    }
    if (isset($_POST['cf_zone_id'])) {
        update_option('cf_zone_id', sanitize_text_field($_POST['cf_zone_id']));
    }
    if (isset($_POST['vu_master_ip'])) {
        update_option('vu_master_ip', sanitize_text_field($_POST['vu_master_ip']));
    }
    update_option('vu_hls_mode', isset($_POST['vu_hls_mode_value']) && $_POST['vu_hls_mode_value'] === '1');
    update_option('vu_scraping_mode', isset($_POST['vu_scraping_mode_value']) && $_POST['vu_scraping_mode_value'] === '1');

    if (isset($_POST['vu_scraping_servers'])) {
        $scraping_servers = [];
        foreach ($_POST['vu_scraping_servers'] as $server_data) {
            if (empty($server_data['ip'])) continue;
            $scraping_servers[] = [
                'ip'       => sanitize_text_field($server_data['ip']),
                'username' => sanitize_text_field($server_data['username']),
                'password' => sanitize_text_field($server_data['password']),
            ];
        }
        update_option('vu_scraping_servers', $scraping_servers);
    } else {
        update_option('vu_scraping_servers', []);
    }

    $api_key = get_option('vu_api_key', '');
    $access_token_json = get_option('vu_access_token', '');
    $client_id = get_option('vu_client_id', '');
    $client_secret = get_option('vu_client_secret', '');
    $access_token_data = json_decode($access_token_json, true);
    $access_token = isset($access_token_data['access_token']) ? sanitize_text_field($access_token_data['access_token']) : '';
    $refresh_token = isset($access_token_data['refresh_token']) ? sanitize_text_field($access_token_data['refresh_token']) : '';
    $master_ip = get_option('vu_master_ip', '');
    $response_data = [];
    foreach ($servers as $index => $server) {
        if (!isset($server['cdn']) || !is_array($server['cdn'])) {
            $response_data[$server['ip']] = 'Failed: CDN data is missing or not an array.';
            continue;
        }
        $existing_cdn_domains = get_option("vu_server_cdn_$index", []);
        $cdn_domains = array_merge($existing_cdn_domains, array_map('sanitize_text_field', $server['cdn']));
        $cdn_domains = array_unique($cdn_domains);
        update_option("vu_server_cdn_$index", $cdn_domains);
        $new_api_url = esc_url(home_url('/')) . "wp-json/vu_plugin/v1/update-status";
        $configContent = generate_config_content($api_key, $client_id, $client_secret, $refresh_token, $access_token, $cdn_domains, $new_api_url, $master_ip);
        $credentialsContent = $access_token_json;
        $master_server = ['ip_address' => $master_ip];
        $data = [
            'configContent' => $configContent,
            'credentialsContent' => $credentialsContent,
            'master_server' => $master_server,
        ];
        $server_ip = sanitize_text_field($server['ip']);
        if (filter_var($server_ip, FILTER_VALIDATE_IP)) {
            $url = "http://" . $server_ip . "/api/saveConfig.php";
        } else {
            $response_data[$server['ip']] = 'Failed: Invalid IP address.';
            continue;
        }
        $response = wp_remote_post($url, [
            'method'    => 'POST',
            'body'      => json_encode($data),
            'headers'   => [
                'API-Key' => $api_key,
                'Content-Type' => 'application/json',
            ],
            'timeout' => 10,
        ]);
        if (is_wp_error($response)) {
            $response_data[$server['ip']] = 'Failed: ' . $response->get_error_message();
        } else {
            $body = wp_remote_retrieve_body($response);
            $result = json_decode($body, true);
            if (isset($result['status']) && $result['status'] === 'success') {
                $response_data[$server['ip']] = 'Success';
            } else {
                $message = isset($result['message']) ? $result['message'] : 'Unknown error.';
                $response_data[$server['ip']] = 'Failed: ' . $message;
            }
        }
    }
    wp_send_json_success($response_data);
    exit;
}

function generate_config_content($api_key, $client_id, $client_secret, $refresh_token, $access_token, $cdn_domains, $new_api_url, $master_ip) {
    $configContent = "<?php\n";
    $configContent .= "\$phpSettings = array(\n";
    $configContent .= "\"max_execution_time\"    => \"0\",\n";
    $configContent .= "\"memory_limit\"          => \"-1\",\n";
    $configContent .= "\"max_input_time\"        => \"-1\",\n";
    $configContent .= "\"default_socket_timeout\"=> \"1300\",\n";
    $configContent .= "\"implicit_flush\"        => \"On\",\n";
    $configContent .= "\"display_errors\"        => \"1\",\n";
    $configContent .= "\"display_startup_errors\"=> \"1\",\n";
    $configContent .= "\"log_errors\"            => \"1\",\n";
    $configContent .= "\"error_log\"             => \"/var/log/php_errors.log\"\n";
    $configContent .= ");\n";
    $configContent .= "foreach (\$phpSettings as \$k => \$v) {\n";
    $configContent .= "@ini_set(\$k, \$v);\n";
    $configContent .= "}\n";
    $configContent .= "return [\n";
    $configContent .= "'google_drive_credentials' => '/home/<USER>/credentials.json',\n";
    $configContent .= "'hls_save_path' => '/home/<USER>/cdn/down/',\n";
    $configContent .= "'queue_path' => '/home/<USER>/status/queue/',\n";
    $configContent .= "'done_path' => '/home/<USER>/status/done/',\n";
    $configContent .= "'process_path' => '/home/<USER>/status/process/',\n";
    $configContent .= "'failed_path' => '/home/<USER>/status/failed/',\n";
    $configContent .= "'retry_path' => '/home/<USER>/status/retry/',\n";
    $configContent .= "'master_save_path' => '/home/<USER>/cdn/master/',\n";
    $configContent .= "'master_server' => ['ip_address' => '{$master_ip}'],\n";
    $configContent .= "'wordpress_api_url' => '{$new_api_url}',\n";
    $configContent .= "'api_key' => '{$api_key}',\n";
    $configContent .= "'cdn_domains' => " . json_encode($cdn_domains) . ",\n";
    $configContent .= "'client_id' => '{$client_id}',\n";
    $configContent .= "'client_secret' => '{$client_secret}',\n";
    $configContent .= "'refresh_token' => '{$refresh_token}',\n";
    $configContent .= "'access_token' => '{$access_token}'\n";
    $configContent .= "];\n";
    return $configContent;
}

add_action('wp_ajax_save_vu_server_settings', 'vu_save_server_settings');
add_action('wp_ajax_nopriv_save_vu_server_settings', 'vu_save_server_settings');
add_action('admin_post_save_vu_server_settings', 'vu_save_server_settings');

function check_vu_server_connection() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized');
    }
    $ip = sanitize_text_field($_POST['ip']);
    $connected = false;
    $connection = @fsockopen($ip, 80, $errno, $errstr, 2);
    if ($connection) {
        $connected = true;
        fclose($connection);
    }
    if ($connected) {
        wp_send_json_success(['connected' => true]);
    } else {
        wp_send_json_error(['connected' => false]);
    }
}
add_action('wp_ajax_check_vu_server_connection', 'check_vu_server_connection');

function clear_cdn_domains() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized');
    }
    if (!isset($_POST['server_ip'])) {
        wp_send_json_error('Missing Server IP');
    }
    $server_ip = sanitize_text_field($_POST['server_ip']);
    $servers = get_option('vu_servers', []);
    $server_index = array_search($server_ip, array_column($servers, 'ip'));
    if ($server_index === false) {
        wp_send_json_error('Invalid Server IP');
    }
    update_option("vu_server_cdn_$server_index", []);
    wp_send_json_success([
        'message' => "All CDN cleared for $server_ip",
        'server_ip' => $server_ip
    ]);
}
add_action('wp_ajax_clear_cdn_domains', 'clear_cdn_domains');

function get_post_id_by_video_id($video_id) {
    global $wpdb;
    $meta_key_like = 'video_id%';
    $query = $wpdb->prepare("SELECT post_id, meta_key FROM $wpdb->postmeta WHERE meta_value = %s AND meta_key LIKE %s", $video_id, $meta_key_like);
    $results = $wpdb->get_results($query);
    if ($results) {
        foreach ($results as $result) {
            $post_id = $result->post_id;
            $meta_key = $result->meta_key;
            if (strpos($meta_key, 'video_id_episode_dubbed_') !== false) {
                preg_match('/_(\d+)$/', $meta_key, $matches);
                if (isset($matches[1])) {
                    $index = (int) $matches[1];
                    return ['post_id' => $post_id, 'index' => $index, 'type' => 'dubbed_episode'];
                }
            } elseif (strpos($meta_key, 'video_id_episode_subbed_') !== false) {
                preg_match('/_(\d+)$/', $meta_key, $matches);
                if (isset($matches[1])) {
                    $index = (int) $matches[1];
                    return ['post_id' => $post_id, 'index' => $index, 'type' => 'subbed_episode'];
                }
            } elseif (strpos($meta_key, 'video_id_dubbed') !== false) {
                return ['post_id' => $post_id, 'type' => 'dubbed'];
            } elseif (strpos($meta_key, 'video_id_subbed') !== false) {
                return ['post_id' => $post_id, 'type' => 'subbed'];
            } elseif (strpos($meta_key, 'video_id_adult') !== false) {
                return ['post_id' => $post_id, 'type' => 'adult'];
            }
        }
    }
    return false;
}

function vu_update_video_status() {
    $params = json_decode(file_get_contents('php://input'), true);
    if (!isset($params['video_id']) || !isset($params['status'])) {
        wp_send_json_error('Invalid parameters');
    }
    $video_id = sanitize_text_field($params['video_id']);
    $status = sanitize_text_field($params['status']);
    $video_url = isset($params['video_url']) ? esc_url($params['video_url']) : null;
    $post_data = get_post_id_by_video_id($video_id);
    if ($post_data) {
        $post_id = $post_data['post_id'];
        $type = $post_data['type'];
        $meta_key_status = '';
        $meta_key_url = '';
        if ($type === 'dubbed_episode') {
            $index = $post_data['index'];
            $meta_key_status = "video_status_episode_dubbed_$index";
            $meta_key_url = "video_url_episode_dubbed_$index";
        } elseif ($type === 'subbed_episode') {
            $index = $post_data['index'];
            $meta_key_status = "video_status_episode_subbed_$index";
            $meta_key_url = "video_url_episode_subbed_$index";
        } elseif ($type === 'dubbed') {
            $meta_key_status = "video_status_dubbed";
            $meta_key_url = "video_url_dubbed";
        } elseif ($type === 'subbed') {
            $meta_key_status = "video_status_subbed";
            $meta_key_url = "video_url_subbed";
        } elseif ($type === 'adult') {
            $meta_key_status = "video_status_adult";
            $meta_key_url = "video_url_adult";
        }
        update_post_meta($post_id, $meta_key_status, $status);
        if ($video_url) {
            update_post_meta($post_id, $meta_key_url, $video_url);
        }
        if (strtolower($status) === 'completed') {
            purge_cloudflare_cache(get_permalink($post_id));
        }
        wp_send_json_success('Status updated successfully');
    } else {
        wp_send_json_error('Video ID not found');
    }
}

add_action('rest_api_init', function () {
    register_rest_route('vu_plugin/v1', '/update-status', array(
        'methods' => 'POST',
        'callback' => 'vu_update_video_status',
    ));
});

add_action('rest_api_init', function () {
    register_rest_route('video/v1', '/url/', array(
        'methods' => 'GET',
        'callback' => 'get_video_url',
    ));
});

function check_txt_cache($video_id) {
    $upload_dir = wp_upload_dir();
    $cache_dir = $upload_dir['basedir'] . '/CacheStream';
    if (!file_exists($cache_dir)) {
        wp_mkdir_p($cache_dir);
    }
    $file_path = $cache_dir . "/video_url_{$video_id}.txt";
    if (file_exists($file_path)) {
        $cached_data = file_get_contents($file_path);
        return json_decode($cached_data, true);
    }
    return false;
}

function save_txt_cache($video_id, $data) {
    $upload_dir = wp_upload_dir();
    $cache_dir = $upload_dir['basedir'] . '/CacheStream';
    if (!file_exists($cache_dir)) {
        wp_mkdir_p($cache_dir);
    }
    $file_path = $cache_dir . "/video_url_{$video_id}.txt";
    file_put_contents($file_path, json_encode($data));
}

function get_video_data_from_db($video_id) {
    global $wpdb;
    $query = $wpdb->prepare("SELECT post_id, meta_key FROM {$wpdb->postmeta} WHERE meta_value = %s", $video_id);
    $results = $wpdb->get_results($query);
    return $results ? $results[0] : false;
}

function generate_video_url($post_id, $meta_key_found) {
    $video_url = '';
    $server_ip = '';
    $video_id = get_post_meta($post_id, $meta_key_found, true);
    if (strpos($meta_key_found, 'dubbed') !== false) {
        if ($meta_key_found === 'video_id_dubbed') {
            $video_url = get_post_meta($post_id, 'video_url_dubbed', true);
            $server_ip = get_post_meta($post_id, 'server_ip_dubbed', true);
        } else {
            $index = str_replace('video_id_episode_dubbed_', '', $meta_key_found);
            $video_url = get_post_meta($post_id, 'video_url_episode_dubbed_' . $index, true);
            $server_ip = get_post_meta($post_id, 'server_ip_episode_dubbed_' . $index, true);
        }
    } elseif (strpos($meta_key_found, 'subbed') !== false) {
        if ($meta_key_found === 'video_id_subbed') {
            $video_url = get_post_meta($post_id, 'video_url_subbed', true);
            $server_ip = get_post_meta($post_id, 'server_ip_subbed', true);
        } else {
            $index = str_replace('video_id_episode_subbed_', '', $meta_key_found);
            $video_url = get_post_meta($post_id, 'video_url_episode_subbed_' . $index, true);
            $server_ip = get_post_meta($post_id, 'server_ip_episode_subbed_' . $index, true);
        }
    } elseif ($meta_key_found === 'video_id_adult') {
        $video_url = get_post_meta($post_id, 'video_url_adult', true);
        $server_ip = get_post_meta($post_id, 'server_ip_adult', true);
    }
    if (!$server_ip) {
        return new WP_Error('no_server_ip', 'Server IP not found', array('status' => 404));
    }
    $site_url = get_site_url();
    $parsed_url = parse_url($site_url);
    $domain = preg_replace('/^www\./', '', $parsed_url['host']);
    $servers = get_option('vu_servers', []);
    $server_index = array_search($server_ip, array_column($servers, 'ip'));
    if ($server_index === false) {
        return new WP_Error('invalid_server_ip', 'Invalid Server IP', array('status' => 404));
    }
    $stream_number = $server_index + 1;
    return "https://stream{$stream_number}." . $domain . "/cdn/master/" . $video_id . "/master.m3u8";
}

function get_video_url(WP_REST_Request $request) {
    $video_id = $request->get_param('id');
    if (!$video_id) {
        return new WP_Error('no_video_id', 'Invalid video ID', array('status' => 400));
    }
    $cached_data = check_txt_cache($video_id);
    if ($cached_data) {
        return rest_ensure_response($cached_data);
    }
    $video_data = get_video_data_from_db($video_id);
    if (!$video_data) {
        return new WP_Error('no_video_found', 'Video not found', array('status' => 404));
    }
    $post_id = $video_data->post_id;
    $meta_key_found = $video_data->meta_key;
    $cdn_video_url = generate_video_url($post_id, $meta_key_found);
    if (is_wp_error($cdn_video_url)) {
        return $cdn_video_url;
    }
    $response_data = ['url' => $cdn_video_url];
    save_txt_cache($video_id, $response_data);
    return rest_ensure_response($response_data);
}

function save_cdn_domains() {
    if (!isset($_POST['cdn_domains']) || !isset($_POST['server_ip'])) {
        wp_send_json_error('Missing CDN domains or Server IP');
    }
    $cdn_domains = array_map('sanitize_text_field', $_POST['cdn_domains']);
    $cdn_domains = array_unique($cdn_domains);
    $server_ip = sanitize_text_field($_POST['server_ip']);
    $servers = get_option('vu_servers', []);
    $server_index = array_search($server_ip, array_column($servers, 'ip'));
    if ($server_index === false) {
        $server_index = count($servers);
        $servers[] = ['ip' => $server_ip, 'cdn' => []];
        update_option('vu_servers', $servers);
    }
    $existing_cdn = get_option("vu_server_cdn_$server_index", []);
    $merged_cdn = array_merge($existing_cdn, $cdn_domains);
    $merged_cdn = array_unique($merged_cdn);
    update_option("vu_server_cdn_$server_index", $merged_cdn);
    wp_send_json_success([
        'message' => 'CDN domains saved successfully for Server IP: ' . $server_ip,
        'data' => $merged_cdn
    ]);
}
add_action('wp_ajax_save_cdn_domains', 'save_cdn_domains');
add_action('wp_ajax_nopriv_save_cdn_domains', 'save_cdn_domains');

function export_cdn_domains() {
    if (!isset($_POST['server_ip'])) {
        wp_send_json_error('Missing Server IP');
    }
    $server_ip = sanitize_text_field($_POST['server_ip']);
    $servers = get_option('vu_servers', []);
    $server_index = array_search($server_ip, array_column($servers, 'ip'));
    if ($server_index === false) {
        wp_send_json_error('Invalid Server IP');
    }
    $cdn_list = get_option("vu_server_cdn_$server_index", []);
    wp_send_json_success($cdn_list);
}
add_action('wp_ajax_export_cdn_domains', 'export_cdn_domains');

function import_cdn_domains() {
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized');
    }
    if (!isset($_POST['server_ip']) || !isset($_POST['cdn_json'])) {
        wp_send_json_error('Missing Server IP or CDN JSON');
    }
    $server_ip = sanitize_text_field($_POST['server_ip']);
    $cdn_json = wp_unslash($_POST['cdn_json']);
    $decoded = json_decode($cdn_json, true);
    if (!is_array($decoded)) {
        wp_send_json_error('Invalid JSON format');
    }
    $servers = get_option('vu_servers', []);
    $server_index = array_search($server_ip, array_column($servers, 'ip'));
    if ($server_index === false) {
        $server_index = count($servers);
        $servers[] = ['ip' => $server_ip, 'cdn' => []];
        update_option('vu_servers', $servers);
    }
    $old_cdn = get_option("vu_server_cdn_$server_index", []);
    $new_cdn = array_map('sanitize_text_field', $decoded);
    $merged_cdn = array_merge($old_cdn, $new_cdn);
    $merged_cdn = array_unique($merged_cdn);
    update_option("vu_server_cdn_$server_index", $merged_cdn);
    wp_send_json_success([
        'message' => 'Imported CDN domains for ' . $server_ip,
        'server_ip' => $server_ip,
        'cdn_list' => $merged_cdn
    ]);
}
add_action('wp_ajax_import_cdn_domains', 'import_cdn_domains');

function purge_cloudflare_cache($input) {
    if (empty($input)) {
        return false;
    }
    $url_to_purge = is_numeric($input) ? get_permalink($input) : $input;
    if (!$url_to_purge) {
        return false;
    }
    if (preg_match('/\/page\/\d+/', $url_to_purge)) {
        return false;
    }
    $email = get_option('cf_email');
    $api_key = get_option('cf_api_key');
    $zone_id = get_option('cf_zone_id');
    $response = wp_remote_post('https://api.cloudflare.com/client/v4/zones/' . $zone_id . '/purge_cache', [
        'headers' => [
            'X-Auth-Email' => $email,
            'X-Auth-Key'   => $api_key,
            'Content-Type' => 'application/json',
        ],
        'body' => json_encode(['files' => [$url_to_purge]]),
    ]);
    if (is_wp_error($response)) {
        return false;
    }
    $body = wp_remote_retrieve_body($response);
    $decoded_response = json_decode($body, true);
    if (isset($decoded_response['success']) && $decoded_response['success'] === true) {
        return $decoded_response;
    }
    return false;
}

add_action('pre_post_update', 'capture_old_post_url', 10, 2);
function capture_old_post_url($post_id, $data) {
    $old_post_url = get_permalink($post_id);
    update_post_meta($post_id, '_old_post_url', $old_post_url);
}

function purge_post_and_page_cloudflare_cache($post_id) {
    if (wp_is_post_revision($post_id) || get_post_status($post_id) === 'auto-draft') {
        return;
    }
    $home_url = home_url('/');
    if ($home_url) {
        purge_cloudflare_cache($home_url);
    }
    $old_post_url = get_post_meta($post_id, '_old_post_url', true);
    if ($old_post_url) {
        purge_cloudflare_cache($old_post_url);
        delete_post_meta($post_id, '_old_post_url');
    }
    $post_url = get_permalink($post_id);
    if ($post_url) {
        purge_cloudflare_cache($post_url);
    }
    $pages = get_pages();
    foreach ($pages as $page) {
        $page_url = get_permalink($page->ID);
        purge_cloudflare_cache($page_url);
    }
}
add_action('save_post', 'purge_post_and_page_cloudflare_cache');

add_action('init', 'add_custom_fields_support_to_post_types');
function add_custom_fields_support_to_post_types() {
    $post_types = ['movie', 'serie', 'anime', 'adult'];
    foreach ($post_types as $post_type) {
        add_post_type_support($post_type, 'custom-fields');
    }
}

function vu_add_export_menu_page() {
    add_submenu_page(
        'video-list',
        'Export Video Data',
        'Export Videos',
        'manage_options',
        'vu-export-video-data',
        'vu_export_video_data_page',
        99
    );
}
add_action('admin_menu','vu_add_export_menu_page',99);

function vu_export_video_data_page() {
    ?>
    <div class="wrap">
        <h1>Export Video Data</h1>
        <p>เลือกประเภทโพสต์ที่ต้องการส่งออกข้อมูลในรูปแบบ JSON.</p>
        <form method="post">
            <?php wp_nonce_field('vu_export_video_data_action','vu_export_video_data_nonce'); ?>
            <input type="hidden" name="vu_export_video_data" value="1">
            <button type="submit" name="post_type" value="movie" class="button button-primary">Export Movies</button>
            <button type="submit" name="post_type" value="serie" class="button button-primary">Export Series</button>
            <button type="submit" name="post_type" value="anime" class="button button-primary">Export Anime</button>
            <button type="submit" name="post_type" value="adult" class="button button-primary">Export Adult</button>
        </form>
    </div>
    <?php
    if (isset($_POST['vu_export_video_data'])) {
        if (!isset($_POST['vu_export_video_data_nonce']) || !wp_verify_nonce($_POST['vu_export_video_data_nonce'],'vu_export_video_data_action')) {
            echo '<div class="notice notice-error"><p>Invalid request.</p></div>';
            return;
        }
        $post_type = sanitize_text_field($_POST['post_type']);
        vu_generate_video_json($post_type);
    }
}

function vu_generate_video_json($post_type) {
    $allowed_post_types = ['movie','serie','anime','adult'];
    if (!in_array($post_type,$allowed_post_types)) {
        echo '<div class="notice notice-error"><p>Invalid post type.</p></div>';
        return;
    }
    $export_data = [];
    $args = [
        'post_type' => $post_type,
        'posts_per_page' => -1
    ];
    $query = new WP_Query($args);
    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            $post_id = get_the_ID();
            $post_title = get_the_title();
            if ($post_type === 'movie') {
                $gdrive_dubbed = get_post_meta($post_id,'gdrivedubbed',true);
                $video_id_dubbed = is_string($gdrive_dubbed) ? md5($gdrive_dubbed) : '';
                $export_data[] = [
                    'ID' => $post_id,
                    'Title' => $post_title.' (พากย์ไทย)',
                    'Type' => $post_type,
                    'GDrive Link' => $gdrive_dubbed,
                    'Server IP' => get_post_meta($post_id,'server_ip_dubbed',true),
                    'Status' => get_post_meta($post_id,'video_status_dubbed',true),
                    'Video ID' => $video_id_dubbed,
                    'Video URL' => $video_id_dubbed ? '/embed/'.$video_id_dubbed.'/' : ''
                ];
                $gdrive_subbed = get_post_meta($post_id,'gdrivesubbed',true);
                $video_id_subbed = is_string($gdrive_subbed) ? md5($gdrive_subbed) : '';
                $export_data[] = [
                    'ID' => $post_id,
                    'Title' => $post_title.' (ซับไทย)',
                    'Type' => $post_type,
                    'GDrive Link' => $gdrive_subbed,
                    'Server IP' => get_post_meta($post_id,'server_ip_subbed',true),
                    'Status' => get_post_meta($post_id,'video_status_subbed',true),
                    'Video ID' => $video_id_subbed,
                    'Video URL' => $video_id_subbed ? '/embed/'.$video_id_subbed.'/' : ''
                ];
            } elseif ($post_type === 'serie' || $post_type === 'anime') {
                $serie_dubbed_episodes = get_post_meta($post_id,'serie_dubbed_episodes',true);
                $serie_subbed_episodes = get_post_meta($post_id,'serie_subbed_episodes',true);
                $start_episode_number = get_post_meta($post_id,'serie_start_episode_number',true);
                $start_episode_number = is_numeric($start_episode_number) ? intval($start_episode_number) : 0;
                if (is_array($serie_dubbed_episodes)) {
                    foreach ($serie_dubbed_episodes as $index => $episode_gdrive) {
                        $video_id = is_string($episode_gdrive) ? md5($episode_gdrive) : '';
                        $export_data[] = [
                            'ID' => $post_id,
                            'Title' => $post_title.' - Episode '.($start_episode_number + $index + 1).' (พากย์ไทย)',
                            'Type' => $post_type,
                            'GDrive Link' => $episode_gdrive,
                            'Server IP' => get_post_meta($post_id,'server_ip_episode_dubbed_'.$index,true),
                            'Status' => get_post_meta($post_id,'video_status_episode_dubbed_'.$index,true),
                            'Video ID' => $video_id,
                            'Video URL' => $video_id ? '/embed/'.$video_id.'/' : ''
                        ];
                    }
                }
                if (is_array($serie_subbed_episodes)) {
                    foreach ($serie_subbed_episodes as $index => $episode_gdrive) {
                        $video_id = is_string($episode_gdrive) ? md5($episode_gdrive) : '';
                        $export_data[] = [
                            'ID' => $post_id,
                            'Title' => $post_title.' - Episode '.($start_episode_number + $index + 1).' (ซับไทย)',
                            'Type' => $post_type,
                            'GDrive Link' => $episode_gdrive,
                            'Server IP' => get_post_meta($post_id,'server_ip_episode_subbed_'.$index,true),
                            'Status' => get_post_meta($post_id,'video_status_episode_subbed_'.$index,true),
                            'Video ID' => $video_id,
                            'Video URL' => $video_id ? '/embed/'.$video_id.'/' : ''
                        ];
                    }
                }
            } elseif ($post_type === 'adult') {
                $adult_drive_link = get_post_meta($post_id,'adult_drive_link',true);
                $video_id_adult = is_string($adult_drive_link) ? md5($adult_drive_link) : '';
                $export_data[] = [
                    'ID' => $post_id,
                    'Title' => $post_title,
                    'Type' => $post_type,
                    'GDrive Link' => $adult_drive_link,
                    'Server IP' => get_post_meta($post_id,'server_ip_adult',true),
                    'Status' => get_post_meta($post_id,'video_status_adult',true),
                    'Video ID' => $video_id_adult,
                    'Video URL' => $video_id_adult ? '/embed/'.$video_id_adult.'/' : ''
                ];
            }
        }
        wp_reset_postdata();
    }
    if (empty($export_data)) {
        $export_data = ['message'=>'No data found'];
    }
    $json_data = json_encode($export_data,JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT);
    if (json_last_error() !== JSON_ERROR_NONE) {
        $error = ['error'=>'JSON Encode Error: '.json_last_error_msg()];
        $json_data = json_encode($error,JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT);
    }
    if (ob_get_contents()) {
        ob_end_clean();
    }
    $filename = $post_type.'-data.json';
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="'.$filename.'"');
    header('Pragma: no-cache');
    header('Expires: 0');
    echo $json_data;
    exit;
}

add_action('admin_menu', 'vu_add_admin_menu');

function vu_add_admin_menu() {
    add_options_page('Video Uploader Settings', 'Video Uploader', 'manage_options', 'video-uploader-settings', 'vu_settings_page');
    add_options_page('Movie Scraper Settings', 'Movie Scraper', 'manage_options', 'movie-scraper-settings', 'movie_scraper_settings_page');
}

function movie_scraper_settings_page() {
    if (isset($_POST['submit'])) {
        $settings = array(
            'use_downloaded_files' => isset($_POST['use_downloaded_files']) ? 1 : 0,
            'auto_fill_youtube' => isset($_POST['auto_fill_youtube']) ? 1 : 0,
            'auto_fill_duration' => isset($_POST['auto_fill_duration']) ? 1 : 0,
            'auto_fill_imdb' => isset($_POST['auto_fill_imdb']) ? 1 : 0,
            'auto_download_poster' => isset($_POST['auto_download_poster']) ? 1 : 0,
            'auto_fill_m3u8' => isset($_POST['auto_fill_m3u8']) ? 1 : 0,
        );
        
        update_option('movie_scraper_settings', $settings);
        echo '<div class="notice notice-success"><p>✅ Settings saved successfully!</p></div>';
    }
    
    $settings = get_option('movie_scraper_settings', array());
    $use_downloaded_files = isset($settings['use_downloaded_files']) ? $settings['use_downloaded_files'] : 1;
    $auto_fill_youtube = isset($settings['auto_fill_youtube']) ? $settings['auto_fill_youtube'] : 1;
    $auto_fill_duration = isset($settings['auto_fill_duration']) ? $settings['auto_fill_duration'] : 1;
    $auto_fill_imdb = isset($settings['auto_fill_imdb']) ? $settings['auto_fill_imdb'] : 1;
    $auto_download_poster = isset($settings['auto_download_poster']) ? $settings['auto_download_poster'] : 1;
    $auto_fill_m3u8 = isset($settings['auto_fill_m3u8']) ? $settings['auto_fill_m3u8'] : 1;
    ?>
    
    <div class="wrap">
        <style>
            .movie-scraper-settings {
                max-width: 1200px;
                margin: 20px 0;
            }
            .settings-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 30px;
                border-radius: 10px;
                margin-bottom: 30px;
                text-align: center;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .settings-header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 300;
            }
            .settings-header p {
                margin: 10px 0 0 0;
                opacity: 0.9;
                font-size: 16px;
            }
            .settings-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin-bottom: 30px;
            }
            .settings-card {
                background: white;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                padding: 25px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                transition: transform 0.2s ease;
            }
            .settings-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
            .settings-card h3 {
                margin: 0 0 15px 0;
                color: #2c3e50;
                font-size: 18px;
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .settings-card p {
                margin: 0 0 20px 0;
                color: #7f8c8d;
                line-height: 1.6;
            }
            .toggle-switch {
                position: relative;
                display: inline-block;
                width: 60px;
                height: 34px;
            }
            .toggle-switch input {
                opacity: 0;
                width: 0;
                height: 0;
            }
            .slider {
                position: absolute;
                cursor: pointer;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #ccc;
                transition: .4s;
                border-radius: 34px;
            }
            .slider:before {
                position: absolute;
                content: "";
                height: 26px;
                width: 26px;
                left: 4px;
                bottom: 4px;
                background-color: white;
                transition: .4s;
                border-radius: 50%;
            }
            input:checked + .slider {
                background-color: #2196F3;
            }
            input:checked + .slider:before {
                transform: translateX(26px);
            }
            .main-settings {
                background: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 25px;
                margin-bottom: 20px;
            }
            .main-settings h3 {
                color: #495057;
                margin-bottom: 15px;
                font-size: 20px;
            }
            .save-button {
                background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
            }
            .save-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 20px rgba(40, 167, 69, 0.4);
            }
            @media (max-width: 768px) {
                .settings-grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>
        
        <div class="movie-scraper-settings">
            <div class="settings-header">
                <h1>🎬 Movie Scraper Settings</h1>
                <p>Configure how the movie scraper system works and what data it automatically fills</p>
            </div>
            
            <form method="post" action="">
                <div class="main-settings">
                    <h3>🔧 Main Configuration</h3>
                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                        <label class="toggle-switch">
                            <input type="checkbox" name="use_downloaded_files" <?php checked($use_downloaded_files, 1); ?>>
                            <span class="slider"></span>
                        </label>
                        <div>
                            <strong>Use Downloaded M3U8 Files (แนะนำให้เปิด)</strong>
                            <p style="margin: 5px 0 0 0; color: #6c757d;">
                                <strong>เมื่อเปิดใช้งาน:</strong> ระบบจะดาวน์โหลดไฟล์ M3U8 master และไฟล์ resolution ทั้งหมด (480p, 720p, 1080p ฯลฯ) มาเก็บไว้ในเซิร์ฟเวอร์ของคุณ แล้วสร้างไฟล์ master ใหม่ที่ชี้ไปยังไฟล์ local<br>
                                <strong>เมื่อปิดใช้งาน:</strong> ระบบจะใช้ URL ต้นฉบับที่ได้จากการ scrape โดยตรง (ไม่ดาวน์โหลดมาเก็บ)<br>
                                <strong>โฟลเดอร์เก็บไฟล์:</strong> /wp-content/uploads/movies/post-{ID}/dubbed/ และ /post-{ID}/subbed/
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>📺 YouTube Integration</h3>
                        <p>ดึง YouTube ID มาใส่ในช่อง YouTube ID อัตโนมัติ เมื่อไม่มีข้อมูลอยู่แล้ว</p>
                        <label class="toggle-switch">
                            <input type="checkbox" name="auto_fill_youtube" <?php checked($auto_fill_youtube, 1); ?>>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="settings-card">
                        <h3>⏱️ Movie Duration</h3>
                        <p>ดึงระยะเวลาของหนังมาใส่ในช่อง Duration อัตโนมัติ เมื่อไม่มีข้อมูลอยู่แล้ว</p>
                        <label class="toggle-switch">
                            <input type="checkbox" name="auto_fill_duration" <?php checked($auto_fill_duration, 1); ?>>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="settings-card">
                        <h3>⭐ IMDB Rating</h3>
                        <p>ดึงคะแนน IMDB มาใส่ในช่อง IMDB Rating และ IMDB Taxonomy อัตโนมัติ <strong>(ซิงค์ทั้ง 2 ที่)</strong></p>
                        <label class="toggle-switch">
                            <input type="checkbox" name="auto_fill_imdb" <?php checked($auto_fill_imdb, 1); ?>>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="settings-card">
                        <h3>🖼️ Poster Download</h3>
                        <p>ดาวน์โหลดรูปโปสเตอร์ขนาดใหญ่สุดมาตั้งเป็น Featured Image อัตโนมัติ <strong>(เฉพาะเมื่อยังไม่มีรูป)</strong></p>
                        <label class="toggle-switch">
                            <input type="checkbox" name="auto_download_poster" <?php checked($auto_download_poster, 1); ?>>
                            <span class="slider"></span>
                        </label>
                    </div>
                    
                    <div class="settings-card">
                        <h3>🎥 M3U8 Auto-Fill</h3>
                        <p>เติม URL ใส่ช่อง M3U8 พากย์ไทย และ ซับไทย อัตโนมัติหลังจาก scrape เสร็จ</p>
                        <label class="toggle-switch">
                            <input type="checkbox" name="auto_fill_m3u8" <?php checked($auto_fill_m3u8, 1); ?>>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" name="submit" class="save-button">
                        💾 Save Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <?php
}